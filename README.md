## 1、技术栈

- 框架：[Umi Max](https://umijs.org/docs/max/introduce)
- 组件库：[Antd Pro](https://pro.ant.design/zh-CN) 和 [Antd](https://ant.design/index-cn)
- 图标库：[@ant-design/icons](https://ant-design.antgroup.com/components/icon-cn)
- 图表库：[Ant Design Charts](https://ant-design-charts-next.antgroup.com)
- 样式方案：[Tailwindcss](https://www.tailwindcss.cn)
- 状态共享：[点击参考](https://umijs.org/docs/max/data-flow)

## 2、其它工具

- 日期处理：[dayjs](https://dayjs.gitee.io/zh-CN/)
- 工具函数库：[lodash](https://www.lodashjs.com)
- class 条件工具：[classNames](https://github.com/JedWatson/classnames#readme)
- hooks 工具合集：[ahooks](https://ahooks.gitee.io/zh-CN/guide)

## 3、目录规范

```bash
├── config                          # 系统配置
│   ├── config.ts                   # umi配置
│   ├── config.dev.ts               # umi配置/开发环境
│   ├── config.prod.ts              # umi配置/线上环境
│   └── routes.ts                   # 路由配置
├── public                          # 不参与构建的静态资源
│   └── favicon.png                 # Favicon
├── src
│   ├── assets                      # 通用的本地静态资源
│   ├── components                  # 通用的业务组件
│   ├── pages                       # 业务页面入口
│   │   └── demo                    # 一级页面模块
│   │       └── list                # 二级页面模块
│   │           ├── index.tsx       # 二级页面的入口
│   │           ├── components      # 二级页面的通用组件
│   │           ├── services.ts     # 二级页面的接口服务
│   │           ├── types           # 二级页面的类型文件目录
│   │           └── config          # 二级页面的配置文件目录
│   ├── services                    # 通用的接口服务
│   ├── types                       # 通用的类型文件目录
│   ├── utils                       # 工具库
│   ├── global.scss                 # 全局样式
│   └── app.tsx                     # 运行时配置
├── README.md
└── package.json
```

特别说明：

- 业务模块目录采用分而治之的原则，能内聚在业务模块内的文件就内聚在业务模块文件夹，如果能公用，则抽取到最小公用目录，例如组件全局能公用则抽取到`src/components`目录，否则就放在业务模块目录
- 非必要避免修改公用目录的文件，如果要修改需要充分沟通和识别影响点
- 详细细节可以参见`/src/pages/demo`这个示例模块

## 4、编码规范

- 严格遵守 Typescript 编码规范
- 全部采用 tailwindcss 样式方案，非必要不得采用其他样式方案
- 非必要不得使用公用状态，必要的场景：1、全局状态，例如登录用户信息; 2、模块层级跨越较多场景共享状态
- 按钮触发数据提交场景必须要增加 loading 状态
- 页面合理拆分，避免大文件，增强可读性和可维护性

##

- 门店管理系统
