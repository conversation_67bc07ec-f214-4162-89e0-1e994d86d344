FROM registry.openanolis.cn/openanolis/node:16.17.1 as build

# 设置工作目录
WORKDIR /tmp

# 将源代码复制到容器中
COPY --chown=1000:1000 . .

# 构建项目并生成 html 文件
RUN yarn \
    && yarn build

# 第二个阶段: 使用轻量级的 nginx 镜像
FROM registry.openanolis.cn/openanolis/nginx:1.14.1-8.6 AS final

# 设置工作目录
WORKDIR /app

# 使用 update-rc.d 设置时区
RUN rm -rf /usr/share/nginx/html/*

COPY --from=build  /tmp/dist/ /usr/share/nginx/html
COPY --from=build  /tmp/nginx.conf /etc/nginx/nginx.conf
# 默认执行启动脚本
CMD ["nginx", "-g", "daemon off;"]
