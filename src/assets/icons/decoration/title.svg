<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>标题</title>
    <g id="装修" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="01装修-图片热区" transform="translate(-144.000000, -411.000000)" fill-rule="nonzero">
            <g id="装修/基础组件" transform="translate(24.000000, 277.000000)">
                <g id="编组" transform="translate(96.000000, 134.000000)">
                    <g id="标题" transform="translate(24.000000, 0.000000)">
                        <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="32" height="32"></rect>
                        <path d="M21,5 L21,9 L20,9 L20,6 L12,6 L12,26 L17,26 L17,27 L6,27 L6,26 L11,26 L11,6 L3,6 L3,9 L2,9 L2,5 L21,5 Z M30,13 L30,16 L29,16 L29,14 L25,14 L25,26 L27,26 L27,27 L22,27 L22,26 L24,26 L24,14 L20,14 L20,16 L19,16 L19,13 L30,13 Z" id="形状结合" fill="#0D0D0D"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>