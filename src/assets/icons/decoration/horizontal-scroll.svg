<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>3备份</title>
    <g id="专题页装修" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="3备份">
            <rect id="矩形备份" stroke-opacity="0.15" stroke="#0D0D0D" x="0.5" y="0.5" width="31" height="31" rx="1"></rect>
            <path d="M23.5,9 C23.7761424,9 24,9.22385763 24,9.5 L24,22.5 C24,22.7761424 23.7761424,23 23.5,23 C23.2238576,23 23,22.7761424 23,22.5 L23,9.5 C23,9.22385763 23.2238576,9 23.5,9 Z M13,9 C13.5522847,9 14,9.44771525 14,10 L14,20 C14,20.5522847 13.5522847,21 13,21 L9,21 C8.44771525,21 8,20.5522847 8,20 L8,10 C8,9.44771525 8.44771525,9 9,9 L13,9 Z M13,10 L9,10 L9,20 L13,20 L13,10 Z M20,9 <PERSON>20.5522847,9 21,9.44771525 21,10 L21,20 C21,20.5522847 20.5522847,21 20,21 L16,21 C15.4477153,21 15,20.5522847 15,20 L15,10 C15,9.44771525 15.4477153,9 16,9 L20,9 Z M20,10 L16,10 L16,20 L20,20 L20,10 Z M8.5,22 L13.5,22 C13.7761424,22 14,22.2238576 14,22.5 C14,22.7761424 13.7761424,23 13.5,23 L8.5,23 C8.22385763,23 8,22.7761424 8,22.5 C8,22.2238576 8.22385763,22 8.5,22 Z M15.5,22 L20.5,22 C20.7761424,22 21,22.2238576 21,22.5 C21,22.7761424 20.7761424,23 20.5,23 L15.5,23 C15.2238576,23 15,22.7761424 15,22.5 C15,22.2238576 15.2238576,22 15.5,22 Z" id="形状结合" fill-opacity="0.55" fill="#0D0D0D" fill-rule="nonzero"></path>
        </g>
    </g>
</svg>