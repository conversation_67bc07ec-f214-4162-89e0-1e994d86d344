<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>image</title>
    <g id="装修" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="01装修-图片热区" transform="translate(-144.000000, -325.000000)" fill-rule="nonzero">
            <g id="装修/基础组件" transform="translate(24.000000, 277.000000)">
                <g id="image" transform="translate(120.000000, 48.000000)">
                    <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="32" height="32"></rect>
                    <path d="M29,5 C29.5522847,5 30,5.44771525 30,6 L30,26 C30,26.5522847 29.5522847,27 29,27 L3,27 C2.44771525,27 2,26.5522847 2,26 L2,6 C2,5.44771525 2.44771525,5 3,5 L29,5 Z M20.7222891,15.6284996 L13.4209952,24.3925891 L7.90566167,17.8409996 L3,25.209 L3,26 L29,26 L29,25.36 L20.7222891,15.6284996 Z M29,6 L3,6 L3,23.619 L7.90566167,16.25 L13.4209952,22.8015895 L20.7222891,14.0375 L29,23.77 L29,6 Z M9,9 C10.1045695,9 11,9.8954305 11,11 C11,12.1045695 10.1045695,13 9,13 C7.8954305,13 7,12.1045695 7,11 C7,9.8954305 7.8954305,9 9,9 Z M9,10 C8.44771525,10 8,10.4477153 8,11 C8,11.5522847 8.44771525,12 9,12 C9.55228475,12 10,11.5522847 10,11 C10,10.4477153 9.55228475,10 9,10 Z" id="形状结合" fill="#0D0D0D"></path>
                </g>
            </g>
        </g>
    </g>
</svg>