export interface QuerySysCheckReponse {
  /**
   * 系统设置
   */
  sysProperty?: SysProperty;
}

/**
 * 系统设置
 */
export interface SysProperty {
  /**
   * configKey
   */
  configKey?: string;
  /**
   * 创建人（供大数据使用）
   */
  createPerson?: string;
  /**
   * 创建时间（供大数据使用）
   */
  createTime?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 是否删除，0未删除
   */
  isDelete?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 类型
   */
  type?: string;
  /**
   * 更新人（供大数据使用）
   */
  updatePerson?: string;
  /**
   * 更新时间（供大数据使用）
   */
  updateTime?: string;
  /**
   * value
   */
  value?: string;
}
