export interface QueryTaskResultResponse {
  /**
   * 所属模块信息
   */
  moduleInfo?: ModuleInfo;
  /**
   * 任务信息
   */
  taskInfo?: TaskInfo;
  /**
   * 任务执行结果
   */
  taskResultInfo?: TaskResultInfo;
}

/**
 * 所属模块信息
 */
export interface ModuleInfo {
  /**
   * 模块Id
   */
  moduleId?: string;
  /**
   * 模块名称
   */
  moduleName?: string;
  /**
   * 系统id
   */
  systemId?: string;
  /**
   * 系统名称
   */
  systemName?: string;
  /**
   * 任务类型1:导入2:导出
   */
  taskType?: number;
}

/**
 * 任务信息
 */
export interface TaskInfo {
  /**
   * 创建人姓名
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人id
   */
  createUserId?: string;
  /**
   * 结束时间
   */
  endTime?: string;
  /**
   * 开始时间
   */
  startTime?: string;
  /**
   * 任务状态0:未开始1:进行中2:成功3:失败
   */
  status?: number;
  /**
   * 任务描述
   */
  taskDesc?: string;
  /**
   * 主键
   */
  taskId?: number;
}

/**
 * 任务执行结果
 */
export interface TaskResultInfo {
  /**
   * 失败条目数量
   */
  failAmount?: number;
  /**
   * 任务结果信息导出成功时为下载地址,其他为说明信息
   */
  info?: string;
  /**
   * 成功条目数量
   */
  successAmount?: number;
  /**
   * 总条目数量
   */
  totalAmount?: number;
}
