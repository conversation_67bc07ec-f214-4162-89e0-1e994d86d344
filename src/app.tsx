import { getInitSystemInfo } from '@/services/systerm';
import { flattenMenuTree, flattenRouteTree } from '@/utils/formatMenuData';
import { history } from '@umijs/max';
import queryString from 'query-string';
import routes from '../config/routes';
import { clearCookies, setSessionCookie } from './access';
import { autoLoginPost } from './pages/common/login/services';

const loginPath = '/login';

/**
 * 初始化数据
 * 系统级别的数据在此获取，获取过程会阻塞系统加载
 * https://umijs.org/zh-CN/plugins/plugin-initial-state
 */
export async function getInitialState() {
  const {
    location: { search, pathname },
  } = history;

  if (search) {
    //解析是否有token 免登录
    const { token, sid } = queryString.parse(location.search);
    if (sid) {
      setSessionCookie(sid as string);
    } else if (token) {
      //不为空 则去根据token获取 sessionId
      const { code, data } = await autoLoginPost({ token });
      if (code == 0 && data && data.sSessionId) {
        // 登录成功
        setSessionCookie(data.sSessionId);
      } else {
        return;
      }
    }
  }

  const {
    currentUser,
    systemMenu,
    remoteMenuData,
    buttonItem,
    currentUserInfo,
  } = await getInitSystemInfo();
  if (currentUser?.accountId) {
    if (location.pathname == loginPath) {
      history.push('/');
    }
    localStorage.setItem('remoteMenuData', JSON.stringify(remoteMenuData));
    onRouteChange();
    return {
      currentUser,
      systemMenu,
      buttonItem,
      currentUserInfo,
    };
  }
  if (location.pathname !== loginPath) {
    clearCookies();
    history.push(loginPath);
    return {};
  }
}

export function onRouteChange() {
  // 路由权限处理
  const remoteMenuData = JSON.parse(localStorage.getItem('remoteMenuData') ?? '[]');
  const flatRemoteMenu = flattenMenuTree(remoteMenuData);
  const remoteMenuKeys = flatRemoteMenu.map((item) => item.path);
  const flatRoute = flattenRouteTree(routes);
  const whiteList = flatRoute.filter((item) => item.whitePath).map((item) => item.path);
  const currentRoute = flatRoute.find((item) => item.path === location.pathname);
  if (currentRoute?.name) {
    document.title = currentRoute.name;
  }
  if (
    USE_REMOTE_MENU &&
    !remoteMenuKeys.includes(location.pathname) &&
    !whiteList.includes(location.pathname)
  ) {
    history.push('/403');
  }
}
