import { useEffect } from 'react';

const useScript = (src: string) => {
  useEffect(() => {
    let script = document.createElement('script');
    script.src = src;
    script.async = true;

    script.onload = () => {
      console.log(`Script loaded: ${src}`);
    };

    script.onerror = () => {
      console.error(`Script load error: ${src}`);
    };

    document.body.appendChild(script);

    // 清理函数，组件卸载时移除script标签
    return () => {
      document.body.removeChild(script);
    };
  }, [src]);
};

export default useScript;
