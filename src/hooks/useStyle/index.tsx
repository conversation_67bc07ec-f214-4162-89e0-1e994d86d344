import { useEffect } from 'react';

const useStyle = (href: string) => {
  useEffect(() => {
    // 创建一个link元素
    const link = document.createElement('link');
    link.href = href;
    link.rel = 'stylesheet';

    // 将link元素插入到文档头部
    document.head.appendChild(link);

    // 在组件卸载时移除link元素，可选步骤
    return () => {
      document.head.removeChild(link);
    };
  }, [href]);
};

export default useStyle;
