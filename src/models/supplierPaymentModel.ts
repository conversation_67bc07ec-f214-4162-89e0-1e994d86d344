import type { GoodsEntity } from '@/pages/goods/list/types/GoodsEntity.entity';
import type { CommonModelForm } from '@/types/CommonModelForm';
import { useState } from 'react';

export default () => {
  /**
   * 侧边栏是否收起
   */
  const [orderSelectDrawer, setOrderSelectDrawer] = useState<
    CommonModelForm<number, GoodsEntity[]>
  >({
    visible: false,
    title: '选择订单',
  });

  return { orderSelectDrawer, setOrderSelectDrawer };
};
