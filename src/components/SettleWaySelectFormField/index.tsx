import { PayKind, payKindOptions } from '@/components/PaymentForm/types/PayKind';
import { getCstDetail } from '@/pages/customer/list/services';
import type { CustomerSaveEntity } from '@/pages/customer/list/types/CustomerSaveEntity';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { ProFormDependency, ProFormDigit } from '@ant-design/pro-components';
import { ProFormGroup, ProFormList, ProFormSelect } from '@ant-design/pro-form';
import type { FormListFieldData, FormListOperation } from 'antd';
import { ConfigProvider, Tooltip } from 'antd';
import { defaultTo } from 'lodash';
import React, { useEffect, useState } from 'react';

export interface PaymentFormProps {
  cstId?: string; // 客户ID
  storeId?: string; // 门店ID
}

const PaymentForm = (props: PaymentFormProps) => {
  const { cstId, storeId } = props;
  const [accountList, setAccountList] = useState<any[]>([]);
  const [cstDetail, setCstDetail] = useState<CustomerSaveEntity>();

  useEffect(() => {
    if (storeId) {
      queryMemberAccountPage({
        belongToStore: [storeId],
      }).then((result) => {
        if (result?.data?.length) {
          setAccountList(
            result.data.map((item) => ({
              label: item.memberAccountName,
              value: item.id.toString(),
            })),
          );
        }
      });
    }
  }, [storeId]);

  useEffect(() => {
    if (cstId) {
      getCstDetail({ cstId }).then((result) => {
        if (result) {
          setCstDetail(result);
        }
      });
    }
  }, [cstId]);

  /**
   * 自定义按钮
   * @param field
   * @param action
   * @param defaultActionDom
   * @param count
   */
  const actionRender = (
    field: FormListFieldData,
    action: FormListOperation,
    defaultActionDom: React.ReactNode[],
    count: number,
  ) => {
    const btn = [];
    switch (count) {
      case 1:
        btn.push(
          <Tooltip title="新增一行">
            <PlusOutlined className="cursor-pointer ml-2" onClick={() => action.add()} />
          </Tooltip>,
        );
        break;
      default:
        btn.push(
          <Tooltip title="删除此行">
            <DeleteOutlined
              className="cursor-pointer ml-2"
              onClick={() => action.remove(field.name)}
            />
          </Tooltip>,
        );
        break;
    }
    return btn;
  };

  return (
    <ConfigProvider theme={{ components: { Form: { itemMarginBottom: 8 } } }}>
      <ProFormGroup colProps={{ span: 8 }}>
        <ProFormSelect
          initialValue={1}
          label={<span className="font-semibold">结算方式</span>}
          name="payKind"
          options={payKindOptions(cstDetail?.settle?.credit === true)}
        />
        <ProFormDependency name={['payKind']}>
          {({ payKind }) => {
            if (payKind === PayKind.Credit) {
              return (
                <div className="text-gray-500 availableAmount">
                  剩余额度：
                  <div className="text-gray-500 availableAmount">
                    已用{defaultTo(cstDetail?.settle?.usedAmount, '-')}/可用
                    {defaultTo(cstDetail?.settle?.availableAmount, '-')}
                  </div>
                  {/* <MoneyText text={cstDetail?.settle?.availableAmount} /> */}
                </div>
              );
            }
            if (payKind === PayKind.Cash) {
              return (
                <ProFormList
                  name="payDetailList"
                  max={2}
                  min={1}
                  initialValue={[{}]}
                  creatorButtonProps={false}
                  actionRender={actionRender}
                >
                  <ProFormGroup key="group">
                    <div className="flex">
                      <ProFormSelect
                        name="payeeAcount"
                        options={accountList}
                        placeholder="请选择账户"
                        width={120}
                        rules={[REQUIRED_RULES]}
                      />
                      <ProFormDigit
                        min={0.01}
                        name="payAmount"
                        fieldProps={{
                          controls: false,
                          precision: 2,
                          addonAfter: '元',
                        }}
                        rules={[REQUIRED_RULES]}
                      />
                    </div>
                  </ProFormGroup>
                </ProFormList>
              );
            }
          }}
        </ProFormDependency>
      </ProFormGroup>
    </ConfigProvider>
  );
};

export default PaymentForm;
