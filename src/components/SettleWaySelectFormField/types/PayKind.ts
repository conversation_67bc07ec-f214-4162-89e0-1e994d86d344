export enum PayKind {
  Cash = 1, // 现款
  Credit, // 挂帐
}

export enum PayKindName {
  Cash = '现款',
  Credit = '挂帐',
}

export const payKindOptions = (canCredit: boolean) => {
  const options = [
    {
      label: PayKindName.Cash,
      value: PayKind.Cash,
    },
  ];
  if (canCredit) {
    options.push({
      label: PayKindName.Credit,
      value: PayKind.Credit,
    });
  }
  return options;
};
