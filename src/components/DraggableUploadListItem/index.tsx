import { DeleteOutlined, DownloadOutlined, EyeOutlined } from '@ant-design/icons';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useIntl } from '@umijs/max';
import { Tooltip, type UploadFile } from 'antd';
export interface DraggableUploadListItemProps {
  originNode: React.ReactElement<any, string | React.JSXElementConstructor<any>>;
  file: UploadFile;
  onPreview: (file: UploadFile) => void;
  onDownload: (file: UploadFile) => void;
  onRemove: (file: UploadFile) => void | boolean | Promise<void | boolean>;
}
export default ({
  originNode,
  file,
  onPreview,
  onDownload,
  onRemove,
}: DraggableUploadListItemProps) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: file.uid,
  });
  const intl = useIntl();

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: 'move',
    width: 102,
    height: 102,
  };

  return (
    <div className="relative z-0 flex flex-col justify-center">
      <div
        key={file.uid}
        ref={setNodeRef}
        style={style}
        // prevent preview event when drag end
        className={isDragging ? 'is-dragging' : ''}
        {...attributes}
        {...listeners}
      >
        {/* hide error tooltip when dragging */}
        {file.status === 'error' && isDragging ? originNode.props.children : originNode}
      </div>
      <div className="absolute left-0 right-0 z-50 bottom-[-24px] flex flex-row justify-around">
        <Tooltip title={intl.formatMessage({ id: 'common.button.download' })}>
          <DownloadOutlined
            onClick={() => {
              onDownload(file);
            }}
            className="cursor-pointer hover:text-[blue]"
          />
        </Tooltip>
        <Tooltip title={intl.formatMessage({ id: 'common.button.preview' })}>
          <EyeOutlined
            onClick={() => {
              onPreview(file);
            }}
            className="cursor-pointer hover:text-[blue]"
          />
        </Tooltip>
        <Tooltip title={intl.formatMessage({ id: 'common.button.delete' })}>
          <DeleteOutlined
            onClick={() => {
              onRemove(file);
            }}
            className="cursor-pointer hover:text-[red]"
          />
        </Tooltip>
      </div>
    </div>
  );
};
