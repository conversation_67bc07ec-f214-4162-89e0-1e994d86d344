import { DownOutlined } from '@ant-design/icons';
import { Popover, Space } from 'antd';

/**
 * 带下拉箭头的悬浮框列渲染
 * @param value
 * @returns
 */
const ArrayColumnRender = (value?: string[]) => {
  if (!value || value.length === 0) return '';
  return value.length == 1 ? (
    <span>{value[0]}</span>
  ) : (
    <Popover
      arrow={false}
      placement="bottom"
      content={
        <div className="max-h-[400px] overflow-auto">
          {value.map((t) => (
            <p key={t}>{t}</p>
          ))}
        </div>
      }
    >
      <Space size={10}>
        <span>{value[0]}</span>
        <DownOutlined style={{ color: '#00000073' }} />
      </Space>
    </Popover>
  );
};

/**
 * 富文本列渲染【去除所有html标签】
 * @param value
 * @returns
 */
const removeHtmlTagRegExp = /<\/?[^>]+(>|$)/g
const RichContentColumnRender = (html?: string) => {
  if (!html) return ''
  return html.replace(removeHtmlTagRegExp, '');
};
export default {
  RichContentColumnRender,
  ArrayColumnRender,
};
