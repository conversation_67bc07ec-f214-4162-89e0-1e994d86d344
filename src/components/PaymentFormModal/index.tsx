import { message, Modal } from 'antd';
import PaymentForm, { PaymentFormProps } from '@/components/PaymentForm';
import { ProForm, ProFormInstance } from '@ant-design/pro-components';
import { useEffect, useRef } from 'react';
import styles from './index.module.scss';
import classNames from 'classnames';
import { PayKind } from '@/components/PaymentForm/types/PayKind';
import { PayDetailList } from '@/pages/sales/order/edit/types/confirm.pay.request';
import MoneyText from '@/components/common/MoneyText';
import _ from 'lodash';

export interface PayValues {
  /**
   * 支付明细
   */
  payDetailList?: PayDetailList[];
  /**
   * 支付方式1现款2挂账
   */
  payKind?: PayKind;
}

export interface PaymentFormModalProps extends PaymentFormProps {
  visible: boolean;
  onClose: () => void;
  totalAmount?: number; // 结算金额
  onSubmit: (values: PayValues) => void;
  dataSource?: PayValues;
}

const PaymentFormModal = (props: PaymentFormModalProps) => {
  const ref = useRef<any>();
  const formRef = useRef<ProFormInstance>();
  const { visible, onClose, totalAmount = 0, onSubmit, dataSource, ...rest } = props;

  useEffect(() => {
    if (dataSource) {
      formRef.current?.setFieldsValue(dataSource);
    }
  }, [dataSource]);

  const handleOk = () => {
    formRef.current?.validateFields().then((result) => {
      if (result?.payKind === PayKind.Cash) {
        let total = 0;
        result?.payDetailList?.forEach((item: any) => {
          if (item?.payAmount) {
            total = _.add(total, item.payAmount);
          }
        });
        if (total != totalAmount) {
          message.error(`支付金额不等于订单应付金额`);
          return;
        }
      }
      onSubmit(result);
      onClose();
    });
  };

  const handleValues = (changedValues: any, allValues: any) => {
    switch (allValues.payKind) {
      case PayKind.Credit:
        break;
      case PayKind.Cash:
        const { accountList } = ref.current?.getState();
        if (
          !allValues?.payDetailList &&
          accountList?.length &&
          !dataSource?.payDetailList?.[0]?.payeeAcount
        )
          formRef.current?.setFieldsValue({
            payDetailList: [{ payeeAcount: accountList?.[0]?.value, payAmount: totalAmount }],
          });
        break;
    }
  };

  return (
    <Modal title="结算方式" open={visible} onCancel={onClose} onOk={handleOk}>
      <ProForm
        formRef={formRef}
        submitter={false}
        layout={'horizontal'}
        className={classNames('py-4', styles.form)}
        onValuesChange={handleValues}
      >
        <div className="mb-4">
          结算金额：
          <span className="text-[24px] text-[#F83431]">
            <MoneyText text={totalAmount} />
          </span>
        </div>
        <PaymentForm {...rest} ref={ref} />
      </ProForm>
    </Modal>
  );
};

export default PaymentFormModal;
