import { useKeyPress } from 'ahooks';
import { Image, Space } from 'antd';
import { isEmpty } from 'lodash';
import type { ReactNode } from 'react';
import { useState } from 'react';
import { simulateEscKeyPress } from '../GoodsSearch/components/GoodsList/useKeyboardEvents';
export interface ImageListProps {
  urls?: string[];
  width?: number;
  itemName?: ReactNode;
  isHighLight?: boolean;
}

export default (props: ImageListProps) => {
  const { urls = [], width = 26, itemName = '', isHighLight = false } = props;
  console.log('isHighLight', isHighLight);
  const [visible, setVisible] = useState(false);

  useKeyPress('alt.t', () => {
    if (!visible) {
      simulateEscKeyPress()
    }
    setVisible(prev => !prev);
  }, {
    events: (isHighLight && !isEmpty(urls)) ? ['keydown'] : [],
  });


  return (
    <Image.PreviewGroup items={urls} preview={{
      visible,
      onVisibleChange: (value) => {
        setVisible(value);
      },
    }}>
      <Space align="center" size={8}>
        {!isEmpty(urls) && <Image width={width} height={width} src={urls?.[0]} />}{itemName}
      </Space>
    </Image.PreviewGroup>
  );
};
