import { queryStockRecord } from '@/components/StocksInfoDrawer/services';
import type { QueryPurchaseRecordRequest } from '@/components/StocksInfoDrawer/types/query.purchase.record.request';
import type { QueryPurchaseRecordResponse } from '@/components/StocksInfoDrawer/types/query.purchase.record.response';
import FunProTable from '@/components/common/FunProTable';
import SubTitle from '@/components/common/SubTitle';
import { ProCard } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Drawer, Tag } from 'antd';
import { useEffect, useState } from 'react';

import blueIcon from './imgs/blue.svg';
import greenIcon from './imgs/green.svg';
import redIcon from './imgs/red.svg';

export interface StocksInfoDrawerProps extends QueryPurchaseRecordRequest {
  title?: string;
  visible?: boolean;
  onClose?: () => void;
  warehouseId?: string;
}

const StocksInfoDrawer = (props: StocksInfoDrawerProps) => {
  const intl = useIntl();
  const { title = intl.formatMessage({ id: 'common.stocksInfoDrawer.title' }), visible, onClose, warehouseId, ...rest } = props;
  const [detail, setDetail] = useState<QueryPurchaseRecordResponse>();

  useEffect(() => {
    if (visible) {
      queryStockRecord(rest).then((result) => {
        if (result) {
          setDetail(result);
        }
      });
    }
  }, [visible]);

  if (!detail) {
    return null;
  }

  const stocksInfo = [
    {
      label: intl.formatMessage({ id: 'common.stocksInfoDrawer.totalInventory' }),
      value: detail?.totalInventoryNum ?? 0,
      icon: blueIcon,
    },
    {
      label: intl.formatMessage({ id: 'common.stocksInfoDrawer.occupiedInventory' }),
      value: detail?.totalLockedNum ?? 0,
      icon: redIcon,
    },
    {
      label: intl.formatMessage({ id: 'common.stocksInfoDrawer.availableInventory' }),
      value: detail?.totalAvaNum ?? 0,
      icon: greenIcon,
    },
  ];

  return (
    <Drawer
      width={1080}
      open={visible}
      onClose={onClose}
      maskClosable={false}
      title={title}
      styles={{
        body: { padding: 0, background: '#f2f2f2' },
      }}
    >
      <div className="mx-[24px] mt-[24px]">
        <ProCard gutter={20}>
          {stocksInfo.map((item) => (
            <ProCard key={item.label} bordered={true}>
              <div className="flex items-center">
                <img src={item.icon} width={40} height={40} />
                <div className="ml-4">
                  <div className="text-2xl font-semibold">{item.value}</div>
                  <div className="text-gray-500">{item.label}</div>
                </div>
              </div>
            </ProCard>
          ))}
        </ProCard>
        <FunProTable
          headerTitle={<SubTitle text={intl.formatMessage({ id: 'common.stocksInfoDrawer.stockDistribution' })} />}
          search={false}
          scroll={{ x: true }}
          pagination={false}
          options={false}
          dataSource={detail?.stockInventoryRos ?? []}
          columns={[
            {
              title: intl.formatMessage({ id: 'common.stocksInfoDrawer.warehouseName' }),
              dataIndex: 'warehouseName',
              renderText: (text, record) => (
                <span>
                  {text}
                  {record.warehouseId === warehouseId ? (
                    <Tag color="red" className="ml-2">
                      {intl.formatMessage({ id: 'common.stocksInfoDrawer.selected' })}
                    </Tag>
                  ) : (
                    ''
                  )}
                </span>
              ),
            },
            {
              title: intl.formatMessage({ id: 'common.stocksInfoDrawer.totalStock' }),
              dataIndex: 'inventoryNum',
            },
            {
              title: intl.formatMessage({ id: 'common.stocksInfoDrawer.occupiedStock' }),
              dataIndex: 'lockedNum',
            },
            {
              title: intl.formatMessage({ id: 'common.stocksInfoDrawer.availableStock' }),
              dataIndex: 'avaNum',
            },
            {
              title: intl.formatMessage({ id: 'common.stocksInfoDrawer.purchaseInTransit' }),
              dataIndex: 'purchaseTransitNum',
            },
            {
              title: intl.formatMessage({ id: 'common.stocksInfoDrawer.upperLimit' }),
              dataIndex: 'upperLimit',
            },
            {
              title: intl.formatMessage({ id: 'common.stocksInfoDrawer.lowerLimit' }),
              dataIndex: 'lowerLimit',
            },
          ]}
        />
      </div>
    </Drawer>
  );
};

export default StocksInfoDrawer;
