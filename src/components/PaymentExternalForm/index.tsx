import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { PayTypeStatus, payTypeStatusOptions } from '@/pages/purchase/list/types/PayTypeStatus';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { FormListActionType, ProFormDependency, ProFormDigit } from '@ant-design/pro-components';
import { ProFormGroup, ProFormList, ProFormSelect } from '@ant-design/pro-form';
import { ConfigProvider, Flex, FormListFieldData, FormListOperation, Tooltip } from 'antd';
import { useEffect, useRef, useState } from 'react';
import styles from './index.module.scss';

export interface PaymentExternalFormProps {
  storeId?: string; // 门店ID
}

const PaymentExternalForm = (props: PaymentExternalFormProps) => {
  const { storeId } = props;
  const [accountList, setAccountList] = useState<any[]>([]);
  const actionRef = useRef<FormListActionType>();

  useEffect(() => {
    if (storeId) {
      console.log(storeId);

      queryMemberAccountPage({
        belongToStore: [storeId],
      }).then((result) => {
        if (result?.data?.length) {
          setAccountList(
            result.data.map((item) => ({
              label: item.memberAccountName,
              value: item.id.toString(),
            })),
          );
        }
      });
    }
  }, [storeId]);
  /**
   * 自定义按钮
   * @param field
   * @param action
   * @param defaultActionDom
   * @param count
   */
  const actionRender = (
    field: FormListFieldData,
    action: FormListOperation,
    defaultActionDom: React.ReactNode[],
    count: number,
  ) => {
    const btn = [];
    switch (count) {
      case 1:
        btn.push(
          <Tooltip title="新增一行">
            <PlusOutlined className="cursor-pointer ml-2" onClick={() => action.add()} />
          </Tooltip>,
        );
        break;
      default:
        btn.push(
          <Tooltip title="删除此行">
            <DeleteOutlined
              className="cursor-pointer ml-2"
              onClick={() => action.remove(field.name)}
            />
          </Tooltip>,
        );
        break;
    }
    return btn;
  };

  return (
    <ConfigProvider>
      <Flex vertical>
        <Flex vertical>
          <span className="pb-[8px] font-semibold">结算方式</span>
          <ProFormSelect
            name="payType"
            rules={[{ required: true }]}
            width={'md'}
            valueEnum={payTypeStatusOptions}
            fieldProps={{ allowClear: false }}
          />
        </Flex>
        <ProFormDependency name={['payType']}>
          {({ payType }, form) => {
            if (payType == PayTypeStatus.CASH) {
              // let initialValues = form.getFieldValue('paySubTypeList') || [];
              // //清空
              // initialValues = [];
              // if (initialValues.length === 0 && accountList.length > 0) {
              //   form.setFieldValue('paySubTypeList', [{ id: accountList[0].value }]);
              // }
              return (
                <ProFormList
                  name="paySubTypeList"
                  max={2}
                  min={1}
                  creatorButtonProps={false}
                  actionRef={actionRef}
                  initialValue={[{}]}
                  actionRender={actionRender}
                  className={styles.group}
                >
                  <ProFormGroup key="group">
                    <div className="flex">
                      <ProFormSelect
                        name="id"
                        rules={[{ required: true }]}
                        options={accountList}
                        placeholder="请选择账户"
                        width={140}
                      />
                      <ProFormDigit
                        min={0.01}
                        name="amount"
                        rules={[{ required: true }]}
                        fieldProps={{
                          controls: false,
                          precision: 2,
                          addonAfter: '元',
                        }}
                      />
                    </div>
                  </ProFormGroup>
                </ProFormList>
              );
            }
          }}
        </ProFormDependency>
      </Flex>
    </ConfigProvider>
  );
};

export default PaymentExternalForm;
