import ColumnRender from '@/components/ColumnRender';
import type { PrintGoodItem } from '@/components/GoodsPrintDrawer';
import { InputNumber } from 'antd';
import type { ColumnType } from 'rc-table';

export interface ColumnsForPurchaseProps {
  onChangePrintNumber: (itemSn: string, number: number) => void;
}

export const columnsForPurchase = (props: ColumnsForPurchaseProps) =>
  [
    {
      title: '序号',
      width: 40,
      render: (text, record, index) => index + 1,
    },
    {
      dataIndex: 'itemSn',
      title: '商品编码',
      width: 100,
    },
    {
      dataIndex: 'itemName',
      title: '商品名称',
    },
    {
      dataIndex: 'oeNos',
      title: 'OE',
      width: 100,
      render: (text) => ColumnRender.ArrayColumnRender(text ?? []),
    },
    {
      dataIndex: 'brandPartNos',
      title: '品牌件号',
      width: 100,
      render: (text) => ColumnRender.ArrayColumnRender(text ?? []),
    },
    {
      dataIndex: 'brandName',
      title: '品牌',
      width: 80,
    },
    {
      dataIndex: 'categoryName',
      title: '分类',
      width: 100,
    },
    {
      dataIndex: 'number',
      title: '采购数',
      width: 80,
    },
    {
      dataIndex: 'printNumber',
      title: '打印数',
      render: (text, record) => {
        return (
          <InputNumber
            value={text}
            min={1}
            onChange={(v) => props.onChangePrintNumber(record.itemSn, v)}
          />
        );
      },
    },
  ] as ColumnType<PrintGoodItem>[];
