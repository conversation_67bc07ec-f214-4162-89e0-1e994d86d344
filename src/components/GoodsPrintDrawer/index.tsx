import { Button, Drawer, message, Space, Table } from 'antd';
import { useEffect, useState } from 'react';
import { columnsForPurchase } from '@/components/GoodsPrintDrawer/config/columnsForPurchase';
import { queryStoreGoodsPage } from '@/components/GoodsSearch/services';
import { StoreGoodsEntity } from '@/components/GoodsSearch/components/GoodsList/types/store.goods.entity';
import _ from 'lodash';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { columnsForInventory } from '@/components/GoodsPrintDrawer/config/columnsForInventory';

export interface PrintItems {
  itemSn: string;
  number: number;
  warehouseName?: string;
}

export interface GoodsPrintDrawerProps {
  visible: boolean;
  onClose: () => void;
  bizType: 'purchase' | 'inventory';
  items: PrintItems[];
}

export interface PrintGoodItem extends StoreGoodsEntity {
  number: number;
  printNumber: number;
}

const GoodsPrintDrawer = (props: GoodsPrintDrawerProps) => {
  const { visible, onClose, bizType, items } = props;
  const [list, setList] = useState<PrintGoodItem[]>([]);
  const [checkedItemSn, setCheckedItemSn] = useState<string[]>([]);

  useEffect(() => {
    setCheckedItemSn(list.map((item) => item.itemSn));
  }, [list]);

  useEffect(() => {
    if (visible) {
      queryStoreGoodsPage({
        pageNo: 1,
        pageSize: 999,
        itemSnList: items.map((item) => item.itemSn),
      }).then((result) => {
        if (result) {
          setList(
            result.data?.map((item) => {
              const number = items.find((n) => n.itemSn === item.itemSn)?.number ?? 0;
              const warehouseName =
                items.find((n) => n.itemSn === item.itemSn)?.warehouseName ?? '';
              return (
                {
                  ...item,
                  number,
                  printNumber: number,
                  warehouseName: warehouseName,
                } ?? []
              );
            }),
          );
        }
      });
    }
  }, [visible, items]);

  const onChangePrintNumber = (itemSn: string, number: number) => {
    const newList = _.cloneDeep(list);
    newList.forEach((item) => {
      if (item.itemSn === itemSn) {
        item.printNumber = number;
      }
    });
    setList(newList);
  };

  const handlePrint = () => {
    const checkedItems = list.filter((item) => checkedItemSn.includes(item.itemSn));
    let totalRecord = 0;
    checkedItems.forEach((item) => {
      totalRecord = totalRecord + (item.printNumber ?? 0);
    });
    if (totalRecord === 0) {
      message.warning('请至少打印一张标签');
      return;
    }
    const goods = checkedItems.map((item) => ({ itemSn: item.itemSn, number: item.printNumber }));
    window.open(`/print?printType=${PrintType.goodTag}&goods=${JSON.stringify(goods)}`);
  };

  const getColumns = () => {
    switch (bizType) {
      case 'inventory':
        return columnsForInventory({ onChangePrintNumber });
      case 'purchase':
        return columnsForPurchase({ onChangePrintNumber });
    }
  };

  return (
    <Drawer
      open={visible}
      onClose={onClose}
      title="打印商品标签"
      width={1100}
      footer={
        <div className="flex justify-end">
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type={'primary'} onClick={handlePrint}>
              打印
            </Button>
          </Space>
        </div>
      }
    >
      <Table
        columns={getColumns()}
        pagination={false}
        dataSource={list}
        rowKey="itemSn"
        rowSelection={{
          selectedRowKeys: checkedItemSn,
          onChange: (selectedRowKeys) => {
            setCheckedItemSn(selectedRowKeys as string[]);
          },
        }}
      />
    </Drawer>
  );
};

export default GoodsPrintDrawer;
