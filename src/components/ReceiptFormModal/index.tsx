import { REG_LENGTH_RULE, REG_ONLY_ALPHA_AND_DIGIT_RULE, REQUIRED_RULES } from '@/utils/RuleUtils';
import type { ProFormInstance } from '@ant-design/pro-components';
import { ProForm, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { Modal } from 'antd';
import { useEffect, useRef } from 'react';

export interface ReceiptValues {
  /**
   * 发票号
   */
  receiptId?: string;
  /**
   * 备注
   */
  remark?: string;
}

export interface ReceiptFormModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (values: ReceiptValues) => void;
  dataSource?: ReceiptValues;
}

const ReceiptFormModal = (props: ReceiptFormModalProps) => {
  const formRef = useRef<ProFormInstance>();
  const { visible, onClose, onSubmit, dataSource, } = props;

  useEffect(() => {
    if (dataSource) {
      formRef.current?.setFieldsValue(dataSource);
    }
  }, [dataSource]);

  const handleOk = () => {
    formRef.current?.validateFields().then((result) => {
      onSubmit(result);
      onClose();
    });
  };

  return (
    <Modal title="新增开票" open={visible} onCancel={onClose} onOk={handleOk}>
      <ProForm
        formRef={formRef}
        submitter={false}
        layout={'horizontal'}
      >
        <div className="p-4">
          <ProFormText
            rules={[REQUIRED_RULES, REG_ONLY_ALPHA_AND_DIGIT_RULE, REG_LENGTH_RULE]}
            name="receiptId"
            label="发票号"
            labelCol={{ span: 4 }}
            colProps={{ span: 24 }}
          />
          <ProFormTextArea
            name="remark"
            label="备注"
            placeholder="请输入，最多100个字符"
            fieldProps={{ count: { max: 100, show: true }, maxLength: 100 }}
            rules={[{ max: 100 }]}
            labelCol={{ span: 4 }}
            colProps={{ span: 24 }}
          />
        </div>
      </ProForm>
    </Modal>
  );
};

export default ReceiptFormModal;
