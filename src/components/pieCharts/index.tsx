import type { PieType } from '@/pages/report/sales/types/ChartsType';
import type { PieConfig } from '@ant-design/charts';
import { Pie } from '@ant-design/charts';
export interface PieChartsProps {
  data?: PieType[];
}
const PieCharts = (props: PieChartsProps) => {
  const config: PieConfig = {
    data: props.data || [],
    angleField: 'value',
    colorField: 'type',
    innerRadius: 0.6,
    padding: 80,
    label: {
      text: (d) => `${d.type ?? ''}\n ${d.value} ${(d.percent * 100).toFixed(2)}%`,
      position: 'outside',
      style: {
        fontWeight: 'bold',
      },
      transform: [
        {
          type: 'overlapHide',
        },
      ],
    },
    tooltip: (d) => ({ value: `${d.type ?? ''} - ${d.value}- ${(d.percent * 100).toFixed(2)}%` }),
    legend: false,
  };
  return <Pie {...config} />;
};

export default PieCharts;
