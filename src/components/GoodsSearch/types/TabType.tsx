import GoodsList from '@/components/GoodsSearch/components/GoodsList';
import type { Agg } from '@/components/GoodsSearch/components/GoodsList/types/Agg';
import type { QueryGoodsMultiRequest } from '@/components/GoodsSearch/components/GoodsList/types/query.goods.multi.request';
import SalesSlip from '../components/search/SalesSlip';
import Synthesis from '../components/search/Synthesis';
import { GoodsSearchBizType } from './BizType';

export enum GoodsSearchTabType {
  Synthesis = 'Synthesis',
  VIN = 'VIN',
  SalesSlip = 'SalesSlip',
}

export enum GoodsSearchTabTypeName {
  Synthesis = 'goods.search.tab.synthesis',
  VIN = 'goods.search.tab.vin',
  SalesSlip = 'goods.search.tab.salesSlip',
}

export interface TabOptions extends CurrentBizTabOptionsProps {
  tabKey: GoodsSearchTabType;
  intl: any;
}

const tabOptions = (props: TabOptions) => {
  const { handleSearch, agg, setAgg, intl, ...rest } = props;
  switch (props.tabKey) {
    case GoodsSearchTabType.Synthesis:
      return {
        label: intl.formatMessage({ id: GoodsSearchTabTypeName.Synthesis }),
        key: GoodsSearchTabType.Synthesis,
        destroyInactiveTabPane: true,
        children: (
          <>
            <Synthesis onSearch={handleSearch} bizType={rest.bizType} />
            <GoodsList {...rest} />
          </>
        ),
      };
    // case GoodsSearchTabType.VIN:
    //   return {
    //     label: intl.formatMessage({ id: GoodsSearchTabTypeName.VIN }),
    //     key: GoodsSearchTabType.VIN,
    //     destroyInactiveTabPane: true,
    //     children: (
    //       <>
    //         <VIN onSearch={handleSearch} agg={agg} bizType={rest.bizType} />
    //         <GoodsList {...rest} setAgg={setAgg} />
    //       </>
    //     ),
    //   };
    case GoodsSearchTabType.SalesSlip:
      return {
        label: intl.formatMessage({ id: GoodsSearchTabTypeName.SalesSlip }),
        key: GoodsSearchTabType.SalesSlip,
        destroyInactiveTabPane: true,
        children: (
          <>
            <SalesSlip onSearch={handleSearch} />
            <GoodsList {...rest} />
          </>
        ),
      };
    default:
      return {
        label: intl.formatMessage({ id: GoodsSearchTabTypeName.Synthesis }),
        key: GoodsSearchTabType.Synthesis,
        destroyInactiveTabPane: true,
        children: (
          <>
            <Synthesis onSearch={handleSearch} bizType={rest.bizType} />
            <GoodsList {...rest} />
          </>
        ),
      };
  }
};

export interface CurrentBizTabOptionsProps {
  bizType: GoodsSearchBizType;
  handleSearch: (formData: QueryGoodsMultiRequest) => void;
  formData?: QueryGoodsMultiRequest;
  cstId?: string;
  storeId?: string;
  onAdd: (itemList: []) => void;
  addedItemSns?: string[];
  activeTabKey: GoodsSearchTabType;
  agg?: Agg;
  setAgg: (agg: Agg) => void;
  warehouseId?: string;
  supplierId?: string;
  intl: any;
}

export const currentBizTabOptions = (props: CurrentBizTabOptionsProps) => {
  const { bizType } = props;
  switch (bizType) {
    case GoodsSearchBizType.Sales:
      return [
        tabOptions({ tabKey: GoodsSearchTabType.Synthesis, ...props }),
      ];
    case GoodsSearchBizType.PlatformPurchase:
      return [
        tabOptions({ tabKey: GoodsSearchTabType.Synthesis, ...props }),
        tabOptions({ tabKey: GoodsSearchTabType.SalesSlip, ...props }),
      ];
    case GoodsSearchBizType.ExternalPurchase:
      return [
        tabOptions({ tabKey: GoodsSearchTabType.Synthesis, ...props }),
        tabOptions({ tabKey: GoodsSearchTabType.SalesSlip, ...props }),
      ];
    case GoodsSearchBizType.ExternalPurchaseReturn:
    case GoodsSearchBizType.SalesReturn:
      return [tabOptions({ tabKey: GoodsSearchTabType.Synthesis, ...props })];
  }
};
