import type { CarModelByVinCode } from '@/components/GoodsSearch/types/CarModelByVinCode';
import type { VinHistory } from '@/components/GoodsSearch/types/VinHistory';
import type { InsertVinHistoryRequest } from '@/components/GoodsSearch/types/insert.vin.history.request';
import type { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { OEGoodsEntity } from './components/GoodsList/types/OE.goods.entity';
import type { CloudGoodsEntity } from './components/GoodsList/types/cloud.goods.entity';
import type { PurchaseGoodsEntity } from './components/GoodsList/types/purchase.goods.entity';
import type { QueryOEGoodsPageRequest } from './components/GoodsList/types/query.OE.goods.page.request';
import type { QueryCloudGoodsPageRequest } from './components/GoodsList/types/query.cloud.goods.page.request';
import type { QueryPurchaseGoodsPageRequest } from './components/GoodsList/types/query.purchase.goods.page.request';
import type { QueryStoreGoodsPageRequest } from './components/GoodsList/types/query.store.goods.page.request';
import type { StoreGoodsEntity } from './components/GoodsList/types/store.goods.entity';
import type { GetCarModelByVinCodeRequest } from './types/get.car.model.by.vin.code.request';

/**
 * 商品分页查询
 * @param params
 * @returns
 */
export const queryStoreGoodsPage = async (params: QueryStoreGoodsPageRequest) => {
  return request<PageResponseDataType<StoreGoodsEntity>>(`/ipmsconsole/goods/member/query`, {
    data: params,
  });
};

/**
 * 销售单查询
 * @param params
 * @returns
 */
export const queryOrderGoodsForPurchase = async (params: QueryPurchaseGoodsPageRequest) => {
  return request<PageResponseDataType<PurchaseGoodsEntity>>(
    `/ipmssale/pageQueryOrderGoodsForPurchase`,
    {
      data: params,
    },
  );
};

/**
 * 通过VIN码查询车型
 */
export const getCarModelByVinCode = async (params: GetCarModelByVinCodeRequest) => {
  return request<CarModelByVinCode[]>(`/ipmsgoods/vin/getCarModelByVinCode`, {
    data: params,
  });
};

/**
 * 通过图片解析VIN码
 */
export const getVinCodeByPic = async (vinPicUrl: string) => {
  return request<string>(`/ipmsgoods/vin/getVinCodeByPic`, {
    data: { vinPicUrl },
  });
};

/**
 * 插入VIN查询历史
 */
export const insertVinHistory = async (params: InsertVinHistoryRequest) => {
  return request<boolean>(`/ipmsgoods/vin/insertVinHistory`, {
    data: params,
  });
};

/**
 * 查询VIN查询历史
 */
export const queryVinHistory = async (params: InsertVinHistoryRequest) => {
  return request<VinHistory[]>(`/ipmsgoods/vin/queryVinHistory`, {
    data: params,
  });
};

/**
 * 删除VIN查询记录
 */
export const cleanVinHistory = async (params: InsertVinHistoryRequest) => {
  return request<boolean>(`/ipmsgoods/vin/cleanVinHistory`, {
    data: params,
  });
};
