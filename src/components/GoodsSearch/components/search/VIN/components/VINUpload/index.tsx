import { Alert, Button, Input, message, Modal, Spin, Upload } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { getVinCodeByPic } from '@/components/GoodsSearch/services';

export interface VINUploadProps {
  visible: boolean;
  onClose: () => void;
  onOk: (value: string) => void;
}

const VINUpload = (props: VINUploadProps) => {
  const { visible, onOk, onClose } = props;
  const [value, setValue] = useState<string>();
  const [imgUrl, setImgUrl] = useState<string>();
  const [loading, setLoading] = useState(false);

  const handleReset = () => {
    setImgUrl(undefined);
    setValue(undefined);
  };

  const handleClose = () => {
    onClose();
    handleReset();
  };

  /**
   * 通过图片地址解析VIN码
   */
  useEffect(() => {
    if (imgUrl) {
      setLoading(true);
      getVinCodeByPic(imgUrl)
        .then((result) => {
          setValue(result);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [imgUrl]);

  const handleOk = () => {
    if (value) {
      onOk(value);
      onClose();
    } else {
      message.warning('请上传图片解析VIN码');
    }
  };

  return (
    <Modal title="上传图片" open={visible} destroyOnClose onCancel={handleClose} onOk={handleOk}>
      <Spin spinning={loading}>
        <Alert
          banner
          type={'error'}
          className="my-4 px-7 text-sm -mx-[24px]"
          message={
            <div className="leading-6">
              <div>
                1.支持<span className="text-red-600">行驶证、前风挡玻璃、机动车铭牌</span>
                等全厂家识别，支持整图或局部图识别
              </div>
              <div>
                2.VIN码图片<span className="text-red-600">需拍正、拍清楚、避免反光</span>
              </div>
            </div>
          }
          showIcon={false}
        />
        {imgUrl ? (
          <div
            className="h-[147px] bg-contain bg-no-repeat bg-center"
            style={{ backgroundImage: `url(${imgUrl})`, backgroundColor: '#f5f5f5' }}
          ></div>
        ) : (
          <Upload.Dragger
            name="file"
            multiple={false}
            accept=".jpg,.png,.jpeg,.gif"
            action="/apigateway/public/upload/object/batch"
            onChange={(info) => {
              const url = info.file?.response?.data?.[0];
              if (url) {
                setImgUrl(url);
              }
            }}
          >
            <p className="ant-upload-drag-icon">
              <PlusOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此处上传</p>
            <p className="ant-upload-hint">仅支持jpg、jpeg、png图片格式</p>
          </Upload.Dragger>
        )}
        {value && (
          <div className="flex mt-4 items-center">
            <span>解析结果：</span>
            <Input disabled={true} value={value} className="flex-1" />
            <Button type="link" onClick={handleReset}>
              重新上传
            </Button>
          </div>
        )}
      </Spin>
    </Modal>
  );
};

export default VINUpload;
