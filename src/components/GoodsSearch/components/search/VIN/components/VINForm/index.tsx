import VINUpload from '../VINUpload';
import { ReactNode, useEffect, useRef, useState } from 'react';
import { ProFormInstance, QueryFilter } from '@ant-design/pro-components';
import { DeleteOutlined, PictureOutlined } from '@ant-design/icons';
import {
  cleanVinHistory,
  getCarModelByVinCode,
  insertVinHistory,
  queryVinHistory,
} from '@/components/GoodsSearch/services';
import { CarModelByVinCode } from '@/components/GoodsSearch/types/CarModelByVinCode';
import { AutoComplete, Button, Form, Tooltip } from 'antd';
import { DefaultOptionType } from 'antd/es/select';

export interface VINFormProps {
  setVehicleList: (data: CarModelByVinCode[]) => void;
  vinCodeRef: any;
}

const VINForm = (props: VINFormProps) => {
  const formRef = useRef<ProFormInstance>();
  const { setVehicleList, vinCodeRef } = props;
  const [isVINUploadVisible, setIsVINUploadVisible] = useState(false);
  const [historyList, setHistoryList] = useState<DefaultOptionType[]>([]);

  /**
   * VIN查询
   */
  const handleQueryVehicleList = async (formData: any): Promise<boolean | void> => {
    getCarModelByVinCode(formData).then((result) => {
      if (result) {
        setVehicleList(result);
        vinCodeRef.current = formData.vinCode;
        insertVinHistory({ vinCode: formData.vinCode, source: 0 }).then((result) => {
          if (result) {
            queryHistory();
          }
        });
      }
    });
  };

  /**
   * 删除历史记录
   */
  const handleRemove = (e: React.MouseEvent, vinCode: string) => {
    e.stopPropagation();
    cleanVinHistory({ vinCode, source: 0 }).then((result) => {
      if (result) {
        queryHistory();
      }
    });
  };

  /**
   * 删除所有历史记录
   */
  const handleRemoveAll = () => {
    cleanVinHistory({ source: 0 }).then((result) => {
      if (result) {
        queryHistory();
      }
    });
  };

  /**
   * VIN查询历史查询
   */
  const queryHistory = () => {
    queryVinHistory({ source: 0 }).then((result) => {
      if (result) {
        setHistoryList(
          result.map((item) => ({
            label: (
              <div className="flex justify-between">
                <span>{item.vinCode}</span>
                <Tooltip title="删除此条记录">
                  <DeleteOutlined onClick={(e) => handleRemove(e, item.vinCode!)} />
                </Tooltip>
              </div>
            ),
            value: item.vinCode,
          })),
        );
      }
    });
  };

  useEffect(() => {
    queryHistory();
  }, []);

  return (
    <>
      <QueryFilter
        formRef={formRef}
        className="!p-0"
        onFinish={handleQueryVehicleList}
        submitter={false}
        requiredMark={false}
      >
        <Form.Item
          label="VIN码"
          name="vinCode"
          rules={[
            {
              required: true,
              message: '请输入或者点击右侧图标上传图片！',
            },
          ]}
        >
          <AutoComplete
            placeholder="请输入或者点击右侧图标上传图片"
            options={historyList}
            dropdownRender={(menus: ReactNode) => (
              <div>
                {menus}
                <div className="m-[10px] cursor-pointer text-right">
                  <a className="text-gray-500" onClick={handleRemoveAll}>
                    全部删除
                  </a>
                </div>
              </div>
            )}
            suffixIcon={
              <PictureOutlined
                className="cursor-pointer"
                title="上传包含VIN码的图片"
                onClick={() => setIsVINUploadVisible(true)}
              />
            }
          />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit">
            解析
          </Button>
        </Form.Item>
      </QueryFilter>
      <VINUpload
        visible={isVINUploadVisible}
        onClose={() => setIsVINUploadVisible(false)}
        onOk={(value) => {
          formRef.current?.setFieldValue('vinCode', value);
        }}
      />
    </>
  );
};

export default VINForm;
