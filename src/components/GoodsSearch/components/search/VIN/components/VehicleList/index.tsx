import { message, Modal } from 'antd';
import { useEffect, useState } from 'react';
import { CheckCard } from '@ant-design/pro-components';
import { CarModelByVinCode } from '../../../../../types/CarModelByVinCode';
import CarModelTitle from '@/components/GoodsSearch/components/search/VIN/components/CarModelTitle';

export interface VehicleListProps {
  vehicleList?: CarModelByVinCode[];
  modelId?: string;
  setModelId: (id: string) => void;
  visible: boolean;
  onClose: () => void;
}

const VehicleList = (props: VehicleListProps) => {
  const { visible, onClose, vehicleList, modelId, setModelId } = props;
  const [localCurrentId, setLocalCurrentId] = useState<string>();

  useEffect(() => {
    if (visible && modelId) {
      setLocalCurrentId(modelId);
    }
  }, [modelId, visible]);

  useEffect(() => {
    if (!visible) {
      setLocalCurrentId(undefined);
    }
  }, [visible]);

  const handleOK = () => {
    if (localCurrentId) {
      setModelId(localCurrentId);
      onClose();
    } else {
      message.warning('请选择车型');
    }
  };

  return (
    <Modal title="选择车型" open={visible} onCancel={onClose} onOk={handleOK}>
      <div className="max-h-[400px] overflow-auto">
        <CheckCard.Group
          onChange={(value) => {
            setLocalCurrentId(value as string);
          }}
          value={localCurrentId}
        >
          {vehicleList?.map((item) => (
            <CheckCard
              key={item.modelId}
              style={{ width: '100%' }}
              title={<CarModelTitle item={item} />}
              value={item.modelId}
            />
          ))}
        </CheckCard.Group>
      </div>
    </Modal>
  );
};

export default VehicleList;
