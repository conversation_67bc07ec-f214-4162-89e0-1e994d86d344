import { useRef, useState } from 'react';
import VINForm from './components/VINForm';
import VehicleForm from './components/VehicleForm';
import { CarModelByVinCode } from '../../../types/CarModelByVinCode';
import { QueryGoodsMultiRequest } from '@/components/GoodsSearch/components/GoodsList/types/query.goods.multi.request';
import { Agg } from '@/components/GoodsSearch/components/GoodsList/types/Agg';
import { GoodsSearchBizType } from '@/components/GoodsSearch/types/BizType';
import { useModel } from '@@/exports';
import { Result } from 'antd';

export interface VINProps {
  onSearch: (value: QueryGoodsMultiRequest) => void;
  agg?: Agg;
  bizType: GoodsSearchBizType;
}

const VIN = (props: VINProps) => {
  const { onSearch, agg, bizType } = props;
  const [vehicleList, setVehicleList] = useState<CarModelByVinCode[]>([]);
  const { initialState } = useModel('@@initialState');
  // 神策埋点需要
  const vinCodeRef = useRef<string>();

  if (!initialState?.currentUser?.etcMemberId || !initialState?.currentUser?.etcAccountId) {
    return <Result title="请先绑定一体系账号" status={403} />;
  }

  const handleSearch = (values: any) => {
    onSearch({ ...values, vin: vinCodeRef.current });
  };

  return (
    <>
      <VINForm setVehicleList={setVehicleList} vinCodeRef={vinCodeRef} />
      <VehicleForm vehicleList={vehicleList} bizType={bizType} onSearch={handleSearch} agg={agg} />
    </>
  );
};

export default VIN;
