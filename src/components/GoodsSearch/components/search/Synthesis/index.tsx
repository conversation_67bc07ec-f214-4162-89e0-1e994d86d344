import { GoodsSearchBizType } from '@/components/GoodsSearch/types/BizType';
import { queryGoodsPropertyPage } from '@/pages/goods/property/services';
import { flattenCategory, transformCategoryTree } from '@/utils/transformCategoryTree';
import {
  ProFormSelect,
  ProFormText,
  ProFormTreeSelect,
  QueryFilter,
} from '@ant-design/pro-components';
import { RequestOptionsType } from '@ant-design/pro-utils/es/typing';
import { useIntl } from '@umijs/max';
import { Button, Form, Space } from 'antd';
import { useRef } from 'react';
import type { QueryGoodsMultiRequest } from '../../GoodsList/types/query.goods.multi.request';

export interface SynthesisProps {
  onSearch: (value: QueryGoodsMultiRequest) => void;
  bizType: GoodsSearchBizType;
}

const Synthesis = (props: SynthesisProps) => {
  const { onSearch, bizType } = props;
  const intl = useIntl();
  // 神策上报需要传递Name
  const brandList = useRef<RequestOptionsType[]>([]);
  const categoryList = useRef<RequestOptionsType[]>([]);

  const handleSearch = (values: any) => {
    const data = { ...values };

    // 只给神策用
    if (data.brandIdList?.length) {
      data.brandIdAndNameList = brandList.current
        .filter((item) => data.brandIdList.includes(item.value))
        .map((item) => ({
          id: item.value,
          name: item.label,
        }));
    }

    // 只给神策用
    if (data.categoryIdList?.length) {
      data.categoryIdAndNameList = categoryList.current
        .filter((item) => data.categoryIdList.includes(item.value))
        .map((item) => ({
          id: item.value,
          name: item.label,
        }));
    }
    onSearch(data);
  };

  return (
    <QueryFilter onFinish={handleSearch} submitter={false} className="!p-0" defaultColsNumber={4}>
      <ProFormText
        label={intl.formatMessage({ id: 'goods.search.form.goodsInfo' })}
        name="queryKeyWord"
        placeholder={intl.formatMessage({ id: 'goods.search.form.goodsInfoPlaceholder' })}
      />
      <ProFormSelect
        label={intl.formatMessage({ id: 'goods.search.form.brand' })}
        name="brandIdList"
        showSearch={true}
        debounceTime={300}
        mode={'multiple'}
        fieldProps={{
          filterOption: false,
          maxTagCount: 3,
          optionRender: (option) => (
            <Space>
              {option.data.label}
            </Space>
          ),
        }}
        request={async (query) => {
          const params: any = {
            brandStatus: '1',
            pageSize: 99,
            pageNo: 1,
            brandName: query.keyWords,
          };
          if (bizType === GoodsSearchBizType.PlatformPurchase) {
            params.dataType = 0; // 平台采购查询标准商品
          }
          const result = await queryGoodsPropertyPage(params, 'brand').then((result) =>
            result.data?.map((item) => ({
              label: item.brandName,
              dataType: item.dataType,
              value: item.brandId,
            })),
          );
          brandList.current = result;
          return result;
        }}
      />
      <ProFormTreeSelect
        label={intl.formatMessage({ id: 'goods.search.form.category' })}
        name="categoryIdList"
        fieldProps={{
          maxTagCount: 3,
          treeCheckable: true,
          filterTreeNode: (text, treeNode) => treeNode.text?.includes(text),
        }}
        request={async () => {
          const params: any = {
            categoryStatus: 1,
            pageSize: 999,
            pageNo: 1,
            isReturnTree: true,
          };
          if (bizType === GoodsSearchBizType.PlatformPurchase) {
            params.dataType = 0; // 平台采购查询标准商品
          }
          const result = (await queryGoodsPropertyPage(params, 'category')) ?? [];
          categoryList.current = flattenCategory(result.data);
          return transformCategoryTree(result.data);
        }}
      />
      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit">
            {intl.formatMessage({ id: 'goods.search.button.query' })}
          </Button>
          <Button type="primary" ghost htmlType="reset">
            {intl.formatMessage({ id: 'goods.search.button.reset' })}
          </Button>
        </Space>
      </Form.Item>
    </QueryFilter>
  );
};

export default Synthesis;
