export interface PurchaseGoodsEntity {
  id?: string;
  /**
     * 本地库存
     */
  avaNum?: number;
  /**
   * 品牌名称
   */
  brandName?: string;
  /**
   * 品牌件号
   */
  brandPartNo?: string[];
  /**
   * etc有无活动：0-无活动1-有活动
   */
  etcHasActivity?: number;
  /**
   * 一体系库存
   */
  etcHasInventory?: number;
  /**
   * 是否套装：0-否1-是
   */
  isSuit?: number;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 库存下限
   */
  lowerLimit?: number;
  /**
   * oem码
   */
  oeNo?: string[];
  /**
   * 创建时间
   */
  orderCreateTime?: string;
  /**
   * 订单id
   */
  orderId?: string;
  /**
   * 销售单号
   */
  orderNo?: string;
  /**
   * 采购价
   */
  purchasePrice?: number;
  /**
   * 购买数量
   */
  saleNum?: number;
  /**
   * SKU商品名称
   */
  skuName?: string;
  /**
   * 销售门店
   */
  storeId?: string;
  /**
   * 门店名称
   */
  storeName?: string;
  /**
   * 单位
   */
  unitName?: string;
  /**
   * 库存上限
   */
  upperLimit?: number;
  /**
   * 发货仓库
   */
  warehouseId?: string;
  /**
   * 发货仓库
   */
  warehouseName?: string;
}
