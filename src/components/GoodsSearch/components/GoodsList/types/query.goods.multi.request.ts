import type { QueryOEGoodsPageRequest } from './query.OE.goods.page.request';
import type { QueryCloudGoodsPageRequest } from './query.cloud.goods.page.request';
import type { QueryPurchaseGoodsPageRequest } from './query.purchase.goods.page.request';
import type { QueryStoreGoodsPageRequest } from './query.store.goods.page.request';

export type QueryGoodsMultiRequest =
  | QueryCloudGoodsPageRequest
  | QueryStoreGoodsPageRequest
  | QueryOEGoodsPageRequest
  | QueryPurchaseGoodsPageRequest;
