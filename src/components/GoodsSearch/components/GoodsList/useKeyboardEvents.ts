import { useKeyPress } from 'ahooks';
import { useEffect, useState } from 'react';
import { GoodsMultiEntity } from './types/goods.multi.entity';

interface UseKeyboardEventsProps {
  dataSourceCache: GoodsMultiEntity[];
  enable: boolean;
  handleViewStocksInfo: (params: any) => void;
  handleViewPriceInfo: (params: any) => void;
  setCreateModalProps: (params: any) => void;
  createModalProps: any;
  stocksDrawer: any;
  priceDrawer: any;
}

export const simulateEscKeyPress = () => {
  const targetElement = document.querySelectorAll('.ant-drawer-open');
  const targetElement2 = document.querySelectorAll('.ant-image-preview-wrap');
  console.log('simulateEscKeyPress', targetElement)
  const escKeyEvent = new KeyboardEvent('keydown', {
    key: 'Escape',
    keyCode: 27,
    code: 'Escape',
    which: 27,
    bubbles: true,
  });
  if (targetElement) {
    targetElement.forEach((ele) => {
      ele.dispatchEvent(escKeyEvent);
    })
  }
  if (targetElement2) {
    targetElement2.forEach((ele) => {
      ele.dispatchEvent(escKeyEvent);
    })
  }
};

export const useKeyboardEvents = ({
  dataSourceCache,
  enable = false,
  handleViewStocksInfo,
  handleViewPriceInfo,
  setCreateModalProps,
  createModalProps,
  stocksDrawer,
  priceDrawer
}: UseKeyboardEventsProps) => {

  const [hightLightRowIndex, setHightLightRowIndex] = useState(0);
  const hightLightRow = dataSourceCache?.[hightLightRowIndex];


  useEffect(() => {
    if (dataSourceCache?.length > 0) {
      setHightLightRowIndex(0);
    }
  }, [dataSourceCache]);


  const handleUpArrow = (e) => {
    // 防止在输入框中触发这些快捷键
    if (e.target instanceof HTMLInputElement ||
      e.target instanceof HTMLTextAreaElement ||
      e.target instanceof HTMLSelectElement) {
      return;
    }
    setHightLightRowIndex((prevIndex) => {
      let newIndex = prevIndex;
      newIndex = prevIndex > 0 ? prevIndex - 1 : dataSourceCache?.length - 1;
      return newIndex;
    });
  }


  const handleDownArrow = (e) => {
    // 防止在输入框中触发这些快捷键
    if (e.target instanceof HTMLInputElement ||
      e.target instanceof HTMLTextAreaElement ||
      e.target instanceof HTMLSelectElement) {
      return;
    }
    setHightLightRowIndex((prevIndex) => {
      let newIndex = prevIndex;
      newIndex = prevIndex < dataSourceCache?.length - 1 ? prevIndex + 1 : 0;
      return newIndex;
    });
  }

  const handleAltB = () => {
    if (!stocksDrawer.visible) {
      simulateEscKeyPress()
    }
    handleViewStocksInfo({
      itemIdList: [hightLightRow?.itemId],
    });
  }

  const handleAltJ = () => {
    if (!(priceDrawer.visible && priceDrawer.defaultActiveTabKey === 'price')) {
      simulateEscKeyPress()
    }
    handleViewPriceInfo({
      itemId: hightLightRow?.itemId,
      itemSn: hightLightRow?.itemSn,
      itemName: hightLightRow?.itemName,
      suggestPrice: hightLightRow?.suggestPrice,
      lowPrice: hightLightRow?.lowPrice,
      costPrice: hightLightRow?.costPrice,
      defaultActiveTabKey: 'price',
    });
  }

  const handleAltC = () => {
    if (!(priceDrawer.visible && priceDrawer.defaultActiveTabKey === 'purchase')) {
      simulateEscKeyPress()
    }
    handleViewPriceInfo({
      itemId: hightLightRow?.itemId,
      itemSn: hightLightRow?.itemSn,
      itemName: hightLightRow?.itemName,
      suggestPrice: hightLightRow?.suggestPrice,
      lowPrice: hightLightRow?.lowPrice,
      costPrice: hightLightRow?.costPrice,
      defaultActiveTabKey: 'purchase',
    });
  }

  const handleAltS = () => {
    if (!createModalProps.visible) {
      simulateEscKeyPress()
    }
    setCreateModalProps((preModalProps) => ({
      ...preModalProps,
      readOnly: false,
      recordId: hightLightRow?.itemId,
      title: '商品编辑',
      visible: !preModalProps.visible,
    }));
  }

  useKeyPress('uparrow', handleUpArrow, { events: enable && dataSourceCache?.length > 1 ? ['keydown'] : [] });
  useKeyPress('downarrow', handleDownArrow, { events: enable && dataSourceCache?.length > 1 ? ['keydown'] : [] });
  useKeyPress('alt.b', handleAltB, { events: enable ? ['keydown'] : [] });
  useKeyPress('alt.j', handleAltJ, { events: enable ? ['keydown'] : [] });
  useKeyPress('alt.c', handleAltC, { events: enable ? ['keydown'] : [] });
  useKeyPress('alt.s', handleAltS, { events: enable ? ['keydown'] : [] });


  return {
    hightLightRowIndex,
  };
};
