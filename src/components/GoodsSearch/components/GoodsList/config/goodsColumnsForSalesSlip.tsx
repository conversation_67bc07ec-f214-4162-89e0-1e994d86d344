import ColumnRender from '@/components/ColumnRender';
import { requiredProps } from '@/types/validateRules';
import type { ProColumns } from '@ant-design/pro-components';
import { Button, InputNumber } from 'antd';
import type { GoodsMultiEntity } from '../types/goods.multi.entity';

import type { PriceInfoDrawerProps } from '@/components/PriceInfoDrawer';
import type { StocksInfoDrawerProps } from '@/components/StocksInfoDrawer';
import MoneyText from '@/components/common/MoneyText';
import type { PurchaseGoodsEntity } from '../types/purchase.goods.entity';

export interface GoodsColumnsForSalesSlipProps {
  /**
   * 已选商品ID集合
   */
  addedItemSns: string[];
  /**
   * 添加商品事件
   * @param itemList
   */
  handleAdd: (itemList: any[]) => void;
  /**
   * 查看库存
   */
  handleViewStocksInfo: (data: StocksInfoDrawerProps) => void;

  /**
   * 查看价格
   */
  handleViewPriceInfo: (data: PriceInfoDrawerProps) => void;
}

export const goodsColumnsForSalesSlip = (props: GoodsColumnsForSalesSlipProps) =>
  [
    {
      title: '序号',
      valueType: 'index',
      fixed: 'left',
      width: 40,
      editable: false,
    },
    {
      title: '销售单号',
      dataIndex: 'orderNo',
      width: 160,
      editable: false,
    },
    {
      title: '创建时间',
      dataIndex: 'orderCreateTime',
      width: 140,
      editable: false,
    },
    {
      title: '销售门店',
      dataIndex: 'storeName',
      width: 120,
      editable: false,
    },
    {
      title: '发货仓库',
      dataIndex: 'warehouseName',
      width: 120,
      editable: false,
    },
    {
      title: '商品编码',
      dataIndex: 'itemSn',
      width: 140,
      editable: false,
    },
    {
      title: '商品名称',
      dataIndex: 'itemName',
      width: 180,
      editable: false,
    },
    {
      title: 'OE',
      dataIndex: 'oeNos',
      width: 140,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: '品牌件号',
      dataIndex: 'brandPartNos',
      width: 100,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: '品牌',
      dataIndex: 'brandName',
      width: 120,
      editable: false,
    },
    {
      title: '分类',
      dataIndex: 'categoryName',
      width: 100,
      editable: false,
    },
    {
      title: '单位',
      dataIndex: 'unitName',
      width: 50,
      editable: false,
    },
    {
      title: '销售数量',
      dataIndex: 'saleNum',
      width: 80,
      editable: false,
    },
    {
      title: '本地库存',
      dataIndex: 'avaNum',
      width: 60,
      editable: false,
      render: (text, record: PurchaseGoodsEntity) => (
        <a
          className="cursor-pointer"
          onClick={() => {
            props.handleViewStocksInfo({
              itemIdList: [record.itemId!],
            });
          }}
        >
          {record.avaNum}
        </a>
      ),
    },
    {
      title: '库存下限',
      dataIndex: 'lowerLimit',
      width: 120,
      editable: false,
    },
    {
      title: '库存上限',
      dataIndex: 'upperLimit',
      width: 120,
      editable: false,
    },
    {
      title: '一体系库存',
      dataIndex: 'etcHasInventory',
      width: 80,
      editable: false,
      renderText: (text) => {
        if (typeof text !== 'undefined') {
          if (text === 1) {
            return '有货';
          } else if (text === 0) {
            return '无货';
          }
        } else {
          return '';
        }
      },
    },
    {
      title: '采购价',
      dataIndex: 'purchasePrice',
      width: 80,
      editable: false,
      valueType: 'money',
      render: (text, record: PurchaseGoodsEntity) =>
        record.purchasePrice && record.itemId ? (
          <a
            className="cursor-pointer"
            onClick={() =>
              props.handleViewPriceInfo({
                itemId: record.itemId,
                itemName: record.skuName,
                hidePriceTab: true,
              })
            }
          >
            <MoneyText text={(record as PurchaseGoodsEntity).purchasePrice} />
          </a>
        ) : (
          record.purchasePrice
        ),
    },
    {
      title: '采购数量',
      dataIndex: 'number',
      width: 100,
      fixed: 'right',
      valueType: 'digit',
      renderFormItem: (item, config) => {
        const step = (config.record)?.minPackNum ?? 1;
        return (
          <InputNumber
            min={(config.record)?.minOrderNum ?? 1}
            step={step}
            precision={0}
            max={9999}
            parser={(value: any) => {
              const parsedValue = parseInt(value, 10);
              if (parsedValue % step !== 0) {
                return Math.round(parsedValue / step) * step;
              }
              return parsedValue;
            }}
            placeholder="请输入"
            disabled={props.addedItemSns?.includes(config.record?.etcNo as string)}
            onPressEnter={() => props.handleAdd([config.record])}
          />
        );
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: '操作',
      width: 80,
      editable: false,
      fixed: 'right',
      align: 'center',
      render: (text, row) => (
        <Button
          type="link"
          className="px-[0]"
          disabled={props.addedItemSns?.includes((row as PurchaseGoodsEntity).etcNo) || !row.etcNo}
          onClick={() => props.handleAdd([row as PurchaseGoodsEntity])}
        >
          添加
        </Button>
      ),
    },
  ] as ProColumns<GoodsMultiEntity, 'text'>[];
