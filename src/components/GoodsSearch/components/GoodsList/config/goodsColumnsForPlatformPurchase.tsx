import ColumnRender from '@/components/ColumnRender';
import { requiredProps } from '@/types/validateRules';
import type { ProColumns } from '@ant-design/pro-components';
import { Button, InputNumber, Tag } from 'antd';
import type { CloudGoodsEntity } from '../types/cloud.goods.entity';
import type { GoodsMultiEntity } from '../types/goods.multi.entity';

import type { PriceInfoDrawerProps } from '@/components/PriceInfoDrawer';
import MoneyText from '@/components/common/MoneyText';
import { MAX_COUNT } from '@/utils/Constants';

export interface GoodsColumnsForPlatformPurchaseProps {
  /**
   * 已选商品ID集合
   */
  addedItemSns: string[];
  /**
   * 添加商品事件
   * @param itemList
   */
  handleAdd: (itemList: any[]) => void;
  /**
   * 查看价格
   */
  handleViewPriceInfo: (data: PriceInfoDrawerProps) => void;
}

export const goodsColumnsForPlatformPurchase = (props: GoodsColumnsForPlatformPurchaseProps) =>
  [
    {
      title: '序号',
      valueType: 'index',
      fixed: 'left',
      width: 40,
      editable: false,
    },
    {
      title: '商品名称',
      dataIndex: 'skuName',
      width: 140,
      editable: false,
    },
    {
      title: 'OE号',
      dataIndex: 'oeNos',
      width: 140,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: '品牌件号',
      dataIndex: 'brandPartNos',
      width: 100,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: '品牌',
      dataIndex: 'brandName',
      width: 100,
      editable: false,
    },
    {
      title: '分类',
      dataIndex: 'categoryName',
      width: 100,
      editable: false,
    },
    {
      title: '适用车系',
      dataIndex: 'adaptSeries',
      width: 120,
      editable: false,
    },
    {
      title: '标准适用车型',
      dataIndex: 'adaptCarModel',
      width: 120,
      editable: false,
      ellipsis: true,
    },
    {
      title: '商品备注',
      dataIndex: 'skuRemark',
      width: 120,
      editable: false,
      ellipsis: true,
    },
    {
      title: '单位',
      dataIndex: 'unitName',
      width: 50,
      editable: false,
    },
    {
      title: '本地库存',
      dataIndex: 'avaNum',
      width: 60,
      editable: false,
    },
    {
      title: '库存下限',
      dataIndex: 'lowerLimit',
      width: 60,
      editable: false,
    },
    {
      title: '库存上限',
      dataIndex: 'upperLimit',
      width: 60,
      editable: false,
    },
    {
      title: '30天销量',
      dataIndex: 'saleNum30d',
      width: 80,
      editable: false,
      sorter: true,
    },
    {
      title: '日均销量',
      dataIndex: 'saleAvgNum',
      width: 80,
      editable: false,
      sorter: true,
    },
    {
      title: '一体系库存',
      dataIndex: 'etcHasInventory',
      width: 80,
      editable: false,
      renderText: (text) => (text === 0 ? '无货' : '有货'),
    },
    {
      title: '最小起订量',
      dataIndex: 'minOrderNum',
      width: 80,
      editable: false,
    },
    {
      title: '最小包装量',
      dataIndex: 'minPackNum',
      width: 80,
      editable: false,
    },
    {
      title: '采购价',
      dataIndex: 'etcPurchasePrice',
      width: 80,
      editable: false,
      valueType: 'money',
      render: (text, record: CloudGoodsEntity) =>
        record.etcPurchasePrice && record.memberItemId ? (
          <a
            className="cursor-pointer"
            onClick={() =>
              props.handleViewPriceInfo({
                itemId: record.memberItemId,
                itemName: record.skuName,
                hidePriceTab: true,
              })
            }
          >
            <MoneyText text={(record as CloudGoodsEntity).etcPurchasePrice} />
          </a>
        ) : (
          record.etcPurchasePrice
        ),
    },
    {
      title: '采购数量',
      dataIndex: 'number',
      width: 100,
      fixed: 'right',
      valueType: 'digit',
      renderFormItem: (item, config) => {
        const step = (config.record as CloudGoodsEntity)?.minPackNum ?? 1;
        return (
          <InputNumber
            min={(config.record as CloudGoodsEntity)?.minOrderNum ?? 1}
            step={step}
            precision={0}
            max={MAX_COUNT}
            parser={(value: any) => {
              const parsedValue = parseInt(value, 10);
              if (parsedValue % step !== 0) {
                return Math.round(parsedValue / step) * step;
              }
              return parsedValue;
            }}
            placeholder="请输入"
            disabled={props.addedItemSns?.includes(config.recordKey as string)}
            onPressEnter={() => props.handleAdd([config.record as CloudGoodsEntity])}
          />
        );
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: '操作',
      width: 60,
      editable: false,
      fixed: 'right',
      align: 'center',
      render: (text, row) => (
        <Button
          type="link"
          className="px-[0]"
          disabled={props.addedItemSns?.includes((row as CloudGoodsEntity).etcNo)}
          onClick={() => props.handleAdd([row as CloudGoodsEntity])}
        >
          添加
        </Button>
      ),
    },
  ] as ProColumns<GoodsMultiEntity, 'text'>[];
