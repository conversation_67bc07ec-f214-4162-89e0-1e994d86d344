import ColumnRender from '@/components/ColumnRender';
import type { StoreGoodsEntity } from '@/components/GoodsSearch/components/GoodsList/types/store.goods.entity';
import type { StocksInfoDrawerProps } from '@/components/StocksInfoDrawer';
import { requiredProps } from '@/types/validateRules';
import { MAX_AMOUNT, MAX_COUNT } from '@/utils/Constants';
import type { ProColumns } from '@ant-design/pro-components';
import { Button, InputNumber } from 'antd';
import type { GoodsMultiEntity } from '../types/goods.multi.entity';

export interface GoodsColumnsForExternalPurchaseReturn {
  /**
   * 已选商品ID集合
   */
  addedItemSns: string[];
  /**
   * 添加商品事件
   * @param itemList
   */
  handleAdd: (itemList: any[]) => void;
  /**
   * 查看库存
   */
  handleViewStocksInfo: (data: StocksInfoDrawerProps) => void;
}

export const goodsColumnsForExternalPurchaseReturn = (
  props: GoodsColumnsForExternalPurchaseReturn,
) =>
  [
    {
      title: '序号',
      valueType: 'index',
      fixed: 'left',
      width: 40,
      editable: false,
    },
    {
      title: '商品名称',
      dataIndex: 'skuName',
      fixed: 'left',
      width: 140,
      ellipsis: true,
      editable: false,
    },
    {
      title: '商品编码',
      dataIndex: 'itemSn',
      width: 100,
      editable: false,
    },
    {
      title: 'OE号',
      dataIndex: 'oeNos',
      width: 140,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: '品牌件号',
      dataIndex: 'brandPartNos',
      width: 100,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: '品牌',
      dataIndex: 'brandName',
      width: 100,
      editable: false,
    },
    {
      title: '本地库存',
      dataIndex: 'avaNum',
      width: 60,
      editable: false,
      render: (text, record: StoreGoodsEntity) => (
        <a
          className="cursor-pointer"
          onClick={() => {
            props.handleViewStocksInfo({
              itemIdList: [record.itemId],
            });
          }}
        >
          {record.avaNum}
        </a>
      ),
    },
    {
      title: '采购价',
      dataIndex: 'purchasePrice',
      width: 80,
      editable: false,
      valueType: 'money',
    },
    {
      title: '退款金额',
      dataIndex: 'price',
      width: 100,
      fixed: 'right',
      renderFormItem: (item, config) => {
        return (
          <InputNumber
            min={0.01}
            max={MAX_AMOUNT}
            controls={false}
            precision={2}
            placeholder="请输入"
            disabled={props.addedItemSns?.includes(config.recordKey as string)}
          />
        );
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: '退货数量',
      dataIndex: 'number',
      width: 100,
      fixed: 'right',
      renderFormItem: (item, config) => {
        return (
          <InputNumber
            min={1}
            max={MAX_COUNT}
            precision={0}
            placeholder="请输入"
            disabled={props.addedItemSns?.includes(config.recordKey as string)}
            onPressEnter={() => props.handleAdd([config.record as StoreGoodsEntity])}
          />
        );
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: '操作',
      width: 60,
      editable: false,
      align: 'center',
      fixed: 'right',
      render: (text, row) => (
        <Button
          type="link"
          className="px-[0]"
          disabled={props.addedItemSns?.includes((row as StoreGoodsEntity).itemSn)}
          onClick={() => props.handleAdd([row as StoreGoodsEntity])}
        >
          添加
        </Button>
      ),
    },
  ] as ProColumns<GoodsMultiEntity, 'text'>[];
