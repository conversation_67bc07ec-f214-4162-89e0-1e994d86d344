import { useModel } from '@@/exports';
import { history, useLocation } from '@umijs/max';
import { Menu, MenuProps } from 'antd';

const SidebarMenu = () => {
  const { initialState } = useModel('@@initialState');
  const { isSidebarFold } = useModel('layoutModel');
  const location = useLocation();

  /**
   * 组装打开的菜单
   */
  const getOpenKeys = (): string[] => {
    const pathname = location.pathname;
    const paths = pathname.split('/').filter((path) => path);
    const openKeys = paths.map((_, index) => `/${paths.slice(0, index + 1).join('/')}`);
    return openKeys;
  };

  /**
   * 菜单点击事件
   * @param e
   */
  const onClick: MenuProps['onClick'] = (e) => {
    history.push(e.key);
  };

  return (
    <Menu
      onClick={onClick}
      selectedKeys={[location.pathname]}
      defaultOpenKeys={getOpenKeys()}
      inlineCollapsed={isSidebarFold}
      mode="inline"
      // @ts-ignore
      items={initialState?.systemMenu ?? []}
    />
  );
};

export default SidebarMenu;
