import SidebarMenu from '@/components/SidebarMenu';
import { useModel } from '@@/exports';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import { useIntl } from '@umijs/max';
import classNames from 'classnames';

const Sidebar = () => {
  const { isSidebarFold, setIsSidebarFold } = useModel('layoutModel');
  const intl = useIntl();

  const handleToggleSidebar = () => {
    setIsSidebarFold(!isSidebarFold);
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 flex-shrink-0 min-h-0 overflow-y-auto">
        <SidebarMenu />
      </div>
      <div
        className={classNames('p-[10px] cursor-pointer bg-white whitespace-nowrap', {
          'text-center': isSidebarFold,
        })}
        onClick={handleToggleSidebar}
      >
        <span className={classNames('mr-[10px] ml-[6px]')}>
          {isSidebarFold ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        </span>
        {!isSidebarFold && intl.formatMessage({ id: 'layout.sidebar.fold' })}
      </div>
    </div>
  );
};

export default Sidebar;
