import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { PaySubTypeList } from '@/pages/purchase/detail/types/purchase.post.entity';
import { PayTypeStatus } from '@/pages/purchase/list/types/PayTypeStatus';
import { ModalForm } from '@ant-design/pro-components';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';
import PaymentExternalForm, { PaymentExternalFormProps } from '../PaymentExternalForm';
import MoneyText from '../common/MoneyText';

export interface PayValues {
  /**
   * 支付明细
   */
  paySubTypeList?: PaySubTypeList[];
  /**
   * 支付方式1现款2挂账
   */
  payType?: PayTypeStatus;
}

export interface PaymentExternalFormModalProps extends PaymentExternalFormProps {
  visible: boolean;
  onClose: () => void;
  totalAmount?: number; // 结算金额
  onSubmit: (values: PayValues) => void;
  dataSource?: PayValues;
}

const PaymentExternalFormModal = (props: PaymentExternalFormModalProps) => {
  const [form] = useForm();
  const { visible, onClose, totalAmount, onSubmit, dataSource, ...rest } = props;
  const { storeId } = props;
  const [accountList, setAccountList] = useState<any[]>([]);

  useEffect(() => {
    if (dataSource) {
      form.setFieldsValue(dataSource);
      if (storeId) {
        queryMemberAccountPage({
          belongToStore: [storeId],
        }).then((result) => {
          if (result?.data?.length) {
            setAccountList(
              result.data.map((item) => ({
                label: item.memberAccountName,
                value: item.id.toString(),
              })),
            );
          }
        });
      }
    }
  }, [visible]);

  const handleOk = async () => {
    form.validateFields().then((result) => {
      onSubmit(result);
    });
  };
  const paymentOnValueChange = async (values: any, allValue: any) => {
    if (PayTypeStatus.CASH == values?.payType) {
      //现款
      if (allValue?.paySubTypeList == undefined || allValue?.paySubTypeList?.length == 0) {
        //如果没有值
        form.setFieldValue('paySubTypeList', [
          { id: accountList?.[0]?.value, amount: totalAmount },
        ]);
      }
    }
  };
  return (
    <ModalForm
      title="结算方式"
      open={visible}
      form={form}
      width="30%"
      layout="horizontal"
      modalProps={{
        maskClosable: false,
        centered: true,
        destroyOnClose: true,
        onCancel: onClose,
      }}
      submitTimeout={2000}
      onValuesChange={paymentOnValueChange}
      onFinish={handleOk}
    >
      <div className="mb-4">
        结算金额：
        <span className="text-[24px] text-[#F83431]">
          <MoneyText text={totalAmount} />
        </span>
      </div>
      <PaymentExternalForm {...rest} />
    </ModalForm>
  );
};

export default PaymentExternalFormModal;
