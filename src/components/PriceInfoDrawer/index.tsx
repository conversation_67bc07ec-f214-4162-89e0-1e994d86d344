import PriceInfo from '@/components/PriceInfoDrawer/components/PriceInfo';
import PurchaseInfo from '@/components/PriceInfoDrawer/components/PurchaseInfo';
import { useIntl } from '@umijs/max';
import { Drawer, Tabs } from 'antd';
import type { Tab } from 'rc-tabs/lib/interface';
import { useEffect, useState } from 'react';
import { GoodsSearchTabType } from '../GoodsSearch/types/TabType';

export interface PriceInfoDrawerProps {
  itemId?: string;
  storeId?: string;
  cstId?: string;
  visible?: boolean;
  onClose?: () => void;
  itemName?: string;
  itemSn?: string;
  suggestPrice?: number; // 销售价
  lowPrice?: number; // 最低售价
  costPrice?: number; // 成本价
  hidePriceTab?: boolean; // 是否隐藏价格信息tab
  defaultActiveTabKey?: 'price' | 'purchase'; // 默认激活的tab
}

const PriceInfoDrawer = (props: PriceInfoDrawerProps) => {
  const intl = useIntl();
  const { visible, hidePriceTab = false, onClose, defaultActiveTabKey = 'price' } = props;
  const [activeTabKey, setActiveTabKey] = useState<string>(defaultActiveTabKey);


  useEffect(() => {
    if (visible) {
      setActiveTabKey(defaultActiveTabKey)
    }
  }, [visible, defaultActiveTabKey])

  const items: Tab[] = [];

  if (!hidePriceTab) {
    items.push({
      label: intl.formatMessage({ id: 'common.priceInfoDrawer.priceInfo' }),
      key: 'price',
      children: <PriceInfo {...props} />,
    });
  }

  items.push({
    label: intl.formatMessage({ id: 'common.priceInfoDrawer.purchaseHistory' }),
    key: 'purchase',
    children: <PurchaseInfo {...props} />,
  });

  return (
    <Drawer
      width={1080}
      open={visible}
      onClose={onClose}
      maskClosable={false}
      title={`${props.itemName ?? ''}${props.itemSn ? `(${props.itemSn})` : ''}`}
      bodyStyle={{ padding: 0, background: '#f2f2f2' }}
    >
      <Tabs activeKey={activeTabKey} onChange={(val) => setActiveTabKey(val as GoodsSearchTabType)} items={items} tabBarStyle={{ background: '#fff', padding: '0 30px' }} />
    </Drawer>
  );
};

export default PriceInfoDrawer;
