export interface LastSalePriceResponse {
  /**
   * 客户最近5次售价
   */
  cstLastFiveSalePrice?: CstLastFiveSalePrice[];
  /**
   * None
   */
  memberId?: string;
  /**
   * 门店最近5次售价
   */
  storeLastFiveSalePrice?: StoreLastFiveSalePrice[];
}

export interface CstLastFiveSalePrice {
  /**
   * None
   */
  cstName?: string;
  /**
   * None
   */
  saleNum?: number;
  /**
   * None
   */
  salePrice?: number;
  /**
   * None
   */
  saleTime?: string;
}

export interface StoreLastFiveSalePrice {
  /**
   * None
   */
  cstName?: string;
  /**
   * None
   */
  saleNum?: number;
  /**
   * None
   */
  salePrice?: number;
  /**
   * None
   */
  saleTime?: string;
}
