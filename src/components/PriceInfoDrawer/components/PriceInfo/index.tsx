import { lastSalePrice } from '@/components/PriceInfoDrawer/services';
import type { LastSalePriceResponse } from '@/components/PriceInfoDrawer/types/last.sale.price.response';
import FunProTable from '@/components/common/FunProTable';
import MoneyText from '@/components/common/MoneyText';
import SubTitle from '@/components/common/SubTitle';
import { ProCard } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useEffect, useState } from 'react';
import type { PriceInfoDrawerProps } from '../../index';

const PriceInfo = (props: PriceInfoDrawerProps) => {
  const intl = useIntl();
  const { suggestPrice, lowPrice, costPrice, itemId, cstId, storeId } = props;
  const [detail, setDetail] = useState<LastSalePriceResponse>();

  const priceInfo = [
    {
      label: intl.formatMessage({ id: 'common.priceInfoDrawer.salePrice' }),
      value: suggestPrice,
    },
    {
      label: intl.formatMessage({ id: 'common.priceInfoDrawer.lowestPrice' }),
      value: lowPrice,
    },
    {
      label: intl.formatMessage({ id: 'common.priceInfoDrawer.costPrice' }),
      value: costPrice,
    },
  ];

  useEffect(() => {
    if (itemId && storeId) {
      lastSalePrice({ itemId, storeId, cstId }).then((result) => {
        if (result) {
          setDetail(result);
        }
      });
    }
  }, [itemId, storeId, cstId]);

  return (
    <div className="mx-[24px]">
      <ProCard title={<SubTitle text={intl.formatMessage({ id: 'common.priceInfoDrawer.goodsPrice' })} />} gutter={20}>
        {priceInfo.map((item) => (
          <ProCard key={item.label} bordered={true}>
            <div className="text-2xl text-[#f83331]">
              <MoneyText text={item.value} />
            </div>
            <div className="text-gray-500 ml-2">{item.label}</div>
          </ProCard>
        ))}
      </ProCard>
      {cstId && (
        <FunProTable
          headerTitle={<SubTitle text={intl.formatMessage({ id: 'common.priceInfoDrawer.customerLastFivePrices' })} />}
          search={false}
          className="mt-5"
          options={false}
          scroll={{ x: true }}
          pagination={false}
          dataSource={detail?.cstLastFiveSalePrice ?? []}
          columns={[
            {
              title: intl.formatMessage({ id: 'common.priceInfoDrawer.orderNo' }),
              dataIndex: 'orderNo',
              width: 160,
            },
            {
              title: intl.formatMessage({ id: 'common.priceInfoDrawer.price' }),
              dataIndex: 'salePrice',
              valueType: 'money',
              width: 80,
            },
            {
              title: intl.formatMessage({ id: 'common.priceInfoDrawer.quantity' }),
              dataIndex: 'saleNum',
              width: 80,
            },
            {
              title: intl.formatMessage({ id: 'common.priceInfoDrawer.time' }),
              valueType: 'money',
              dataIndex: 'sortTime',
            },
          ]}
        />
      )}

      <FunProTable
        headerTitle={<SubTitle text={intl.formatMessage({ id: 'common.priceInfoDrawer.storeLastFivePrices' })} />}
        className="mt-5"
        search={false}
        scroll={{ x: true }}
        options={false}
        pagination={false}
        dataSource={detail?.storeLastFiveSalePrice ?? []}
        columns={[
          {
            title: intl.formatMessage({ id: 'common.priceInfoDrawer.orderNo' }),
            dataIndex: 'orderNo',
            width: 160,
          },
          {
            title: intl.formatMessage({ id: 'common.priceInfoDrawer.price' }),
            dataIndex: 'salePrice',
            valueType: 'money',
            width: 80,
          },
          {
            title: intl.formatMessage({ id: 'common.priceInfoDrawer.quantity' }),
            dataIndex: 'saleNum',
            width: 80,
          },
          {
            title: intl.formatMessage({ id: 'common.priceInfoDrawer.customerName' }),
            dataIndex: 'cstName',
            width: 220,
          },
          {
            title: intl.formatMessage({ id: 'common.priceInfoDrawer.time' }),
            valueType: 'money',
            dataIndex: 'sortTime',
          },
        ]}
      />
    </div>
  );
};

export default PriceInfo;
