import { queryPurchaseRecord } from '@/components/PriceInfoDrawer/services';
import { PurchaseRecordEntity } from '@/components/PriceInfoDrawer/types/purchase.record.entity';
import FunProTable from '@/components/common/FunProTable';
import SubTitle from '@/components/common/SubTitle';
import { useIntl } from '@umijs/max';
import { useEffect, useState } from 'react';
import { PriceInfoDrawerProps } from '../../index';

const PurchaseInfo = (props: PriceInfoDrawerProps) => {
  const intl = useIntl();
  const { itemId } = props;
  const [detail, setDetail] = useState<PurchaseRecordEntity[]>();

  useEffect(() => {
    if (itemId) {
      queryPurchaseRecord(itemId).then((result) => {
        if (result) {
          setDetail(result);
        }
      });
    }
  }, [itemId]);

  console.log('queryPurchaseRecord', detail);

  return (
    <div className="mx-[24px]">
      <FunProTable
        headerTitle={
          <span className="pt-[5px]">
            <SubTitle text={intl.formatMessage({ id: 'common.priceInfoDrawer.lastTenPurchases' })} />
          </span>
        }
        dataSource={detail}
        search={false}
        pagination={false}
        options={false}
        scroll={{ x: true }}
        columns={[
          {
            title: intl.formatMessage({ id: 'common.priceInfoDrawer.purchaseOrderNo' }),
            dataIndex: 'orderNo',
          },
          {
            title: intl.formatMessage({ id: 'common.priceInfoDrawer.supplier' }),
            dataIndex: 'supplierName',
          },
          {
            title: intl.formatMessage({ id: 'common.priceInfoDrawer.purchaseQuantity' }),
            dataIndex: 'sumQuantity',
          },
          {
            title: intl.formatMessage({ id: 'common.priceInfoDrawer.purchasePrice' }),
            valueType: 'money',
            dataIndex: 'sumAmount',
          },
          {
            title: intl.formatMessage({ id: 'common.priceInfoDrawer.purchaseTime' }),
            dataIndex: 'orderTime',
          },
        ]}
      />
    </div>
  );
};

export default PurchaseInfo;
