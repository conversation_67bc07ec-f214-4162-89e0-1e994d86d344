import { request } from '@/utils/request';
import { LastSalePriceRequest } from '@/components/PriceInfoDrawer/types/last.sale.price.request';
import { LastSalePriceResponse } from '@/components/PriceInfoDrawer/types/last.sale.price.response';
import { PurchaseRecordEntity } from '@/components/PriceInfoDrawer/types/purchase.record.entity';

export const lastSalePrice = async (params: LastSalePriceRequest) => {
  return request<LastSalePriceResponse>(`/ipmsconsole/goods/price/lastSalePrice`, {
    data: params,
  });
};

export const queryPurchaseRecord = async (itemId: string) => {
  return request<PurchaseRecordEntity[]>(
    `/ipmspurchase/purchase/PurchaseOrderFacade/queryPurchaseRecord`,
    {
      data: { itemId },
    },
  );
};
