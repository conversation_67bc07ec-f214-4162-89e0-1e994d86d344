import ColumnRender from '@/components/ColumnRender';
import { queryEtcAfterDetailPagePost } from '@/pages/purchase/detail/services';
import type { ViewDetailModalProps } from '@/pages/system/message';
import MessageDetailModal from '@/pages/system/message/components/MessageDetailModal';
import {
  createReturnOrderByETC,
  getUnReadCount,
  queryMsgList,
  setRead,
} from '@/pages/system/message/services';
import { MsgJumpType } from '@/pages/system/message/types/MsgJumpType';
import { MsgStatus } from '@/pages/system/message/types/MsgStatus';
import { NoticeType } from '@/pages/system/message/types/NoticeType';
import type { MsgListItemEntity } from '@/pages/system/message/types/msg.list.item.entity';
import { history } from '@@/core/history';
import { BellOutlined } from '@ant-design/icons';
import { App, Badge, Button, Space, Spin, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { MsgBizType } from '@/pages/system/message/types/MsgBizType';
import classNames from 'classnames';

const Message = () => {
  const { notification } = App.useApp();
  const getModalMessageTimer = useRef<any>();
  const messageIdRef = useRef<number>();
  const messageTypeRef = useRef<MsgBizType>();
  const [loading, setLoading] = useState(false);
  const [unReadCount, setUnReadCount] = useState(0);
  const [viewDetailModalProps, setViewDetailModalProps] = useState<ViewDetailModalProps>({
    visible: false,
  });
  /**
   * 轮询未读消息数目
   */
  const getUnReadCountFn = () => {
    getUnReadCount().then((result) => {
      setUnReadCount(result);
    });
  };

  useEffect(() => {
    // getUnReadCountFn();
    // const queryUnReadCountTimer = setInterval(() => {
    //   getUnReadCountFn();
    // }, 3000);
    // return () => {
    //   clearInterval(queryUnReadCountTimer);
    // };
  }, []);

  /**
   * 轮询弹窗消息
   */
  const getModalMessage = () => {
    queryMsgList({
      noticeType: NoticeType.Modal,
      isDelete: 0,
      msgStatus: MsgStatus.NoRead,
      pageNo: 1,
      pageSize: 1,
      overdueTime: dayjs().subtract(2, 'days').format('YYYY-MM-DD HH:mm:ss'),
    }).then((result) => {
      if (result?.data?.length) {
        messageIdRef.current = result?.data?.[0].id;
        messageTypeRef.current = result?.data?.[0].bizType;
        notification.open({
          message: <Content modalMessage={result?.data?.[0]} />,
          duration: 0,
          onClose: onClose,
        });
        clearInterval(getModalMessageTimer.current);
      }
    });
  };

  /**
   * 弹窗内容
   */
  const Content = (props: { modalMessage: MsgListItemEntity }) => {
    const { modalMessage } = props;
    return (
      <Spin spinning={loading}>
        <div className="mb-4">{modalMessage?.title}</div>
        <div
          className={classNames('mb-4', {
            'line-clamp-2': messageTypeRef.current === MsgBizType.System,
          })}
        >
          {ColumnRender.RichContentColumnRender(modalMessage?.content ?? '')}
        </div>
        <Space>
          {MsgJumpType.ETC_PURCHASE_ORDER === modalMessage?.jumpType && (
            <Button
              type="primary"
              ghost
              onClick={() => {
                history.push(`/purchase/detail?${modalMessage?.jumpParam}`);
                // eslint-disable-next-line @typescript-eslint/no-use-before-define
                onClose();
              }}
            >
              查看采购单
            </Button>
          )}
          {MsgJumpType.REORDER_RECOMMENDATION === modalMessage?.jumpType && (
            <Button
              type="primary"
              ghost
              onClick={() => {
                history.push(`/purchase/stockUp/detail?${modalMessage?.jumpParam}`);
                // eslint-disable-next-line @typescript-eslint/no-use-before-define
                onClose();
              }}
            >
              查看补货建议
            </Button>
          )}
          {MsgJumpType.ETC_RETURN_ORDER === modalMessage?.jumpType && (
            <Button
              type="primary"
              ghost
              onClick={() => {
                setLoading(true);
                createReturnOrderByETC(JSON.parse(modalMessage?.jumpParam)?.afterSalesNo)
                  .then((result) => {
                    if (result) {
                      history.push(`/purchase/returns/detail?returnId=${result}`);
                      // eslint-disable-next-line @typescript-eslint/no-use-before-define
                      onClose();
                    }
                  })
                  .finally(() => {
                    setLoading(false);
                  });
              }}
            >
              创建采购退货单
            </Button>
          )}
          {[MsgJumpType.ETC_RETURN_ORDER, MsgJumpType.ETC_RETURN_LOGISTICS].includes(
            modalMessage?.jumpType!,
          ) && (
            <Button
              onClick={() => {
                setLoading(true);
                queryEtcAfterDetailPagePost({
                  afterSalesNo: JSON.parse(modalMessage?.jumpParam)?.afterSalesNo,
                })
                  .then((result) => {
                    if (result) {
                      window.open(result);
                    }
                  })
                  .finally(() => {
                    setLoading(false);
                  });
              }}
            >
              查看详情
            </Button>
          )}
          {modalMessage.bizType == 0 && (
            <Button
              type="primary"
              onClick={() => {
                setViewDetailModalProps({
                  id: modalMessage.id,
                  visible: true,
                });
                // eslint-disable-next-line @typescript-eslint/no-use-before-define
                onClose();
              }}
            >
              查看详情
            </Button>
          )}
        </Space>
      </Spin>
    );
  };

  useEffect(() => {
    // getModalMessage();
    // getModalMessageTimer.current = setInterval(() => {
    //   getModalMessage();
    // }, 3000);
    // return () => {
    //   clearInterval(getModalMessageTimer.current);
    // };
  }, []);

  // 关闭弹窗消息
  const onClose = () => {
    // if (messageIdRef.current) {
    //   setRead([messageIdRef.current]);
    // }
    // notification.destroy();
    // getModalMessageTimer.current = setInterval(() => {
    //   getModalMessage();
    // }, 3000);
  };

  return (
    <>
      <Tooltip title="系统消息">
        <Badge count={unReadCount} dot={true} offset={[-8, 8]}>
          <BellOutlined
            className="p-2 rounded-lg  cursor-pointer hover:bg-[#00000008]"
            style={{ fontSize: 18 }}
            onClick={() => {
              history.push('/system/message');
            }}
          />
        </Badge>
      </Tooltip>
      <MessageDetailModal
        {...viewDetailModalProps}
        onClose={() => setViewDetailModalProps({ id: undefined, visible: false })}
      />
    </>
  );
};

export default Message;
