import { clearCookies } from "@/access";
import iconUser from '@/assets/icons/home/<USER>';
import Message from '@/components/Avatar/components/Message';
import { logoutPost } from '@/pages/common/login/services';
import { useModel } from '@@/exports';
import { CaretDownOutlined } from '@ant-design/icons';
import { history, useIntl } from '@umijs/max';
import type { MenuProps } from 'antd';
import { Avatar, Dropdown, Flex, Space, Tag, Tooltip } from 'antd';

export default () => {
  const { initialState, setInitialState } = useModel('@@initialState');
  const intl = useIntl();

  const handleLogout = async () => {
    const { code } = await logoutPost({});
    if (code == 0) {
      // @ts-ignore
      setInitialState((s) => ({
        ...s,
        currentUser: {},
        currentUserInfo: {},
      }));
      clearCookies();
      history.push('/login');
    }
  };

  const items: MenuProps['items'] = [
    {
      key: '1',
      label: (
        <Flex justify="flex-start" gap={8}>
          <Avatar src={iconUser} size={40} />
          <Flex gap={4} vertical justify="space-between">
            <Space>
              <span>{initialState?.currentUser?.accountName}</span>
              <span>{initialState?.currentUser?.phone}</span>
            </Space>
            <Space wrap direction="horizontal" className="w-48">
              {initialState?.currentUserInfo?.roleNames &&
                initialState?.currentUserInfo?.roleNames.split(',').map((t: string) => (
                  <Tag key={t} className="text-[#176EFFFF] bg-[#E7F0FFFF] rounded border-none">
                    {t}
                  </Tag>
                ))}
            </Space>
          </Flex>
        </Flex>
      ),
    },
    {
      key: '2',
      label: (
        <Flex vertical className="w-48" gap={8}>
          <span className="text-[#000000D9]">{initialState?.currentUser?.memberName}</span>
          <Space wrap direction="horizontal" className="w-64" size={4}>
            {initialState?.currentUserInfo?.storeNames &&
              initialState?.currentUserInfo?.storeNames.split(',').map((t: string) => (
                <Tooltip key={t} title={t}>
                  <Tag className="text-[#00000073] truncate max-w-[200px]">
                    {t}
                  </Tag>
                </Tooltip>
              ))}
          </Space>
        </Flex>
      ),
    },
    {
      type: 'divider',
    },
    {
      key: '5',
      label: <span className="text-[#F83331FF]">{intl.formatMessage({ id: 'layout.button.logout' })}</span>,
    },
  ];

  return (
    <>
      <Space size={16}>
        <Space size={8}>
          <Message />
        </Space>
        <Dropdown
          menu={{
            items,
            onClick: ({ key, item }) => {
              console.log(key, item);
              if (key == '5') {
                handleLogout();
              }
            },
          }}
          placement="bottomLeft"
        >
          <Space size={4} className="p-1 rounded-lg  cursor-pointer hover:bg-[#********]">
            <Avatar src={iconUser} size={24} />
            <Space size={4}>
              <span className="text-[#000000D9]">{initialState?.currentUser?.accountName}</span>
              <CaretDownOutlined />
            </Space>
          </Space>
        </Dropdown>
        {/* <UserOutlined className="mr-2" /> */}
        {/* <Divider type="vertical" />
      <a className="cursor-pointer" onClick={handleLogout}>
        退出
      </a> */}
      </Space>
    </>
  );
};
