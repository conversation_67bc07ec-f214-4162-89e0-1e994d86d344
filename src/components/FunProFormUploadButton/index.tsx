import { uploadFile } from '@/services/systerm';
import type { ProFormUploadButtonProps } from '@ant-design/pro-components';
import { ProFormUploadButton } from '@ant-design/pro-components';
import type { FieldProps } from '@ant-design/pro-form/es/typing';
import type { UploadFile } from 'antd';
import { Image, Upload, message } from 'antd';
import type { RcFile, UploadChangeParam, UploadProps } from 'antd/es/upload';
import _, { isEmpty } from 'lodash';
import { useState } from 'react';

const isImageFileType = (type?: string) =>
  _.isNumber(type) ? type == 0 : type?.indexOf('image/') === 0;
/**
 * supportScale 支持的长度和宽度 [200*300]
 * supportMaxSize 支持的大小单位为K[200]
 * supportImageTypeArray 支持的图片类型默认支持 "image/jpg" "image/png" "image/gif"
 */
type CustomerType = {
  supportScale?: number[];
  supportMaxSize?: number;
  supportImageTypeArray?: string[];
};
type FunProFormUploadButtonPropsType = ProFormUploadButtonProps & CustomerType;

export default (props: FunProFormUploadButtonPropsType) => {
  const {
    name,
    supportScale = [],
    supportMaxSize = 0,
    // needTips = true,
    supportImageTypeArray = ['image/jpg', 'image/jpeg', 'image/png'],
  } = props;
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [visible, setVisible] = useState(false);
  const { fieldProps, ...restProps } = props;
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  /**
   * 校验图片类型，尺寸，大小
   * @param {*} file
   * @returns
   */
  const checkImageFile = async (file: RcFile) => {
    const isImage = isImageFileType(file.type);
    if (!isImage) {
      message.error('文件类型错误（仅支持JPG/PNG类型的图片文件）！');
      return false;
    }
    const fileType = file.type;
    const errorMessage = [];
    if (!_.isEmpty(supportImageTypeArray) && !supportImageTypeArray.includes(fileType)) {
      errorMessage.push('文件类型错误（仅支持JPG/PNG类型的图片文件）！');
    }
    if (supportMaxSize > 0) {
      const fileSizeByK = file.size / 1024;
      if (file.size > supportMaxSize * 1024) {
        errorMessage.push(`文件过大(${fileSizeByK}K),最大支持${supportMaxSize}K的图片文件！`);
      }
    }
    if (!_.isEmpty(supportScale)) {
      const [maxWidth, maxHeight] = supportScale;
      const { width, height } = await createImageBitmap(file);
      if (width != maxWidth || height != maxHeight) {
        errorMessage.push(`文件大小错误（必须为${maxWidth}*${maxHeight}的图片文件）！`);
      }
    }
    const flag = _.isEmpty(errorMessage);
    if (!flag) {
      message.error(errorMessage);
    }
    return flag;
  };

  /**
   * 预览
   * @param file
   */
  const handlePreview = (file: UploadFile) => {
    const { url, originFileObj } = file;
    if (url) {
      setPreviewUrl(url);
    } else {
      const reader = new FileReader();
      reader.onload = function (evt) {
        if (evt.target) {
          const base64: string = evt.target.result as string;
          setPreviewUrl(base64);
        }
      };
      if (originFileObj) {
        reader.readAsDataURL(originFileObj);
      }
    }
    setVisible(true);
  };

  /**
   * 下载
   * @param file
   */
  const handleDownload = (file: UploadFile) => {
    const { name: fileName, originFileObj, url } = file;
    if (url) {
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      // 触发下载
      a.click();
    } else if (originFileObj) {
      const dataUrl = URL.createObjectURL(originFileObj);
      const a = document.createElement('a');
      a.href = dataUrl;
      a.download = fileName;
      // 触发下载
      a.click();
      // 释放内存
      URL.revokeObjectURL(dataUrl);
    }
  };

  const beforeUpload = async (file: RcFile) => {
    const flag = await checkImageFile(file);
    if (flag) {
      const formData = new FormData();
      formData.append('file', file);
      // console.log('formData', formData.get("file"));
      const result = await uploadFile(formData);
      console.log('uploadFile', result);
      if (!isEmpty(result)) {
        // @ts-ignore
        file.url = result[0];
      }

      // return file;
      return false;
    } else {
      return Upload.LIST_IGNORE;
    }
  };
  /**
   * 删除文件
   * @param file
   */
  const handleRemove = (file: UploadFile) => {
    setFileList(fileList.filter((t) => t.uid != file.uid));
  };

  const defaultFieldProps: Partial<FieldProps<HTMLElement> & UploadProps<any>> = {
    showUploadList: {
      showDownloadIcon: true,
      showPreviewIcon: true,
      showRemoveIcon: true,
    },
    onPreview: handlePreview,
    onDownload: handleDownload,
    onRemove: handleRemove,
    onChange: (info: UploadChangeParam) => setFileList(info.fileList),
    beforeUpload: beforeUpload,
  };

  const UploadButton = (
    <ProFormUploadButton
      listType="picture-card"
      accept=".jpg,.png,.jpeg,.gif"
      max={3}
      name={name}
      fieldProps={defaultFieldProps}
      {...restProps}
    />
  );

  return (
    <>
      {UploadButton}
      {previewUrl && (
        <Image
          hidden
          style={{
            display: 'none',
          }}
          preview={{
            visible,
            src: previewUrl,
            onVisibleChange: (value) => {
              setVisible(value);
            },
          }}
        />
      )}
    </>
  );
};
