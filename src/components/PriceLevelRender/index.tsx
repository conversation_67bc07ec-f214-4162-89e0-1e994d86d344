import type { StoreGoodsEntity } from '@/components/GoodsSearch/components/GoodsList/types/store.goods.entity';
import { MAX_AMOUNT } from '@/utils/Constants';
import { InputNumber, Popover } from 'antd';

/**
 * 输入框的悬浮框列渲染
 * @returns
 */
export interface InputColumnRenderProps {
  record: StoreGoodsEntity;
  addedItemSns?: string[];
  cstId?: string;
  value?: number | null;
  onChange?: (value: number | null) => void;
}
export const InputColumnRender = (props: InputColumnRenderProps) => {
  const { record, value, onChange, cstId, addedItemSns } = props;
  return (
    <Popover
      arrow={false}
      placement="bottom"
      trigger="focus"
      content={
        record.priceDetails && record.priceDetails?.length > 0 ? (
          <div className="max-h-[260px] overflow-auto">
            {record.priceDetails.map((t, index) => (
              <p key={t.levelId}>
                {t.levelName}：{t.levelPrice}
              </p>
            ))}
          </div>
        ) : (
          false
        )
      }
    >
      <InputNumber
        min={0}
        max={MAX_AMOUNT}
        precision={2}
        value={value}
        onChange={(v) => onChange?.(v)}
        disabled={addedItemSns?.includes(record.itemSn as string) || !cstId}
      />
    </Popover>
  );
};
export default { InputColumnRender };
