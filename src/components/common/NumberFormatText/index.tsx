import { join } from 'lodash';

const isNumeric = (str?: string) => {
  return !!str && /^-?\d+(\.\d+)?([eE][+-]?\d+)?$/.test(str);
};
export interface NumberFormatTextProps {
  /**
   * 需要格式化的字符串或者数字
   */
  numberText?: string;
  /**
   * 如果为空 默认展示 "-"
   */
  emptyNumberText?: string;
  // 小数点精度 默认2位
  precision?: number;
  prefixStr?: string;
}
export default ({
  numberText,
  emptyNumberText = '-',
  precision = 0,
  prefixStr,
}: NumberFormatTextProps) => {
  const result: string[] = [];
  // 是否需要人民币前缀
  if (prefixStr) {
    result.push(prefixStr);
  }
  let moneyStr = emptyNumberText;
  if (isNumeric(numberText)) {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'decimal',
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    });
    moneyStr = formatter.format(Number(numberText));
  }
  result.push(moneyStr);
  return <span>{join(result, '')}</span>;
};
