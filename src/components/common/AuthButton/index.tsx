import { useAccess } from '@umijs/max';
import { Button } from 'antd';
import { type AuthButtonProps } from './types';
/**
 * 封装权限按钮
 * @param props BaseButtonProps
 * @returns
 */
const AuthButton = (props: AuthButtonProps) => {
  const { hasButtonPerms } = useAccess();
  const { isHref = false, visible = true } = props;
  if (!visible || !hasButtonPerms(props.authority)) {
    return null;
  }
  if (isHref) {
    return <a onClick={props.onClick}>{props.children}</a>;
  }
  return <Button {...props} />;
};
export default AuthButton;
