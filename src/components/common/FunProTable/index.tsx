import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import type { ResponseDataType } from '@/types/ResponseDataType';
import { ProTable, type ParamsType, type ProTableProps } from '@ant-design/pro-components';
import { type SortOrder } from 'antd/es/table/interface';
import { reverse } from 'lodash';
import React from 'react';

interface FunProTableProps<DataType, Params, ValueType>
  extends ProTableProps<DataType, Params, ValueType> {
  visible?: boolean;
  needPage?: boolean;
  bottomLeft?: React.ReactNode;
  requestPage?: (
    params: Params & PageRequestParamsType,
    sort: Record<string, SortOrder>,
    filter: Record<string, (string | number)[] | null>,
  ) => Promise<PageResponseDataType<DataType>>;
  requestList?: (
    params: Params,
    sort: Record<string, SortOrder>,
    filter: Record<string, (string | number)[] | null>,
  ) => Promise<ResponseDataType<DataType>>;
}
/**
 * 自定义ProTable
 * @param props FunProTableProps
 * @returns
 */
const FunProTable = <
  DataType extends Record<string, any>,
  Params extends ParamsType = ParamsType,
  ValueType = 'text',
>(
  props: FunProTableProps<DataType, Params, ValueType>,
) => {
  const {
    rowKey = 'id',
    requestPage,
    requestList,
    needPage = true,
    visible = true /* bottomLeft */,
  } = props;
  if (!visible) return null;
  return (
    // <ProCard className="relative pb-10">
    <ProTable<DataType, Params, ValueType>
      tableAlertOptionRender={false}
      tableAlertRender={false}
      columnEmptyText=""
      form={{
        submitter: { render: (_, dom) => reverse(dom) },
      }}
      pagination={{
        showQuickJumper: true,
        defaultPageSize: 10,
        showSizeChanger: true,
      }}
      scroll={{ x: 1300, y: 500, ...(props.scroll ?? {}) }}
      search={{ defaultColsNumber: 3, defaultCollapsed: false, labelWidth: 120, ...(props.search ?? {}) }}
      options={{ setting: true, density: false, reload: false }}
      rowKey={rowKey}
      tableStyle={props.headerTitle == undefined ? { paddingTop: 16 } : {}}
      request={async (params, sort, filter) => {
        let result = {};
        if (needPage) {
          const current = params?.current;
          const res = await requestPage?.({ ...params, pageNo: current }, sort, filter);
          result = { success: true, data: res?.data, total: res?.total };
        } else {
          const res = await requestList?.(params, sort, filter);
          result = { success: true, data: res?.data };
        }
        return result;
      }}
      {...props}
    />
    /* {bottomLeft && (
        <div className="absolute left-[24px] bottom-[-20px] flex flex-row items-center m-6 z-50">
          {bottomLeft}
        </div>
      )} */
    // </ProCard>
  );
};

export default FunProTable;
