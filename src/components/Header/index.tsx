import logo from '@/assets/imgs/logo.svg';
import Avatar from '@/components/Avatar';
import { history, SelectLang } from '@umijs/max';
import { Image, Space } from 'antd';

const Header = () => {

  return (
    <div className="flex justify-between items-center h-full bg-white shadow pl-[30px] pr-[30px]">
      <Image
        onClick={() => {
          history.push('/');
        }}
        className="cursor-pointer"
        preview={false}
        src={logo}
        width={130}

      />
      <Space>
        <SelectLang />
        <Avatar />
      </Space>
    </div>
  );
};

export default Header;
