import { history, useAppData, useLocation, useSelectedRoutes } from '@umijs/max';
import { useEffect, useState } from 'react';

interface MatchRouteType {
  title: string;
  pathname: string; //  /user/1
  search?: string;
  routePath: string; // /user/:id
  icon?: any;
}

export function useMatchRoute() {
  // 获取匹配到的路由
  const selectedRoutes = useSelectedRoutes();
  // 获取所有路由
  const { routes } = useAppData();
  // 获取当前url
  const { pathname, search } = useLocation();

  const [matchRoute, setMatchRoute] = useState<MatchRouteType | undefined>();

  // 处理菜单名称
  const getMenuTitle = (lastRoute: any) => {
    return lastRoute.route.name;
  };

  // 监听pathname变了，说明路由有变化，重新匹配，返回新路由信息
  useEffect(() => {
    // 获取当前匹配的路由
    const lastRoute = selectedRoutes.at(-1);

    if (!lastRoute?.route?.path) return;

    const routeDetail = routes[(lastRoute.route as any).id];

    // 如果匹配的路由需要重定向，这里直接重定向
    if (routeDetail?.redirect) {
      history.replace(routeDetail?.redirect);
      return;
    }

    // 获取菜单名称
    const title = getMenuTitle(lastRoute);

    setMatchRoute({
      title,
      pathname,
      search,
      routePath: lastRoute.route.path,
      icon: (lastRoute.route as any).icon, // icon是拓展出来的字段
    });
  }, [pathname, search]);

  return matchRoute;
}
