import { MenuTreeEntity } from '@/pages/system/role/list/types/menu.tree.entity';
import { createIcon } from '@/utils/IconUtil';
import { MenuDataItem } from '@umijs/route-utils';
import { RouteType } from '../../config/routes';

/**
 * 扁平化本地route树
 */
export const flattenRouteTree = (tree: RouteType[], parentPath = ''): RouteType[] => {
  const result: RouteType[] = [];
  const flatten = (nodes: RouteType[]) => {
    nodes.forEach((item) => {
      const { routes, ...rest } = item;
      result.push(rest);
      if (routes) {
        flatten(routes);
      }
    });
  };
  flatten(tree);
  return result;
};

/**
 * 扁平化最终菜单树
 */
export const flattenMenuTree = (tree: MenuDataItem[]) => {
  const result: MenuDataItem[] = [];
  const flatten = (nodes: MenuDataItem[]) => {
    nodes.forEach((item) => {
      const { children, ...rest } = item;
      result.push(rest);
      if (children) {
        flatten(children);
      }
    });
  };
  flatten(tree);
  return result;
};

/**
 * 把本地route结构转化成menu结构
 * @param tree
 */
export const transformRouteToMenu = (tree: RouteType[]): MenuDataItem[] => {
  return tree
    .filter((item) => !item.hideInMenu)
    .map((node) => {
      const { name, path, icon, routes, ...rest } = node;
      const transformedNode: MenuDataItem = {
        ...rest,
        label: name,
        key: path,
      };
      if (icon) {
        // @ts-ignore
        transformedNode.icon = createIcon(icon || '');
      }
      if (routes) {
        transformedNode.children = transformRouteToMenu(routes);
      }
      return transformedNode;
    });
};

/**
 * 把远端menu数据转化成系统menu结构，经过本地route过滤
 */
export const transformRemoteMenuToMenu = (
  localRoute: RouteType[],
  remoteMenu: MenuTreeEntity[],
): MenuDataItem[] => {
  const flattenRoute = flattenRouteTree(localRoute);
  return remoteMenu
    .filter((node) => {
      const existRoute = flattenRoute.find((m) => m.path === node.path);
      if (!existRoute || existRoute.hideInMenu) {
        return false;
      }
      return true;
    })
    .map((node) => {
      const { name, path, children, ...rest } = node;
      const existRoute = flattenRoute.find((m) => m.path === node.path);
      const transformedNode: MenuDataItem = {
        ...rest,
        label: name,
        key: path,
      };
      if (existRoute?.icon) {
        // @ts-ignore
        transformedNode.icon = createIcon(existRoute?.icon || '');
      }
      if (children) {
        transformedNode.children = transformRemoteMenuToMenu(localRoute, children);
      }
      return transformedNode;
    });
};
