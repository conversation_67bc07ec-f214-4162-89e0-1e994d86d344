import { createExportTask } from '@/services/systerm';
import { history } from '@@/core/history';
import { Modal } from 'antd';

export interface ExportDataProps {
  moduleId: string;
  systemId: string;
  taskDesc: string;
  params?: any;
  intl?: any; // 国际化对象
}

export const exportData = (props: ExportDataProps) => {
  const { params = {}, intl, ...rest } = props;
  const formatParams: any[] = [];
  for (let key in params) {
    if (typeof params[key] !== 'undefined') {
      if (Array.isArray(params[key])) {
        formatParams.push({
          key,
          values: params[key],
        });
      } else {
        formatParams.push({
          key,
          value: params[key]?.toString(),
        });
      }
    }
  }
  createExportTask({
    ...rest,
    params: formatParams,
  }).then((result) => {
    if (result?.taskId) {
      Modal.success({
        title: intl ? intl.formatMessage({ id: 'common.export.title' }) : '提示',
        centered: true,
        content: intl ? intl.formatMessage({ id: 'common.export.content' }) : '导出任务已经创建成功，是否查看导出结果？',
        okText: intl ? intl.formatMessage({ id: 'common.export.okText' }) : '前往查看',
        onOk: () => {
          history.push('/system/job?jobType=Export');
        },
        closable: true,
      });
    }
  });
};
