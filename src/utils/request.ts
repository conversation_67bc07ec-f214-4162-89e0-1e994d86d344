import axios from 'axios';
import { clearCookies, getSign } from '@/access';
import Cookies from 'js-cookie';
import { includes } from 'lodash';
import { message } from 'antd';
import { history } from '@umijs/max';

const MAX_RETRY_COUNT = 3;

// 创建axios实例
const request = axios.create({
  timeout: 10 * 1000,
  baseURL: '/apigateway',
  method: 'POST',
  headers: {
    'X-Requested-With': 'XMLHttpRequest',
    'Content-Type': 'application/x-www-form-urlencoded',
  },
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // @ts-ignore
    const data = config.originData ?? config.data;
    const timestamp = new Date().getTime();
    const sign = getSign({
      appkey: document.domain.startsWith('r.etc-parts.com') ? '2105731' : '8808181',
      params: data,
      timestamp,
    });
    const urlencoded = new URLSearchParams();
    // 文件上传直接发起请求
    if (data instanceof FormData) {
      return config;
    }

    if (data?.current) {
      data.pageNo = data.current;
      delete data.current;
    }
    urlencoded.append(
      'appkey',
      document.domain.startsWith('r.etc-parts.com') ? '2105731' : '8808181',
    );
    urlencoded.append('timestamp', timestamp + '');
    urlencoded.append('sign', sign);
    urlencoded.append('data', JSON.stringify(data));
    return { ...config, data: urlencoded, originData: config.data };
  },
  (error) => {
    // 请求错误处理
    console.log(error); // for debug
    Promise.reject(error);
  },
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const { data } = response;
    const { code, msg } = data;

    if (code === -9580106) {
      // 数据签名异常：请刷新后重试！
      Cookies.set('%24RM_signature', data.data.secret);
      // @ts-ignore
      const retryCount = response.config?.retryCount || 0;
      if (retryCount < MAX_RETRY_COUNT) {
        return request(response.config.url as string, {
          ...response.config,
          // @ts-ignore
          data: response.config.originData,
          // @ts-ignore
          retryCount: retryCount + 1,
        });
      }
    } else {
      if (code !== 0) {
        if (includes([200, 100022, 100023, 100024, 100013], code)) {
          //登录失效
          clearCookies();
          // token异常 过期或者没有权限
          history.push('/login');
          if (includes([100022, 100023, 100024, 100013], code)) {
            message.error(msg);
          }
        } else if (code == 100001) {
          clearCookies();
          history.push('/login?first=YES');
        } else {
          message.error(msg);
        }
      }
      // @ts-ignore
      if (response.config?.origin) {
        return data;
      } else {
        return data.data;
      }
    }
  },
  (error) => {
    console.log('err' + error); // for debug
    return Promise.reject(error);
  },
);

export { request };
