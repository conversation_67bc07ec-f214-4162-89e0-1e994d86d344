import downloadIcon from '@/assets/icons/icon_download.png';
import importIcon from '@/assets/icons/icon_import.png';
import { createImportTask, downloadFile, queryTaskResult } from '@/services/systerm';
import { history } from '@@/core/history';
import { Button, Image, message, Modal, Upload } from 'antd';

export interface ImportDataProps {
  moduleId: string;
  systemId: string;
  taskDesc: string;
  params?: any;
  downloadFileName?: string;
  onSuccess?: () => void; // 传入此参数是视为同步导入
  intl?: any; // 国际化对象
}

export const importData = (props: ImportDataProps) => {
  const { params = {}, downloadFileName, onSuccess, intl, ...rest } = props;
  /**
   * 上传OSS成功事件
   * @param url
   */
  const handleUploadOssSuccess = (url: string) => {
    const formatParams: any[] = [
      {
        key: 'url',
        value: url,
      },
    ];
    for (let key in params) {
      if (typeof params[key] !== 'undefined') {
        if (Array.isArray(params[key])) {
          formatParams.push({
            key,
            values: params[key],
          });
        } else {
          formatParams.push({
            key,
            value: params[key]?.toString(),
          });
        }
      }
    }
    createImportTask({ ...rest, params: formatParams }).then((result) => {
      if (result?.taskId) {
        importModal?.destroy?.();
        if (onSuccess) {
          const modal = Modal.info({
            title: intl ? intl.formatMessage({ id: 'common.import.processingTitle' }) : '提示',
            centered: true,
            content: intl ? intl.formatMessage({ id: 'common.import.processingContent' }) : '正在导入中，请稍等',
            closable: true,
            footer: false,
            onCancel: () => {
              clearInterval(timer);
            },
          });
          const timer = setInterval(() => {
            queryTaskResult(result?.taskId, rest.systemId).then((result) => {
              if (result?.taskResultInfo?.successAmount) {
                message.success(intl ? intl.formatMessage({ id: 'common.import.successMessage' }) : '导入成功');
                modal.destroy();
                clearInterval(timer);
                onSuccess();
              } else if (result?.taskResultInfo?.info) {
                clearInterval(timer);
                modal.destroy();
                message.error(result?.taskResultInfo?.info);
              }
            });
          }, 1000);
        } else {
          Modal.success({
            title: intl ? intl.formatMessage({ id: 'common.import.successTitle' }) : '提示',
            centered: true,
            content: intl ? intl.formatMessage({ id: 'common.import.successContent' }) : '导入任务已经创建成功，是否查看导入结果？',
            okText: intl ? intl.formatMessage({ id: 'common.import.successOkText' }) : '前往查看',
            onOk: () => {
              history.push('/system/job?jobType=Import');
            },
            closable: true,
          });
        }
      }
    });
  };

  const importModal = Modal.info({
    title: intl ? intl.formatMessage({ id: 'common.import.title' }) : '批量导入',
    centered: true,
    footer: false,
    content: (
      <div>
        <div style={{ margin: '10px 0', color: '#888' }}>
          {intl ? intl.formatMessage({ id: 'common.import.description' }) : '请根据提供的模板进行数据导入'}
        </div>
        <div className="flex" style={{ paddingBottom: 20, marginLeft: -18 }}>
          {downloadFileName && (
            <Button
              type="text"
              className="flex items-center"
              onClick={() => downloadFile(downloadFileName)}
            >
              <Image src={downloadIcon} height={24} width={24} preview={false} />
              <span className="ml-[5px]">
                {intl ? intl.formatMessage({ id: 'common.import.downloadTemplate' }) : '下载模板'}
              </span>
            </Button>
          )}
          <Upload
            name="file"
            multiple={false}
            action="/apigateway/public/upload/object/batch"
            onChange={(info) => {
              const url = info.file?.response?.data?.[0];
              if (url) {
                handleUploadOssSuccess(url);
              }
            }}
          >
            <Button type="text" className="flex items-center">
              <Image src={importIcon} height={24} width={24} preview={false} />
              <span className="ml-[5px]">
                {intl ? intl.formatMessage({ id: 'common.import.importFile' }) : '导入文件'}
              </span>
            </Button>
          </Upload>
        </div>
      </div>
    ),
    closable: true,
  });
};
