export const transformCategoryTree = (tree: any) => {
  return tree.map((node: any) => ({
    label: node.categoryName,
    value: node.categoryId,
    text: node.categoryName,
    key: node.categoryId,
    children: node.sonList ? transformCategoryTree(node.sonList) : [],
  }));
};

export const flattenCategory = (tree: any[]) => {
  let result: { label: any; value: any }[] = [];
  function traverse(tree: any) {
    result.push({
      label: tree.categoryName,
      value: tree.categoryId,
    });
    if (tree.sonList?.length > 0) {
      tree.sonList.forEach(traverse);
    }
  }
  tree.forEach(traverse);
  return result;
};
