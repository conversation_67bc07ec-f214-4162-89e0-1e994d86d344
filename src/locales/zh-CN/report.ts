export default {
    finance: {
        title: '财务报表',
        overview: {
            fundIncome: '资金收入',
            fundExpenditures: '资金支出',
            earnedProfit: '经营利润',
        },
        charts: {
            incomeExpendituresTrend: '收支趋势',
            incomeExpendituresRatio: '收支占比统计',
            receivablePayableTrend: '应收应付趋势',
            fundIncomeChart: '资金收入',
            fundExpendituresChart: '资金支出',
        },
        trends: {
            fundIncome: '资金收入',
            fundExpenditures: '资金支出',
            receivableAmount: '应收金额',
            payableAmount: '应付金额',
        },
        form: {
            store: '门店',
            startTime: '开始时间',
            endTime: '结束时间',
            query: '查询',
            reset: '重置',
        },
        axis: {
            yuan: '元',
        },
    },
    sales: {
        title: '销售报表',
        overview: {
            saleAmount: '销售金额',
            costAmount: '销售成本',
            saleGrossProfit: '销售毛利',
            grossProfitRate: '毛利率',
            saleOrderNum: '销售单数',
            saleCustomerNum: '销售客户数',
        },
        trends: {
            saleAmount: '销售额',
            costAmount: '销售成本',
            saleGrossProfit: '销售毛利',
            grossProfitRate: '毛利率',
            saleOrderNum: '销售单数',
            saleCustomerNum: '销售客户数',
        },
        charts: {
            salesTrend: '销售趋势',
            customerStatistics: '按客户统计',
            goodsStatistics: '按商品统计',
            staffStatistics: '按员工统计',
            brandRatio: '品牌占比',
            categoryRatio: '分类占比',
        },
        form: {
            store: '门店',
            customer: '客户',
            goodsInfo: '商品信息',
            goodsInfoPlaceholder: '请输商品名称/编码',
            brand: '品牌',
            category: '商品分类',
            employee: '员工',
            startTime: '开始时间',
            endTime: '结束时间',
            query: '查询',
            reset: '重置',
        },
        radio: {
            saleAmount: '销售金额',
            saleNum: '销售数量',
            saleGrossProfit: '销售毛利',
        },
        table: {
            customer: {
                index: '序号',
                customerName: '客户名称',
                saleAmount: '销售金额',
                costAmount: '销售成本',
                saleGrossProfit: '销售毛利',
                grossProfitRate: '毛利率',
                saleOrderNum: '销售单数',
                perCstTrans: '客单价',
            },
            goods: {
                index: '序号',
                itemName: '商品名称',
                brandName: '品牌',
                categoryName: '分类',
                saleAmount: '销售金额',
                costAmount: '销售成本',
                saleGrossProfit: '销售毛利',
                grossProfitRate: '毛利率',
                saleSaleNum: '销售数量',
                avgSaleAmount: '销售均价',
            },
            staff: {
                index: '序号',
                employeeName: '员工名称',
                saleAmount: '销售额',
                costAmount: '销售成本',
                saleGrossProfit: '销售毛利',
                grossProfitRate: '毛利率',
                saleOrderNum: '销售单数',
                perCstTrans: '客单价',
            },
        },
        axis: {
            yuan: '元',
            percent: '%',
        },
    },
    purchase: {
        title: '采购报表',
        overview: {
            purchaseAmount: '采购金额',
            purchaseOrderNum: '采购单数',
            purchaseNum: '采购数量',
        },
        charts: {
            purchaseTrend: '采购趋势',
            supplierStatistics: '按供应商统计',
            goodsStatistics: '按商品统计',
            brandChart: '品牌',
            categoryChart: '商品分类',
        },
        trends: {
            purchaseAmount: '采购金额',
            purchaseNum: '采购数量',
        },
        form: {
            store: '门店',
            supplier: '供应商',
            goodsInfo: '商品信息',
            goodsInfoPlaceholder: '请输商品名称/编码',
            brand: '品牌',
            category: '商品分类',
            startTime: '开始时间',
            endTime: '结束时间',
            query: '查询',
            reset: '重置',
        },
        radio: {
            purchaseAmount: '采购金额',
            purchaseNum: '采购数量',
        },
        table: {
            index: '序号',
            supplierName: '供应商名称',
            purchaseAmount: '采购金额',
            purchaseOrderNum: '采购单数',
            purchaseNum: '采购数量',
            itemName: '商品名称',
            brandName: '品牌',
            categoryName: '分类',
            avgPurchaseAmount: '采购均价',
        },
        axis: {
            yuan: '元',
        },
    },
    inventory: {
        title: '库存报表',
        overview: {
            inNum: '入库数量',
            inAmount: '入库金额',
            outNum: '出库数量',
            outAmount: '出库金额',
            stockTurnDays: '平均库存周转天数',
        },
        charts: {
            stockTurnDaysDistribution: '库存周转天数分布',
            stockTurnDaysFormula: '库存周转天数=（期初库存成本余额+期末库存成本余额）/2/日均销售成本',
            inventoryChangeStatistics: '库存变动统计',
        },
        form: {
            warehouse: '仓库',
            goodsInfo: '商品信息',
            goodsInfoPlaceholder: '请输商品名称/编码',
            brand: '品牌',
            category: '商品分类',
            startTime: '开始时间',
            endTime: '结束时间',
            query: '查询',
            reset: '重置',
        },
        table: {
            index: '序号',
            itemName: '商品名称',
            brandName: '品牌',
            categoryName: '分类',
            stockTurnDays: '库存周转天数',
            saleOutNum: '销售数量',
            saleInNum: '退货数量',
            purchaseInNum: '采购数量',
            purchaseOutNum: '采购退货数',
            checkGainNum: '盘盈数量',
            checkLossNum: '盘亏数量',
            saleOutAmount: '销售金额',
            saleInAmount: '退货金额',
            purchaseInAmount: '采购金额',
            purchaseOutAmount: '采购退货金额',
            checkGainAmount: '盘盈金额',
            checkLossAmount: '盘亏金额',
        },
    },
};
