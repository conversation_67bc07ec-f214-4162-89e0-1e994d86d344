export default {
    collection: {
        title: '应收管理',
        detail: '应收详情',
        detailTitle: '应收明细',
        totalReceivableAmount: '应收总金额',
        onlyShowReceivable: '仅看存在应收',
        onlyShowRemaining: '仅看剩余金额大于0',
        exportDescription: '零售商应收管理数据导出',
        exportDetailDescription: '零售商应收管理明细数据导出',
        columns: {
            customer: '客户',
            store: '销售门店',
            overdueStatus: '逾期状态',
            receivableAmount: '应收金额',
            overdueAmount: '逾期金额',
            totalAmount: '总额度',
            availableAmount: '剩余额度',
            creditTerms: '账期(天)',
            arrearsDay: '欠款时间(天)',
            remainTerms: '剩余账期(天)',
            businessOrderNo: '业务单号',
            businessCompleteTime: '业务完成时间',
            orderAmount: '单据金额',
            receivedAmount: '已收金额',
            remainReceivableAmount: '应收金额',
            customerCode: '客户编码',
            contact: '联系人',
            contactPhone: '联系电话',
            contactAddress: '联系地址',
        },
        summary: {
            orderTotal: '单据总额',
            receivedTotal: '已收总额',
            receivableTotal: '应收总额',
            overdueTotal: '逾期总额',
        },
    },
    receive: {
        title: '收款管理',
        detail: '收款详情',
        add: '新增收款',
        confirm: '确认收款',
        autoAssign: '自动分配',
        receivedAmount: '收款金额',
        currentWriteOff: '本次核销总计',
        writeOffOrder: '核销订单',
        writeOffAmountMismatch: '本次核销合计需等于收款金额',
        noAmountError: '收款金额不能为空',
        negativeAmountError: '收款金额小于等于0，请手动分配核销金额',
        noOrderSelectedWarning: '本次收款尚未选择具体的订单，请检查！',
        amountMismatchError: '各订单收款总金额与输入收款金额不一致，请修改！',
        columns: {
            serialNumber: '收款单号',
            store: '收款门店',
            receiveTime: '收款时间',
            customer: '客户',
            customerName: '客户名称',
            receivedAccount: '收款账户',
            receivedAmount: '收款金额',
            creator: '制单人',
            remark: '收款备注',
            receiveType: '收款类型',
            storeName: '门店名称',
            businessOrderNo: '业务单号',
            orderCompleteTime: '订单完成时间',
            transactionCompleteTime: '交易完成时间',
            orderAmount: '订单金额',
            receivedAmountPaid: '已收金额',
            unreceived: '未收金额',
            currentWriteOff: '本次核销',
            writeOffAmount: '核销金额',
        },
        placeholders: {
            businessOrderNo: '业务单号',
            enterAmount: '请输入',
        },
    },
    payment: {
        title: '应付管理',
        detail: '应付详情',
        detailTitle: '应付明细',
        totalPayableAmount: '应付总金额',
        onlyShowPayable: '仅看存在应付',
        onlyShowRemaining: '仅看剩余金额大于0',
        exportDescription: '零售商应付管理数据导出',
        exportDetailDescription: '零售商应付管理明细数据导出',
        columns: {
            supplier: '供应商',
            supplierCode: '供应商编码',
            store: '采购门店',
            payableAmount: '应付金额',
            businessOrderNo: '业务单号',
            businessCompleteTime: '业务完成时间',
            orderAmount: '单据金额',
            paidAmount: '已付金额',
            remainPayableAmount: '应付金额',
            contact: '联系人',
            contactPhone: '联系电话',
            contactAddress: '联系地址',
        },
        summary: {
            orderTotal: '单据总额',
            paidTotal: '已付总额',
            payableTotal: '应付总额',
        },
    },
    supplierPayment: {
        title: '供应商付款',
        detail: '付款详情',
        add: '新增付款',
        confirm: '确认付款',
        autoAssign: '自动分配',
        paymentAmount: '付款金额',
        currentWriteOff: '本次核销总计',
        writeOffOrder: '核销订单',
        writeOffAmountMismatch: '本次核销合计需等于付款金额',
        noAmountError: '付款金额不能为空且必须大于0元！',
        negativeAmountError: '付款金额小于等于0，请手动分配核销金额',
        noOrderSelectedWarning: '本次付款尚未选择具体的单据，请检查！',
        amountMismatchError: '各单据付款总金额与输入付款金额不一致，请修改！',
        columns: {
            serialNumber: '付款单号',
            paymentStore: '付款门店',
            paymentTime: '付款时间',
            supplier: '供应商',
            supplierName: '供应商名称',
            paymentAccount: '付款账户',
            paymentAmount: '付款金额',
            creator: '制单人',
            remark: '付款备注',
            storeName: '门店名称',
            businessOrderNo: '业务单号',
            businessCompleteTime: '业务完成时间',
            orderAmount: '单据金额',
            paidAmount: '已付金额',
            unpaidAmount: '未付金额',
            currentWriteOff: '本次核销',
            writeOffAmount: '核销金额',
        },
        placeholders: {
            businessOrderNo: '业务单号',
            enterAmount: '请输入',
        },
    },
    cost: {
        title: '收支管理',
        income: '收入',
        expend: '支出',
        otherIncome: '其他收入',
        otherExpend: '其他支出',
        add: '新增',
        invalidateSuccess: '作废成功',
        confirmInvalidate: '确认作废吗',
        invalidate: '作废',
        columns: {
            store: '门店',
            incomeType: '收入类型',
            expendType: '支出类型',
            createTime: '制单时间',
            serialNumber: '收支单号',
            incomeStore: '收款门店',
            expendStore: '付款门店',
            incomeAmount: '收款金额',
            expendAmount: '付款金额',
            settlementAccount: '结算账户',
            creator: '制单人',
            incomeRemark: '收款备注',
            expendRemark: '付款备注',
        },
        form: {
            incomeStore: '收款门店',
            expendStore: '付款门店',
            incomeType: '收入类型',
            expendType: '支出类型',
            incomeAmount: '收款金额',
            expendAmount: '付款金额',
            settlementAccount: '结算账户',
            incomeRemark: '收款备注',
            expendRemark: '付款备注',
            yuan: '元',
        },
    },
    flow: {
        title: '资金流水',
        totalIncome: '总收入',
        totalExpend: '总支出',
        columns: {
            businessOrderNo: '业务单号',
            occurTime: '发生时间',
            customerOrSupplier: '客户/供应商',
            incomeExpendType: '收支类型',
            store: '门店',
            settlementAccount: '结算账户',
            income: '收入',
            expend: '支出',
        },
    },
    customer: {
        title: '账户管理',
        addAccount: '新增账户',
        editAccount: '编辑账户',
        confirmDelete: '确认删除吗?',
        columns: {
            accountName: '账户名称',
            bankName: '开户银行',
            bankCardNumber: '银行卡号',
            belongToStore: '所属门店',
            accountBalance: '账户余额',
            initialBalance: '期初余额',
        },
        form: {
            accountName: '账户名称',
            bankName: '开户银行',
            bankCardNumber: '银行卡号',
            belongToStore: '所属门店',
            initialBalance: '期初余额',
        },
    },
    tag: {
        title: '收支类型管理',
        incomeExpenseType: '收支类型',
        addIncomeExpenseType: '新增收支类型',
        editIncomeExpenseType: '编辑收支类型',
        columns: {
            incomeExpenseType: '收支类型',
            incomeDirection: '收入方向',
            source: '来源',
            isEnabled: '是否启用',
        },
        form: {
            incomeExpenseType: '收支类型',
            incomeExpenseDirection: '收支方向',
            income: '收入',
            expense: '支出',
            pleaseInput: '请输入',
        },
    },
};