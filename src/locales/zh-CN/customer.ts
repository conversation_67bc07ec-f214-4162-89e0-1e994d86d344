export default {
    customerList: {
        createModal: {
            titleAdd: '新增客户',
            titleEdit: '客户编辑',
        },
        button: {
            addCustomer: '新增客户',
        },
        detailModal: {
            title: '客户详情',
        },
        table: {
            column: {
                customerSn: '客户编码',
                customerName: '客户名称',
                nickName: '客户简称',
                customerTags: '客户标签',
                storeName: '归属门店',
                salesmanName: '业务员',
                allowCredit: '支持挂账',
                creditLimit: '挂账额度',
                'creditLimit.used': '已用',
                'creditLimit.available': '/可用',
                creditTerms: '账期(天)',
                receivableAmount: '应收金额',
                defaultContactName: '默认联系人',
                defaultContactPhone: '默认联系方式',
                defaultAddress: '默认客户地址',
                createTime: '创建时间',
            },
            popconfirm: {
                enableDisable: '确认{operType}吗?',
            },
            search: {
                customerTags: '客户标签',
                store: '归属门店',
                customerInfo: '客户信息',
                'customerInfo.placeholder': '客户编码/客户名称/客户简称',
                contactName: '联系人',
                contactPhone: '联系方式',
                createTime: '创建时间',
            },
        },
        import: {
            taskDesc: '零售商客户导入',
        },
        export: {
            taskDesc: '零售商客户导出',
        },
        amountHistoryModal: {
            title: '额度调整记录',
            operatorLabel: '操作人：',
            operationTypeLabel: '操作类型：',
            operationContentLabel: '操作内容：',
        },
        createForm: {
            group: {
                baseInfo: '基本信息',
                settlementInfo: '结算信息',
                contactInfo: '联系人信息',
                addressInfo: '地址信息',
            },
            label: {
                customerName: '客户名称',
                customerSn: '客户编码',
                nickName: '客户简称',
                customerTags: '客户标签',
                store: '归属门店',
                salesman: '业务员',
                remark: '备注',
                customerPhoto: '客户照片',
                billingUnit: '开票单位',
                taxNo: '纳税识别号',
                bankName: '开户行名称',
                accountNo: '开户行账号',
                billingAddress: '开票地址',
                billingPhone: '开票电话',
                initialReceivable: '期初应收',
                allowCredit: '允许挂账',
                creditLimit: '挂账额度',
                creditTerms: '账期',
            },
            tooltip: {
                creditLimit: '挂账额度',
                creditTerms: '账期',
            },
            button: {
                addContact: '新增联系人',
                addAddress: '新增地址',
            },
            contactTable: {
                column: {
                    isDefaultContact: '默认联系人',
                    contactName: '联系人',
                }
            },
            addressTable: {
                column: {
                    isDefaultAddress: '默认地址',
                    provinceCityDistrict: '省市区',
                    operation: '操作',
                    detailAddress: '详细地址',
                    contactName: '联系人',
                    contactPhone: '联系人方式',
                },
            },
        },
        detailForm: {
            status: {
                enabled: '启用',
                disabled: '禁用',
            },
            label: {
                customerSn: '客户编码：',
                nickName: '客户简称：',
                customerTags: '客户标签：',
                store: '归属门店：',
                salesman: '业务员：',
                createTime: '创建时间：',
                remark: '客户备注：',
            },
            group: {
                contactInfo: '联系人信息',
                addressInfo: '地址信息',
                settlementInfo: '结算信息',
            },
            tag: {
                defaultContact: '默认联系人',
                defaultAddress: '默认地址',
            },
            contact: {
                label: {
                    position: '职务：',
                    phone: '联系方式：',
                    qq: 'QQ：',
                    wechat: '微信：',
                    email: '邮箱：',
                    remark: '备注：',
                },
            },
            address: {
                label: {
                    phone: '联系方式：',
                    area: '所在地区：',
                    detailAddress: '详细地址：',
                    contactName: '联系人：',
                },
            },
            link: {
                viewAmountHistory: '查看额度调整记录',
            },
            settlement: {
                label: {
                    initialReceivable: '期初应收：',
                    allowCredit: '支持挂账：',
                    creditLimit: '挂账额度：',
                    used: '已用',
                    frozen: '，冻结',
                    available: '，可用',
                    creditTerms: '账期：',
                    remainingStart: '（剩余',
                    remainingEnd: '天）',
                },
            },
            billing: {
                label: {
                    billingUnit: '开票单位：',
                    taxNo: '纳税识别号：',
                    bankName: '开户行名称：',
                    accountNo: '开户行账号：',
                    phone: '开票电话：',
                    address: '开票地址：',
                },
            },
        },
        contactTable: {
            column: {
                phone: '联系方式',
                position: '职务',
                qq: 'QQ',
                wechat: '微信',
                email: '邮箱',
                remark: '备注',
            },
        },
    },
    customerProperty: {
        label: {
            customerTag: '客户标签',
            tag: '标签',
        },
        createModal: {
            titleAddTag: '新增标签',
            titleAddTagSuffix: '标签添加',
        },
        tab: {
            customerTag: '客户标签',
        },
        button: {
            addTag: '新增标签',
        },
        tagTable: {
            column: {
                tagName: '客户标签',
                source: '来源',
            },
            search: {
                tagName: '客户标签',
            },
        },
        priceLevel: {
            column: {
                name: '价格级别',
                source: '来源',
            },
        },
    },
};
