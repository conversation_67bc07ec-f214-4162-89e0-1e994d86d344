export default {
    customerList: {
        createModal: {
            titleAdd: 'Add Customer',
            titleEdit: 'Edit Customer',
        },
        button: {
            addCustomer: 'Add Customer',
        },
        detailModal: {
            title: 'Customer Details',
        },
        table: {
            column: {
                customerSn: 'Customer Code',
                customerName: 'Customer Name',
                nickName: 'Customer Nickname',
                customerTags: 'Customer Tags',
                storeName: 'Store',
                salesmanName: 'Salesperson',
                allowCredit: 'Allow Credit',
                creditLimit: 'Credit Limit',
                'creditLimit.used': 'Used',
                'creditLimit.available': '/Available',
                creditTerms: 'Credit Terms (days)',
                receivableAmount: 'Receivable Amount',
                defaultContactName: 'Default Contact',
                defaultContactPhone: 'Default Contact Phone',
                defaultAddress: 'Default Address',
                createTime: 'Creation Time',
            },
            popconfirm: {
                enableDisable: 'Confirm {operType}?',
            },
            search: {
                customerTags: 'Customer Tags',
                store: 'Store',
                customerInfo: 'Customer Info',
                'customerInfo.placeholder': 'Customer Code/Name/Nickname',
                contactName: 'Contact Name',
                contactPhone: 'Contact Phone',
                createTime: 'Creation Time',
            },
        },
        import: {
            taskDesc: 'Retail Customer Import',
        },
        export: {
            taskDesc: 'Retail Customer Export',
        },
        amountHistoryModal: {
            title: 'Credit Adjustment History',
            operatorLabel: 'Operator:',
            operationTypeLabel: 'Operation Type:',
            operationContentLabel: 'Operation Content:',
        },
        createForm: {
            group: {
                baseInfo: 'Basic Info',
                billingInfo: 'Billing Info',
                settlementInfo: 'Settlement Info',
                contactInfo: 'Contact Info',
                addressInfo: 'Address Info',
            },
            label: {
                customerName: 'Customer Name',
                customerSn: 'Customer Code',
                nickName: 'Customer Nickname',
                customerTags: 'Customer Tags',
                store: 'Store',
                salesman: 'Salesperson',
                remark: 'Remarks',
                customerPhoto: 'Customer Photo',
                billingUnit: 'Billing Unit',
                taxNo: 'Tax ID',
                bankName: 'Bank Name',
                accountNo: 'Bank Account',
                billingAddress: 'Billing Address',
                billingPhone: 'Billing Phone',
                initialReceivable: 'Initial Receivable',
                allowCredit: 'Allow Credit',
                creditLimit: 'Credit Limit',
                creditTerms: 'Credit Terms',
            },
            tooltip: {
                creditLimit: 'Credit Limit',
                creditTerms: 'Credit Terms',
            },
            button: {
                addContact: 'Add Contact',
                addAddress: 'Add Address',
            },
            contactTable: {
                column: {
                    isDefaultContact: 'Default Contact',
                    contactName: 'Contact Name',
                }
            },
            addressTable: {
                column: {
                    isDefaultAddress: 'Default Address',
                    provinceCityDistrict: 'Province/City/District',
                    operation: 'Operation',
                    detailAddress: 'Detailed Address',
                    contactName: 'Contact Name',
                    contactPhone: 'Contact Phone',
                },
            },
        },
        detailForm: {
            status: {
                enabled: 'Enabled',
                disabled: 'Disabled',
            },
            label: {
                customerSn: 'Customer Code:',
                nickName: 'Customer Nickname:',
                customerTags: 'Customer Tags:',
                store: 'Store:',
                salesman: 'Salesperson:',
                createTime: 'Creation Time:',
                remark: 'Customer Remarks:',
            },
            group: {
                contactInfo: 'Contact Info',
                addressInfo: 'Address Info',
                settlementInfo: 'Settlement Info',
                billingInfo: 'Billing Info',
            },
            tag: {
                defaultContact: 'Default Contact',
                defaultAddress: 'Default Address',
            },
            contact: {
                label: {
                    position: 'Position:',
                    phone: 'Phone:',
                    qq: 'QQ:',
                    wechat: 'WeChat:',
                    email: 'Email:',
                    remark: 'Remarks:',
                },
            },
            address: {
                label: {
                    phone: 'Phone:',
                    area: 'Area:',
                    detailAddress: 'Detailed Address:',
                    contactName: 'Contact Name:',
                },
            },
            link: {
                viewAmountHistory: 'View Credit Adjustment History',
            },
            settlement: {
                label: {
                    initialReceivable: 'Initial Receivable:',
                    allowCredit: 'Allow Credit:',
                    creditLimit: 'Credit Limit:',
                    used: 'Used',
                    frozen: ', Frozen',
                    available: ', Available',
                    creditTerms: 'Credit Terms:',
                    remainingStart: '(Remaining ',
                    remainingEnd: ' days)',
                },
            },
            billing: {
                label: {
                    billingUnit: 'Billing Unit:',
                    taxNo: 'Tax ID:',
                    bankName: 'Bank Name:',
                    accountNo: 'Bank Account:',
                    phone: 'Billing Phone:',
                    address: 'Billing Address:',
                },
            },
        },
        contactTable: {
            column: {
                phone: 'Phone',
                position: 'Position',
                qq: 'QQ',
                wechat: 'WeChat',
                email: 'Email',
                remark: 'Remarks',
            },
        },
    },
    customerProperty: {
        label: {
            customerTag: 'Customer Tag',
            tag: 'Tag',
        },
        createModal: {
            titleAddTag: 'Add Tag',
            titleAddTagSuffix: 'Tag Addition',
        },
        tab: {
            customerTag: 'Customer Tag',
        },
        button: {
            addTag: 'Add Tag',
        },
        tagTable: {
            column: {
                tagName: 'Customer Tag',
                source: 'Source',
            },
            search: {
                tagName: 'Customer Tag',
            },
        },
        priceLevel: {
            column: {
                name: 'Price Level',
                source: 'Source',
            },
        },
    },
};