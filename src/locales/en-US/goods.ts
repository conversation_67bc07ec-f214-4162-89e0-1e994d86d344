export default {
    'property.inputFieldLabel.brandName': 'Brand Name',
    'property.inputFieldLabel.unitName': 'Unit Name',
    'property.inputFieldLabel.categoryName': 'Category Name',
    'property.inputFieldLabel.originRegionName': 'Origin Region Name',
    'property.inputFieldLabel.priceLevelName': 'Price Level Name',
    'property.button.brand': 'Brand',
    'property.button.category': 'Category',
    'property.button.unit': 'Unit',
    'property.button.originRegionRelation': 'Origin Region',
    'property.button.priceLevel': 'Price Level',
    'property.tab.brandManagement': 'Brand Management',
    'property.tab.categoryManagement': 'Category Management',
    'property.tab.unitManagement': 'Unit Management',
    'property.tab.originRegionRelationManagement': 'Origin Region Management',
    'property.tab.priceLevelManagement': 'Price Level Management',
    'property.modal.title.add': 'Add {name}',
    'property.modal.title.addSuffix': '{name} Add',
    'property.common.index': 'Index',
    'property.common.source': 'Source',
    'property.common.status': 'Status',
    'property.formModal.placeholder': 'Can be added in bulk, one line per {label}',
    'list.createGoodsTitle': 'Add New Item',
    'list.editGoodsTitle': 'Edit Item',
    'list.createGoodsButton': 'Add New Item',
    'list.importGoodsButton': 'Import Items',
    'list.exportGoodsTaskDesc': 'Retailer Item Export',
    'list.exportGoodsButton': 'Export Items',
    'list.onlineButton': 'Enable',
    'list.offlineButton': 'Disable',
    'createForm.addBrandSuccess': 'Brand 【{brandName}】 added successfully!',
    'createForm.inputBrandNameRequired': 'Please enter brand name!',
    'createForm.baseInfoTitle': 'Basic Information',
    'createForm.oeLabel': 'OE',
    'createForm.brandPartNoLabel': 'Brand Part No.',
    'createForm.itemNameLabel': 'Item Name',
    'createForm.itemSnLabel': 'Item Code',
    'createForm.memCodeLabel': 'Mnemonic Code',
    'createForm.brandLabel': 'Brand',
    'createForm.searchEmpty': 'Search is empty',
    'createForm.addBrandAndSelectButton': 'Add Brand and Select',
    'createForm.categoryLabel': 'Item Category',
    'createForm.unitLabel': 'Item Unit',
    'createForm.specLabel': 'Item Specification',
    'createForm.originRegionLabel': 'Item Origin Region',
    'createForm.supplierLabel': 'Supplier',
    'createForm.adaptModelLabel': 'Applicable Models',
    'createForm.remarkLabel': 'Item Remark',
    'createForm.imagesLabel': 'Item Images',
    'createForm.priceInfoTitle': 'Price Information',
    'createForm.suggestPriceLabel': 'Sale Price',
    'createForm.suggestPriceTooltip': 'Suggested sale price for the item',
    'createForm.lowPriceLabel': 'Minimum Sale Price',
    'createForm.lowPriceTooltip': 'System will prompt if the item price is below the minimum sale price when creating an order',
    'createForm.purchasePriceLabel': 'Purchase Price',
    'createForm.purchasePriceTooltip': 'Reference purchase price for the item',
    'importModal.title': 'Batch Import',
    'importModal.createCustomGoodsTitle': 'Batch Create Custom Items',
    'importModal.createCustomGoodsDesc': 'You can import item information to batch create custom items',
    'importModal.importPriceTitle': 'Batch Import Item Prices',
    'importModal.importPriceDesc': 'You can use item codes to batch maintain item prices, including: sale price, minimum sale price, purchase price, and tiered pricing',
    'importModal.importSupplierRelationTitle': 'Batch Import Supplier and Item Relation',
    'importModal.importSupplierRelationDesc': 'You can import item codes and supplier names to batch add/delete supplier and item relations',
    'importModal.downloadTemplate': 'Download Template',
    'importModal.importFile': 'Import File',
    'importModal.taskCreatedTitle': 'Tip',
    'importModal.taskCreatedContent': 'Import task has been created successfully, do you want to view the import result?',
    'importModal.viewResultButton': 'Go to View',
    'list.table.itemSn': 'Item Code',
    'list.table.itemName': 'Item Name',
    'list.table.queryKeywordPlaceholder': 'Item Name/Code/OE/Brand Part No./Mnemonic Code',
    'list.table.queryKeywordTooltip': 'Item Name/Code/OE/Brand Part No./Mnemonic Code',
    'list.table.oeNos': 'OE No.',
    'list.table.brandPartNos': 'Brand Part No.',
    'list.table.brand': 'Brand',
    'list.table.brandName': 'Brand',
    'list.table.category': 'Category',
    'list.table.categoryName': 'Category',
    'list.table.unit': 'Unit',
    'list.table.spec': 'Specification',
    'list.table.originRegion': 'Origin Region',
    'list.table.supplier': 'Supplier',
    'list.table.suggestPrice': 'Sale Price',
    'list.table.lowPrice': 'Minimum Sale Price',
    'list.table.purchasePrice': 'Purchase Price',
    'list.table.inventoryNum': 'Total Stock',
    'list.table.adaptModel': 'Applicable Models Remark',
    'list.table.remark': 'Item Remark',
    'list.table.memCode': 'Mnemonic Code',
    'list.table.status': 'Status',
    'list.table.confirmOperation': 'Confirm {operType}?',
    'list.table.operation.enable': 'Enable',
    'list.table.operation.disable': 'Disable',
    'list.status.disable': 'Disable',
    'list.status.enable': 'Enable',

    // GoodsSearch component
    'search.tab.synthesis': 'Comprehensive Search',
    'search.tab.vin': 'VIN Code Search',
    'search.tab.salesSlip': 'Sales Goods Search',
    'search.form.goodsInfo': 'Goods Information',
    'search.form.goodsInfoPlaceholder': 'Please enter goods name/code/OE',
    'search.form.brand': 'Brand',
    'search.form.category': 'Goods Category',
    'search.form.orderStatus': 'Order Status',
    'search.form.orderNo': 'Sales Order No.',
    'search.form.orderTime': 'Order Time',
    'search.button.query': 'Search',
    'search.button.reset': 'Reset',
    'search.button.batchAdd': 'Batch Add',
    'search.checkbox.exactSearch': 'Exact Search',
    'search.checkbox.inStockOnly': 'In Stock Only',
    'search.checkbox.supplierSupplyOnly': 'Supplier Supply Only',
    'search.checkbox.belowStockLimit': 'Below Stock Limit',
    'search.message.fillPrice': 'Please fill in the price',
    'search.message.fillQuantity': 'Please fill in the quantity',
    'search.message.selectGoods': 'Please select goods',
    'search.tooltip.keyboardShortcuts': 'Keyboard Shortcuts',

    // GoodsSearch table columns
    'search.table.index': 'Index',
    'search.table.itemSn': 'Item Code',
    'search.table.itemName': 'Item Name',
    'search.table.oeNos': 'OE No.',
    'search.table.brandPartNos': 'Brand Part No.',
    'search.table.brandName': 'Brand',
    'search.table.categoryName': 'Category',
    'search.table.originRegionName': 'Origin',
    'search.table.spec': 'Specification',
    'search.table.unitName': 'Unit',
    'search.table.adaptModel': 'Applicable Models',
    'search.table.remark': 'Item Remark',
    'search.table.avaNum': 'Local Stock',
    'search.table.locationCode': 'Location',
    'search.table.suggestPrice': 'Suggested Price',
    'search.table.lastSalePrice': 'Last Sale Price',
    'search.table.lowPrice': 'Min Sale Price',
    'search.table.costPrice': 'Cost Price',
    'search.table.grossMargin': 'Gross Margin',
    'search.table.price': 'Sale Price',
    'search.table.number': 'Quantity',
    'search.table.action': 'Action',
    'search.table.add': 'Add',

    // Validation messages
    'search.validation.required': 'This field is required',
    'search.validation.belowMinPrice': 'Below minimum sale price',
    'search.validation.belowCostPrice': 'Below cost price',

    // External purchase specific columns
    'search.table.lowerLimit': 'Lower Limit',
    'search.table.upperLimit': 'Upper Limit',
    'search.table.saleNum30d': '30-Day Sales',
    'search.table.saleAvgNum': 'Avg Daily Sales',
    'search.table.purchasePrice': 'Purchase Price',
    'search.table.purchaseUnitPrice': 'Purchase Unit Price',
    'search.table.purchaseQuantity': 'Purchase Quantity',
    'search.placeholder.pleaseInput': 'Please input',

    // Sales slip specific columns
    'search.table.orderNo': 'Order No.',
    'search.table.createTime': 'Create Time',
    'search.table.storeName': 'Store Name',
    'search.table.warehouseName': 'Warehouse Name',
    'search.table.saleNum': 'Sale Quantity',

    // Sales return specific columns
    'search.table.skuName': 'Product Name',
    'search.table.refundAmount': 'Refund Amount',
    'search.table.returnQuantity': 'Return Quantity',
};
