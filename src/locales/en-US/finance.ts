export default {
    collection: {
        title: 'Receivables Management',
        detail: 'Receivable Details',
        detailTitle: 'Receivable Details',
        totalReceivableAmount: 'Total Receivable Amount',
        onlyShowReceivable: 'Only show existing receivables',
        onlyShowRemaining: 'Only show remaining amount > 0',
        exportDescription: 'Retailer receivables management data export',
        exportDetailDescription: 'Retailer receivables management detail data export',
        columns: {
            customer: 'Customer',
            store: 'Sales Store',
            overdueStatus: 'Overdue Status',
            receivableAmount: 'Receivable Amount',
            overdueAmount: 'Overdue Amount',
            totalAmount: 'Total Credit',
            availableAmount: 'Available Credit',
            creditTerms: 'Credit Terms (Days)',
            arrearsDay: 'Arrears Days',
            remainTerms: 'Remaining Terms (Days)',
            businessOrderNo: 'Business Order No.',
            businessCompleteTime: 'Business Complete Time',
            orderAmount: 'Order Amount',
            receivedAmount: 'Received Amount',
            remainReceivableAmount: 'Receivable Amount',
            customerCode: 'Customer Code',
            contact: 'Contact',
            contactPhone: 'Contact Phone',
            contactAddress: 'Contact Address',
        },
        summary: {
            orderTotal: 'Order Total',
            receivedTotal: 'Received Total',
            receivableTotal: 'Receivable Total',
            overdueTotal: 'Overdue Total',
        },
    },
    receive: {
        title: 'Payment Management',
        detail: 'Payment Details',
        add: 'Add Payment',
        confirm: 'Confirm Payment',
        autoAssign: 'Auto Assign',
        receivedAmount: 'Received Amount',
        currentWriteOff: 'Current Write-off Total',
        writeOffOrder: 'Write-off Orders',
        writeOffAmountMismatch: 'Current write-off total must equal received amount',
        noAmountError: 'Received amount cannot be empty',
        negativeAmountError: 'Received amount is less than or equal to 0, please manually assign write-off amount',
        noOrderSelectedWarning: 'No specific orders selected for this payment, please check!',
        amountMismatchError: 'Total received amount from orders does not match input received amount, please modify!',
        columns: {
            serialNumber: 'Payment No.',
            store: 'Payment Store',
            receiveTime: 'Payment Time',
            customer: 'Customer',
            customerName: 'Customer Name',
            receivedAccount: 'Payment Account',
            receivedAmount: 'Received Amount',
            creator: 'Creator',
            remark: 'Payment Remark',
            receiveType: 'Payment Type',
            storeName: 'Store Name',
            businessOrderNo: 'Business Order No.',
            orderCompleteTime: 'Order Complete Time',
            transactionCompleteTime: 'Transaction Complete Time',
            orderAmount: 'Order Amount',
            receivedAmountPaid: 'Received Amount',
            unreceived: 'Unreceived Amount',
            currentWriteOff: 'Current Write-off',
            writeOffAmount: 'Write-off Amount',
        },
        placeholders: {
            businessOrderNo: 'Business Order No.',
            enterAmount: 'Please enter',
        },
    },
    payment: {
        title: 'Payables Management',
        detail: 'Payable Details',
        detailTitle: 'Payable Details',
        totalPayableAmount: 'Total Payable Amount',
        onlyShowPayable: 'Only show existing payables',
        onlyShowRemaining: 'Only show remaining amount > 0',
        exportDescription: 'Retailer payables management data export',
        exportDetailDescription: 'Retailer payables management detail data export',
        columns: {
            supplier: 'Supplier',
            supplierCode: 'Supplier Code',
            store: 'Purchase Store',
            payableAmount: 'Payable Amount',
            businessOrderNo: 'Business Order No.',
            businessCompleteTime: 'Business Complete Time',
            orderAmount: 'Order Amount',
            paidAmount: 'Paid Amount',
            remainPayableAmount: 'Payable Amount',
            contact: 'Contact',
            contactPhone: 'Contact Phone',
            contactAddress: 'Contact Address',
        },
        summary: {
            orderTotal: 'Order Total',
            paidTotal: 'Paid Total',
            payableTotal: 'Payable Total',
        },
    },
    supplierPayment: {
        title: 'Supplier Payment',
        detail: 'Payment Details',
        add: 'Add Payment',
        confirm: 'Confirm Payment',
        autoAssign: 'Auto Assign',
        paymentAmount: 'Payment Amount',
        currentWriteOff: 'Current Write-off Total',
        writeOffOrder: 'Write-off Orders',
        writeOffAmountMismatch: 'Current write-off total must equal payment amount',
        noAmountError: 'Payment amount cannot be empty and must be greater than 0!',
        negativeAmountError: 'Payment amount is less than or equal to 0, please manually assign write-off amount',
        noOrderSelectedWarning: 'No specific documents selected for this payment, please check!',
        amountMismatchError: 'Total payment amount for each document is inconsistent with input payment amount, please modify!',
        columns: {
            serialNumber: 'Payment No.',
            paymentStore: 'Payment Store',
            paymentTime: 'Payment Time',
            supplier: 'Supplier',
            supplierName: 'Supplier Name',
            paymentAccount: 'Payment Account',
            paymentAmount: 'Payment Amount',
            creator: 'Creator',
            remark: 'Payment Remark',
            storeName: 'Store Name',
            businessOrderNo: 'Business Order No.',
            businessCompleteTime: 'Business Complete Time',
            orderAmount: 'Order Amount',
            paidAmount: 'Paid Amount',
            unpaidAmount: 'Unpaid Amount',
            currentWriteOff: 'Current Write-off',
            writeOffAmount: 'Write-off Amount',
        },
        placeholders: {
            businessOrderNo: 'Business Order No.',
            enterAmount: 'Please enter',
        },
    },
    cost: {
        title: 'Income & Expense Management',
        income: 'Income',
        expend: 'Expense',
        otherIncome: 'Other Income',
        otherExpend: 'Other Expense',
        add: 'Add',
        invalidateSuccess: 'Invalidated successfully',
        confirmInvalidate: 'Confirm to invalidate?',
        invalidate: 'Invalidate',
        columns: {
            store: 'Store',
            incomeType: 'Income Type',
            expendType: 'Expense Type',
            createTime: 'Create Time',
            serialNumber: 'Transaction No.',
            incomeStore: 'Income Store',
            expendStore: 'Payment Store',
            incomeAmount: 'Income Amount',
            expendAmount: 'Payment Amount',
            settlementAccount: 'Settlement Account',
            creator: 'Creator',
            incomeRemark: 'Income Remark',
            expendRemark: 'Payment Remark',
        },
        form: {
            incomeStore: 'Income Store',
            expendStore: 'Payment Store',
            incomeType: 'Income Type',
            expendType: 'Expense Type',
            incomeAmount: 'Income Amount',
            expendAmount: 'Payment Amount',
            settlementAccount: 'Settlement Account',
            incomeRemark: 'Income Remark',
            expendRemark: 'Payment Remark',
            yuan: 'Yuan',
        },
    },
    flow: {
        title: 'Capital Flow',
        totalIncome: 'Total Income',
        totalExpend: 'Total Expenditure',
        columns: {
            businessOrderNo: 'Business Order No.',
            occurTime: 'Occurrence Time',
            customerOrSupplier: 'Customer/Supplier',
            incomeExpendType: 'Income/Expense Type',
            store: 'Store',
            settlementAccount: 'Settlement Account',
            income: 'Income',
            expend: 'Expenditure',
        },
    },
    customer: {
        title: 'Account Management',
        addAccount: 'Add Account',
        editAccount: 'Edit Account',
        confirmDelete: 'Confirm to delete?',
        columns: {
            accountName: 'Account Name',
            bankName: 'Bank Name',
            bankCardNumber: 'Bank Card Number',
            belongToStore: 'Belongs to Store',
            accountBalance: 'Account Balance',
            initialBalance: 'Initial Balance',
        },
        form: {
            accountName: 'Account Name',
            bankName: 'Bank Name',
            bankCardNumber: 'Bank Card Number',
            belongToStore: 'Belongs to Store',
            initialBalance: 'Initial Balance',
        },
    },
    tag: {
        title: 'Income & Expense Type Management',
        incomeExpenseType: 'Income & Expense Type',
        addIncomeExpenseType: 'Add Income & Expense Type',
        editIncomeExpenseType: 'Edit Income & Expense Type',
        columns: {
            incomeExpenseType: 'Income & Expense Type',
            incomeDirection: 'Income Direction',
            source: 'Source',
            isEnabled: 'Is Enabled',
        },
        form: {
            incomeExpenseType: 'Income & Expense Type',
            incomeExpenseDirection: 'Income & Expense Direction',
            income: 'Income',
            expense: 'Expense',
            pleaseInput: 'Please input',
        },
    },
};