export default {
    finance: {
        title: 'Finance Report',
        overview: {
            fundIncome: 'Fund Income',
            fundExpenditures: 'Fund Expenditures',
            earnedProfit: 'Earned Profit',
        },
        charts: {
            incomeExpendituresTrend: 'Income & Expenditures Trend',
            incomeExpendituresRatio: 'Income & Expenditures Ratio Statistics',
            receivablePayableTrend: 'Receivable & Payable Trend',
            fundIncomeChart: 'Fund Income',
            fundExpendituresChart: 'Fund Expenditures',
        },
        trends: {
            fundIncome: 'Fund Income',
            fundExpenditures: 'Fund Expenditures',
            receivableAmount: 'Receivable Amount',
            payableAmount: 'Payable Amount',
        },
        form: {
            store: 'Store',
            startTime: 'Start Time',
            endTime: 'End Time',
            query: 'Query',
            reset: 'Reset',
        },
        axis: {
            yuan: 'Yuan',
        },
    },
    sales: {
        title: 'Sales Report',
        overview: {
            saleAmount: 'Sales Amount',
            costAmount: 'Sales Cost',
            saleGrossProfit: 'Sales Gross Profit',
            grossProfitRate: 'Gross Profit Rate',
            saleOrderNum: 'Sales Order Count',
            saleCustomerNum: 'Sales Customer Count',
        },
        trends: {
            saleAmount: 'Sales Amount',
            costAmount: 'Sales Cost',
            saleGrossProfit: 'Sales Gross Profit',
            grossProfitRate: 'Gross Profit Rate',
            saleOrderNum: 'Sales Order Count',
            saleCustomerNum: 'Sales Customer Count',
        },
        charts: {
            salesTrend: 'Sales Trend',
            customerStatistics: 'Customer Statistics',
            goodsStatistics: 'Goods Statistics',
            staffStatistics: 'Staff Statistics',
            brandRatio: 'Brand Ratio',
            categoryRatio: 'Category Ratio',
        },
        form: {
            store: 'Store',
            customer: 'Customer',
            goodsInfo: 'Goods Info',
            goodsInfoPlaceholder: 'Please enter goods name/code',
            brand: 'Brand',
            category: 'Category',
            employee: 'Employee',
            startTime: 'Start Time',
            endTime: 'End Time',
            query: 'Query',
            reset: 'Reset',
        },
        radio: {
            saleAmount: 'Sales Amount',
            saleNum: 'Sales Quantity',
            saleGrossProfit: 'Sales Gross Profit',
        },
        table: {
            customer: {
                index: 'Index',
                customerName: 'Customer Name',
                saleAmount: 'Sales Amount',
                costAmount: 'Sales Cost',
                saleGrossProfit: 'Sales Gross Profit',
                grossProfitRate: 'Gross Profit Rate',
                saleOrderNum: 'Sales Order Count',
                perCstTrans: 'Per Customer Transaction',
            },
            goods: {
                index: 'Index',
                itemName: 'Item Name',
                brandName: 'Brand',
                categoryName: 'Category',
                saleAmount: 'Sales Amount',
                costAmount: 'Sales Cost',
                saleGrossProfit: 'Sales Gross Profit',
                grossProfitRate: 'Gross Profit Rate',
                saleSaleNum: 'Sales Quantity',
                avgSaleAmount: 'Average Sales Amount',
            },
            staff: {
                index: 'Index',
                employeeName: 'Employee Name',
                saleAmount: 'Sales Amount',
                costAmount: 'Sales Cost',
                saleGrossProfit: 'Sales Gross Profit',
                grossProfitRate: 'Gross Profit Rate',
                saleOrderNum: 'Sales Order Count',
                perCstTrans: 'Per Customer Transaction',
            },
        },
        axis: {
            yuan: 'Yuan',
            percent: '%',
        },
    },
    purchase: {
        title: 'Purchase Report',
        overview: {
            purchaseAmount: 'Purchase Amount',
            purchaseOrderNum: 'Purchase Orders',
            purchaseNum: 'Purchase Quantity',
        },
        charts: {
            purchaseTrend: 'Purchase Trend',
            supplierStatistics: 'Statistics by Supplier',
            goodsStatistics: 'Statistics by Goods',
            brandChart: 'Brand',
            categoryChart: 'Category',
        },
        trends: {
            purchaseAmount: 'Purchase Amount',
            purchaseNum: 'Purchase Quantity',
        },
        form: {
            store: 'Store',
            supplier: 'Supplier',
            goodsInfo: 'Goods Info',
            goodsInfoPlaceholder: 'Please enter goods name/code',
            brand: 'Brand',
            category: 'Category',
            startTime: 'Start Time',
            endTime: 'End Time',
            query: 'Query',
            reset: 'Reset',
        },
        radio: {
            purchaseAmount: 'Purchase Amount',
            purchaseNum: 'Purchase Quantity',
        },
        table: {
            index: 'Index',
            supplierName: 'Supplier Name',
            purchaseAmount: 'Purchase Amount',
            purchaseOrderNum: 'Purchase Orders',
            purchaseNum: 'Purchase Quantity',
            itemName: 'Item Name',
            brandName: 'Brand',
            categoryName: 'Category',
            avgPurchaseAmount: 'Avg Purchase Amount',
        },
        axis: {
            yuan: 'Yuan',
        },
    },
    inventory: {
        title: 'Inventory Report',
        overview: {
            inNum: 'Inbound Quantity',
            inAmount: 'Inbound Amount',
            outNum: 'Outbound Quantity',
            outAmount: 'Outbound Amount',
            stockTurnDays: 'Average Inventory Turnover Days',
        },
        charts: {
            stockTurnDaysDistribution: 'Inventory Turnover Days Distribution',
            stockTurnDaysFormula: 'Inventory Turnover Days = (Beginning Inventory Cost + Ending Inventory Cost) / 2 / Daily Average Sales Cost',
            inventoryChangeStatistics: 'Inventory Change Statistics',
        },
        form: {
            warehouse: 'Warehouse',
            goodsInfo: 'Goods Info',
            goodsInfoPlaceholder: 'Please enter goods name/code',
            brand: 'Brand',
            category: 'Category',
            startTime: 'Start Time',
            endTime: 'End Time',
            query: 'Query',
            reset: 'Reset',
        },
        table: {
            index: 'Index',
            itemName: 'Item Name',
            brandName: 'Brand',
            categoryName: 'Category',
            stockTurnDays: 'Stock Turnover Days',
            saleOutNum: 'Sales Quantity',
            saleInNum: 'Return Quantity',
            purchaseInNum: 'Purchase Quantity',
            purchaseOutNum: 'Purchase Return Quantity',
            checkGainNum: 'Inventory Gain Quantity',
            checkLossNum: 'Inventory Loss Quantity',
            saleOutAmount: 'Sales Amount',
            saleInAmount: 'Return Amount',
            purchaseInAmount: 'Purchase Amount',
            purchaseOutAmount: 'Purchase Return Amount',
            checkGainAmount: 'Inventory Gain Amount',
            checkLossAmount: 'Inventory Loss Amount',
        },
    },
};
