import ColumnRender from '@/components/ColumnRender';
import LeftTitle from '@/components/LeftTitle';
import { ViewDetailModalProps } from '@/pages/system/message';
import MessageDetailModal from '@/pages/system/message/components/MessageDetailModal';
import { queryMsgList } from '@/pages/system/message/services';
import { MsgStatus } from '@/pages/system/message/types/MsgStatus';
import { MsgListItemEntity } from '@/pages/system/message/types/msg.list.item.entity';
import { useLocation } from '@@/exports';
import { RightOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { Empty, Flex, Spin, Typography } from 'antd';
import { useEffect, useRef, useState } from 'react';

const MessageList = () => {
  const intl = useIntl();
  const timer = useRef<any>();
  const [list, setList] = useState<MsgListItemEntity[]>();
  const [viewDetailModalProps, setViewDetailModalProps] = useState<ViewDetailModalProps>({
    visible: false,
  });
  const { pathname } = useLocation();

  const isHome = pathname.startsWith('/home');

  const queryMsgListFn = () => {
    queryMsgList({ pageNo: 1, pageSize: 5, isDelete: 0 }).then((result) => {
      if (result?.data) {
        setList(result.data);
      }
    });
  };

  useEffect(() => {
    // if (isHome) {
    //   queryMsgListFn();
    //   timer.current = setInterval(() => {
    //     queryMsgListFn();
    //   }, 3000);
    // }
    // return () => {
    //   clearInterval(timer.current);
    // };
  }, [isHome]);

  return (
    <ProCard headerBordered={false} bordered={false}>
      <Flex justify="space-between" align="center" className="mb-[16px]">
        <LeftTitle title={intl.formatMessage({ id: 'home.message.title' })} />
        <span
          className="cursor-pointer"
          onClick={() => {
            history.push('/system/message');
          }}
        >
          <span className="text-[#00000099]">{intl.formatMessage({ id: 'home.message.allMessages' })}</span>
          <RightOutlined width={8} height={12} className="text-[#00000073] ml-2" />
        </span>
      </Flex>
      {/* 消息列表 */}
      <Spin spinning={typeof list === 'undefined'}>
        {list?.length === 0 && <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
        {list?.map((item) => (
          <div
            key={item.id}
            className="flex flex-col mb-4 cursor-pointer"
            onClick={() => {
              setViewDetailModalProps({
                id: item.id,
                visible: true,
              });
            }}
          >
            <div className="relative">
              {item.msgStatus === MsgStatus.NoRead && <span className="notice-unread" />}
              <span className="text-[#000000D9]">{item.title}</span>
            </div>
            <Typography.Paragraph
              ellipsis={{ rows: 2 }}
              className="text-[#00000073]"
              title={item.content}
            >
              {ColumnRender.RichContentColumnRender(item.content)}
            </Typography.Paragraph>
            <span className="text-[#00000073] text-[12px]">{item.createTime}</span>
          </div>
        ))}
      </Spin>
      <MessageDetailModal
        {...viewDetailModalProps}
        onClose={() => setViewDetailModalProps({ id: undefined, visible: false })}
      />
    </ProCard>
  );
};

export default MessageList;
