import { request } from '@/utils/request';
import type { SalesResponseType } from './types/SalesResponseType';

/**
 * 销售概况(日/月)
 * @param params
 * @returns
 */
export const queryIndexOverviewList = (params: {
  storeIdList?: string[] | undefined;
  type: 'DAY' | 'MONTH';
}) => {
  return request<SalesResponseType[]>(`/ipmsconsole/console/IndexFacade/queryIndexOverviewList`, {
    data: params,
  });
};
/**
 * 近30天销售趋势
 * @param params
 * @returns
 */
export const queryIndexSaleTrendList = (params: { storeIdList?: string[] | undefined }) => {
  return request<SalesResponseType[]>(`/ipmsconsole/console/IndexFacade/queryIndexSaleTrendList`, {
    data: params,
  });
};
