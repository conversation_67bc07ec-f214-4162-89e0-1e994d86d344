import iconAccount from '@/assets/icons/home/<USER>';
import iconBill from '@/assets/icons/home/<USER>';
import iconCollect from '@/assets/icons/home/<USER>';
import iconCollectWarning from '@/assets/icons/home/<USER>';
import iconCustomer from '@/assets/icons/home/<USER>';
import iconGoods from '@/assets/icons/home/<USER>';
import iconOrder from '@/assets/icons/home/<USER>';
import iconOutBound from '@/assets/icons/home/<USER>';
import iconReturns from '@/assets/icons/home/<USER>';
import iconStock from '@/assets/icons/home/<USER>';
import iconStockWarning from '@/assets/icons/home/<USER>';
import iconWillHandle from '@/assets/icons/home/<USER>';
// 常用功能
export const CommonFunctions = [
  { key: 'iconBill', href: '/sales/order/edit', icon: iconBill, labelKey: 'home.functions.salesBill', size: [40, 40] },
  {
    key: 'iconReturns',
    href: '/sales/returns/list',
    icon: iconReturns,
    labelKey: 'home.functions.salesReturns',
    size: [40, 40],
  },
  {
    key: 'iconCollect',
    href: '/finance/receive',
    icon: iconCollect,
    labelKey: 'home.functions.orderCollection',
    size: [40, 40],
  },
  {
    key: 'iconAccount',
    href: '/finance/cost',
    icon: iconAccount,
    labelKey: 'home.functions.accounting',
    size: [40, 40],
  },
  {
    key: 'iconOrder',
    href: '/sales/order/list',
    icon: iconOrder,
    labelKey: 'home.functions.allOrders',
    size: [40, 40],
  },
  {
    key: 'iconStock',
    href: '/stocks/inventory',
    icon: iconStock,
    labelKey: 'home.functions.viewInventory',
    size: [40, 40],
  },
  {
    key: 'iconCustomer',
    href: '/customer/list',
    icon: iconCustomer,
    labelKey: 'home.functions.customerManagement',
    size: [40, 40],
  },
  { key: 'iconGoods', href: '/goods/list', icon: iconGoods, labelKey: 'home.functions.productManagement', size: [40, 40] },
];
export const TodoList = [
  {
    key: 'WillHandle',
    icon: iconWillHandle,
    labelKey: 'home.todo.pendingOrders',
    size: [24, 24],
    href: '/sales/order/list',
    state: {
      orderStatusList: ['90'],
    },
  },
  {
    key: 'OutBound',
    icon: iconOutBound,
    labelKey: 'home.todo.outboundOrders',
    size: [24, 24],
    href: '/sales/order/list',
    state: {
      orderStatusList: ['300'],
    },
  },
  {
    key: 'StockWarning',
    icon: iconStockWarning,
    labelKey: 'home.todo.stockWarning',
    size: [24, 24],
    href: '/stocks/inventory',
    state: {
      invLimitStatusList: ['1', '2'],
    },
  },
  {
    key: 'CollectWarning',
    icon: iconCollectWarning,
    labelKey: 'home.todo.receivableWarning',
    href: '/finance/collection',
    size: [24, 24],
    state: {
      receivableFlag: '1',
    },
  },
];
