import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { ResponseDataType } from '@/types/ResponseDataType';
import { request } from '@/utils/request';
import { LocationListEntity } from './list/types/location.list.entity';
import { PostEntity } from './list/types/post.entity';

/**
 * 列表查询
 * @param params
 * @returns
 */
export const queryPostList = async (params: Partial<PostEntity> & PageRequestParamsType) => {
  return request<PageResponseDataType<PostEntity>>(`/ipmswarehouse/warehouseLocation/queryByPage`, {
    data: params,
  });
};

/**
 * 详情查询
 * @param params
 * @returns
 */
export const queryPostDetail = async (params: Partial<PostEntity>) => {
  return request<PostEntity>(`/ipmswarehouse/warehouseLocation/queryById`, {
    data: params,
  });
};

/**
 * 新增
 * @param params
 * @returns
 */
export const createPost = async (params: Partial<PostEntity>) => {
  return request<ResponseDataType<boolean>>(`/ipmswarehouse/warehouseLocation/create`, {
    origin: true,
    data: params,
  });
};
/**
 * 编辑
 * @param params
 * @returns
 */
export const modifyPost = async (params: Partial<PostEntity>) => {
  return request<ResponseDataType<boolean>>(`/ipmswarehouse/warehouseLocation/edit`, {
    origin: true,
    data: params,
  });
};

/**
 *删除
 * @param params
 * @returns
 */
export const modifyRemoveByIdPost = async (params: Partial<PostEntity>) => {
  return request<string>(`/ipmswarehouse/warehouseLocation/removeById`, {
    data: params,
  });
};
/**
 * 根据仓库id查询库位
 * @param params
 * @returns
 */
export const queryLocationByWhId = async (params: { warehouseIdList: string[] }) => {
  return request<LocationListEntity>(`/ipmswarehouse/warehouseLocation/queryByWhId`, {
    data: params,
  });
};
