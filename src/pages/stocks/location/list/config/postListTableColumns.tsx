import AuthButton from '@/components/common/AuthButton';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import type { ProColumns } from '@ant-design/pro-components';
import { Popconfirm, Space } from 'antd';
import { useIntl } from 'umi';
import type { PostEntity } from '../types/post.entity';

export interface PostListTableColumnsProps {
  handleDeleteItem: (id: string) => void;
  handleUpdateItem: (id: string) => void;
}

export const PostListTableColumns = (props: PostListTableColumnsProps) => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'stocks.location.label.locationName' }),
      dataIndex: 'code',
      key: 'code',
      ellipsis: true,
      search: {
        transform: (value: any) => {
          return {
            codeList: [value],
          };
        },
      },
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.location.label.warehouseName' }),
      dataIndex: 'warehouseName',
      key: 'warehouseName',
      search: {
        transform: (values: any) => {
          console.log(values);
          return {
            warehouseIdList: [values],
          };
        },
      },
      valueType: 'select',
      width: 100,
      fieldProps: {
        fieldNames: { label: 'warehouseName', value: 'id' },
      },
      request: () => {
        return warehouseList({}).then((s) => {
          return s.data?.warehouseSimpleRoList;
        });
      },
      // 查询 所有仓库
    },
    {
      title: intl.formatMessage({ id: 'stocks.location.label.locationRemark' }),
      dataIndex: 'remark',
      key: 'remark',
      search: false,
      ellipsis: true,
      width: 180,
    },

    {
      title: intl.formatMessage({ id: 'common.column.createTime' }),
      dataIndex: 'createTime',
      key: 'createTime',
      search: false,
      width: 140,
    },
    {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 80,
      fixed: 'right',
      render: (text, record) => (
        <Space>
          <AuthButton
            isHref
            authority="editLocation"
            onClick={() => props.handleUpdateItem(record.id)}
          >
            {intl.formatMessage({ id: 'common.button.edit' })}
          </AuthButton>
          <Popconfirm title={intl.formatMessage({ id: 'common.confirm.delete' })} onConfirm={() => props.handleDeleteItem(record.id)}>
            <AuthButton isHref authority="deleteLocation">
              {intl.formatMessage({ id: 'common.button.delete' })}
            </AuthButton>
          </Popconfirm>
        </Space>
      ),
    },
  ] as ProColumns<PostEntity>[];
};
