export interface LocationListEntity {
  warehouseLocationRoList: WarehouseLocationRoList[];
}

export interface WarehouseLocationRoList {
  /**
   * 库位编码(库位名称）
   */
  code?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * 库存备注
   */
  remark?: string;
  /**
   * 状态:1：启用;0：停用
   */
  state?: number;
  /**
   * 仓库id
   */
  warehouseId?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
}
