import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { CommonModelForm } from '@/types/CommonModelForm';
import { ProFormSelect } from '@ant-design/pro-components';
import { ModalForm, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { useAsyncEffect } from 'ahooks';
import { Button } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { useIntl } from 'umi';
import { queryPostDetail } from '../../../services';
import { PostEntity } from '../../types/post.entity';

export default (props: CommonModelForm<string, PostEntity>) => {
  const intl = useIntl();
  const [form] = useForm();
  useAsyncEffect(async () => {
    if (props.recordId == '0') {
      form.resetFields();
    } else {
      const data = await queryPostDetail({ id: props.recordId });
      form.setFieldsValue(data);
    }
  }, [props.visible]);

  const submitter = {
    render: () => (
      <Button key="cancel" size="middle" onClick={props.onCancel}>
        {intl.formatMessage({ id: 'common.button.cancel' })}
      </Button>
    ),
  };

  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };
  const rules = [{ required: !props.readOnly }];
  return (
    <ModalForm
      {...layout}
      form={form}
      layout="horizontal"
      title={props.title}
      open={props.visible}
      width="30%"
      modalProps={{
        centered: true,
        onCancel: props.onCancel,
      }}
      submitter={props.readOnly ? submitter : {}}
      onFinish={props.onOk}
    >
      <ProFormText name="id" disabled={props.readOnly} hidden={true} />
      <ProFormText
        rules={[{ required: !props.readOnly, max: 20 }]}
        name="code"
        disabled={props.readOnly}
        label={intl.formatMessage({ id: 'stocks.location.label.locationName' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.inputWithMaxLength' }, { maxLength: 20 })}
      />
      <ProFormSelect
        name="warehouseId"
        rules={rules}
        disabled={props.readOnly || props.recordId != '0'}
        label={intl.formatMessage({ id: 'stocks.location.label.warehouseName' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.select' })}
        fieldProps={{ fieldNames: { label: 'warehouseName', value: 'id' } }}
        request={() => warehouseList({ state: YesNoStatus.YES }).then((s) => s?.data?.warehouseSimpleRoList ?? [])}
      />
      <ProFormTextArea
        name="remark"
        disabled={props.readOnly}
        label={intl.formatMessage({ id: 'common.label.remark' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.inputWithMaxLength' }, { maxLength: 100 })}
        fieldProps={{ count: { max: 100, show: true }, maxLength: 100 }}
        rules={[{ max: 100 }]}
      />
    </ModalForm>
  );
};
