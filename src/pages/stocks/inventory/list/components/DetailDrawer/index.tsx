import FunProTable from '@/components/common/FunProTable';
import type { CommonModelForm } from '@/types/CommonModelForm';
import { DrawerForm } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { isEmpty } from 'lodash';
import { inventoryChangeDetailPost } from '../../../services';
import type { ChangeDetailRequest } from '../../types/change.detail.request';

export default (props: CommonModelForm<ChangeDetailRequest, any>) => {
  const intl = useIntl();
  useAsyncEffect(async () => {
    if (props.recordId) {
    }
  }, [props.visible]);

  return (
    <DrawerForm
      title={props.title}
      width={1080}
      layout="horizontal"
      submitter={false}
      drawerProps={{
        classNames: {
          body: 'bg-[#f2f2f2]',
        },
        destroyOnClose: true,
        onClose: props.onCancel,
        maskClosable: false,
      }}
      open={props.visible}
    >
      <FunProTable
        search={false}
        scroll={{ x: true }}
        options={false}
        params={{
          itemId: props.recordId?.itemId,
          warehouseId: props.recordId?.warehouseId,
          warehouseName: props.recordId?.warehouseName,
        }}
        requestPage={async (params) => {
          if (!isEmpty(params.itemId) && !isEmpty(params.warehouseId)) {
            return await inventoryChangeDetailPost(params);
          }
          return { data: [] };
        }}
        columns={[
          {
            title: intl.formatMessage({ id: 'stocks.inventory.detail.label.changeTime' }),
            dataIndex: 'changeTime',
            width: 140,
            editable: false,
            search: false,
          },
          {
            title: intl.formatMessage({ id: 'stocks.inventory.detail.label.changeType' }),
            dataIndex: 'billTypeDesc',
            width: 120,
            editable: false,
            search: false,
          },
          {
            title: intl.formatMessage({ id: 'stocks.inventory.detail.label.warehouse' }),
            dataIndex: 'warehouseName',
            width: 120,
            editable: false,
            search: false,
          },
          {
            title: intl.formatMessage({ id: 'stocks.inventory.detail.label.changeNum' }),
            dataIndex: 'changeNum',
            width: 120,
            editable: false,
            search: false,
          },
          {
            title: intl.formatMessage({ id: 'stocks.inventory.detail.label.businessOrderNo' }),
            dataIndex: 'origBillNo',
            width: 120,
            editable: false,
            search: false,
          },
        ]}
      />
    </DrawerForm>
  );
};
