import type { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { ChangeDetailEntity } from './list/types/change.detail.entity';
import type { ChangeDetailRequest } from './list/types/change.detail.request';
import type { InventoryPostEntity } from './list/types/inventory.post.entity';
import type { inventorySafetyLimitRquest } from './list/types/inventory.safety.limit.request';
import type { itemLocationRemarkRquest } from './list/types/item.location.remark.request';
import type { QueryPostListRequest } from './list/types/query.post.list.request';
import type { TotalPostEntity } from './list/types/total.post.entity';

/**
 * 列表查询
 * @param params
 * @returns
 */
export const queryInventoryPagePost = async (params: Partial<QueryPostListRequest>) => {
  return request<PageResponseDataType<InventoryPostEntity>>(`/ipmsstocks/inventory/queryByPage`, {
    data: params,
  });
};
/**
 * 查询库存和 金额
 * @param params
 * @returns
 */
export const queryTotalPost = async (params: Partial<QueryPostListRequest>) => {
  return request<TotalPostEntity>(`/ipmsstocks/inventory/queryTotal`, {
    data: params,
  });
};
/**
 * 创建安全库存
 * @param params
 * @returns
 */
export const inventorysafetylimitPost = (params: Partial<inventorySafetyLimitRquest>) => {
  return request<boolean>(`/ipmsstocks/inventorysafetylimit/saveOrUpdate`, {
    data: params,
  });
};
/**
 * 商品库位
 * @param params
 * @returns
 */
export const itemLocationRemarkPost = (params: Partial<itemLocationRemarkRquest>) => {
  return request<boolean>(`/ipmswarehouse/itemLocationRemark/saveOrUpdateList`, {
    data: params,
  });
};

/**
 * 库存流水分页查询
 * @param params
 * @returns
 */
export const inventoryChangeDetailPost = (params: Partial<ChangeDetailRequest>) => {
  return request<PageResponseDataType<ChangeDetailEntity>>(
    `/ipmsstocks/inventoryChangeDetail/queryByPage`,
    {
      data: params,
    },
  );
};
