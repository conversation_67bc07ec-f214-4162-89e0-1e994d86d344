import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { ResponseDataType } from '@/types/ResponseDataType';
import { request } from '@/utils/request';
import { InPutDetailPostEntity } from './detail/types/input.detail.post.entity';
import { InPutDetailRequest } from './detail/types/input.detail.request';
import { InputPostConfirmRequest } from './list/types/input.post.confirm.request';
import { InputPostEntity } from './list/types/input.post.entity';
import { InputPostWithdrawRequest } from './list/types/input.post.withdraw.request';
import { InputStockBatchList } from './list/types/input.stock.batch.list';
import { QueryPostListRequest } from './list/types/query.post.list.request';

/**
 * 列表查询
 * @param params
 * @returns
 */
export const queryInputPutPagePost = async (
  params: Partial<QueryPostListRequest> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<InputPostEntity>>(`/ipmswarehouse/stockin/queryByPage`, {
    data: params,
  });
};

/**
 * 详情查询
 * @param params
 * @returns
 */
export const queryInPutDetailPost = async (params: Partial<InPutDetailRequest>) => {
  return request<InPutDetailPostEntity>(`/ipmswarehouse/stockin/queryDetail`, {
    data: params,
  });
};

/**
 * 入库记录
 * @param params
 * @returns
 */
export const queryBatchDetailPost = async (params: Partial<InPutDetailRequest>) => {
  return request<InputStockBatchList>(
    `/ipmswarehouse/stockin/queryBatchDetail
  `,
    {
      data: params,
    },
  );
};

/**
 * 作废
 * @param params
 * @returns
 */
export const withdrawInputPost = async (params: Partial<InputPostWithdrawRequest>) => {
  return request<ResponseDataType<boolean>>(`/ipmswarehouse/stockin/withdraw`, {
    origin: true,
    data: params,
  });
};

/**
 * 确认入库
 * @param params
 * @returns
 */

export const confirmInPutPost = async (params: Partial<InputPostConfirmRequest>) => {
  return request<boolean>(`/ipmswarehouse/stockin/confirm`, {
    data: params,
  });
};
