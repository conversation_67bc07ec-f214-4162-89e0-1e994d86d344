import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';

export interface InputPostEntity {
  /**
   * 业务类型
   */
  billType?: number;
  /**
   * 业务类型描述
   */
  billTypeDesc?: string;
  /**
   * 入库单号
   */
  bizBillNo?: string;
  /**
   * 1:存在，0：不存在
   */
  existBatchRecord?: YesNoStatus;
  /**
   * 主键
   */
  id?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * 原单号
   */
  origBillNo?: string;
  /**
   * 原三方单号，比如销退入库对应的XSD单
   */
  origThirdNo?: string;
  /**
   * 发货方
   */
  promoter?: string;
  /**
   * 验货人
   */
  realInInspectPerson?: string;
  /**
   * 实际入库人
   */
  realInPerson?: string;
  /**
   * 实际入库时间
   */
  realInTime?: string;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 0.取消1.待入库2.已入库3.部分入库
   */
  state?: number;
  /**
   * 出库单状态描述：0取消1.待出库2.已出库3.部分出库
   */
  stateDesc?: string;
  /**
   * 计划出库商品总数
   */
  totalAmount?: number;
  /**
   * 计划出库商品种类总和
   */
  totalItem?: number;
  /**
   * 入库仓库id
   */
  warehouseId?: string;
  /**
   * 出库仓库
   */
  warehouseIdOut?: string;
  /**
   * 入库仓库名称
   */
  warehouseName?: string;
  /**
   * 出库仓库名称
   */
  warehouseOutName?: string;
}
