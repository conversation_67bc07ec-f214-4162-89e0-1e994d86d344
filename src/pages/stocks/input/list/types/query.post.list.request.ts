import { PaginationRequest } from '@/types/PaginationRequest';

export interface QueryPostListRequest extends PaginationRequest {
  /**
   * 业务类型（参考：com.ipms.core.message.objects.stocks.enums.BillTypeEnum）
   */
  billTypeList?: number[];
  /**
   * 入库单号
   */
  bizBillNo?: string;
  /**
   * 开始结束时间
   */
  endCreateTime?: string;
  /**
   * 入库完成结束时间
   */
  endRealInTime?: string;
  /**
   * 支持输入商品编码和商品名称，模糊匹配查询
   */
  keyword?: string;

  /**
   * 业务单号
   */
  origBillNo?: string;
  /**
   * 开始创建时间
   */
  startCreateTime?: string;
  /**
   * 入库完成开始时间
   */
  startRealInTime?: string;
  /**
   * None
   */
  startRow?: number;
  /**
   * 入库单状态：0取消1.待入库2.已入库3.部分入库
   */
  stateList?: number[];
  /**
   * 当前门店数据
   */
  storeId?: string;
  /**
   * 仓库id
   */
  warehouseIdList?: string[];
}
