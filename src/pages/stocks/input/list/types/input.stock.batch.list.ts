import { InPutStatus } from './InPutStatus';

export interface InputStockBatchList {
  /**
   * 批次列表信息
   */
  stockInBatchRoList?: StockInBatchRoList[];
}

export interface StockInBatchRoList {
  /**
   * 当前批次作废操作人
   */
  cancelPerson?: string;
  /**
   * 当前批次作废时间
   */
  cancelTime?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * 业务单号
   */
  origBillNo?: string;
  /**
   * 状态，0:已作废，1:已出库
   */
  state?: InPutStatus;
  /**
   * 批次明细
   */
  stockInBatchDetailRoList?: StockInBatchDetailRoList[];
  /**
   * 出库单id
   */
  stockInId?: string;

  /**
   * 批次id
   */
  id?: string;
  /**
   * 当前批次入库操作人
   */
  stockInPerson?: string;
  /**
   * 当前批次出库时间
   */
  stockInTime?: string;
}

export interface StockInBatchDetailRoList {
  /**
    * 批次id
    */
  batchId?: string;
  /**
   * 品牌Id
   */
  brandId?: string;
  /**
   * 品牌名称
   */
  brandName?: string;
  /**
   * 品牌件号
   */
  brandPartNo?: string;
  /**
   * 类目Id
   */
  categoryId?: string;
  /**
   * 类目名称
   */
  categoryName?: string;
  /**
   * 货位备注
   */
  code?: string;
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 配送地址
   */
  deliveryAddress?: string;
  /**
   * 配送方式（1：客户自提、2：商家配送、3：快递物流）
   */
  distributionMode?: number;
  /**
   * 主键
   */
  id?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * 商品ID
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 物流公司id
   */
  logisticsCompanyCode?: string;
  /**
   * 物流公司名称
   */
  logisticsCompanyName?: string;
  /**
   * 物流单号
   */
  logisticsNo?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * oe码
   */
  oeNo?: string;
  /**
   * 预出库数量
   */
  preAmount?: number;
  /**
   * 实际出库数量
   */
  realAmount?: number;
  /**
   * 扫描数量
   */
  scanAmount?: number;
  /**
   * 出库详情id
   */
  stockOutDetailId?: string;
  /**
   * 出库单id
   */
  stockOutId?: string;
  /**
   * 单位id
   */
  unitId?: string;
  /**
   * 单位名称
   */
  unitName?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
  /**
   * 出库仓库id
   */
  warehouseId?: string;
}
