export enum InPutStatus {
  DRAFT = 0,
  AUDITING = 1,
  CONFIRM = 2,
  TO_ARRIVAL = 3,
}

export enum InPutStatusName {
  DRAFT = '已取消',
  AUDITING = '待入库',
  CONFIRM = '已入库',
  TO_ARRIVAL = '部分入库',
}
export const inPutStatusOptions = {
  [InPutStatus.DRAFT]: { text: InPutStatusName.DRAFT, status: 'default' },
  [InPutStatus.AUDITING]: { text: InPutStatusName.AUDITING, status: 'error' },
  [InPutStatus.CONFIRM]: { text: InPutStatusName.CONFIRM, status: 'success' },
  [InPutStatus.TO_ARRIVAL]: { text: InPutStatusName.TO_ARRIVAL, status: 'processing' },
};
