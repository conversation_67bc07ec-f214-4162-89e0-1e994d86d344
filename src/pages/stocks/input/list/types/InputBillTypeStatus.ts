export enum InputBillTypeStatus {
  PURCHASE_IN = 100,
  TRANSFER_IN = 101,
  RETURN_OF_GOODS_IN = 102,
  INVENTORY_WIN_IN = 103,
}

export enum InputBillTypeStatusName {
  PURCHASE_IN = '采购入库',
  TRANSFER_IN = '调拨入库',
  RETURN_OF_GOODS_IN = '销退入库',
  INVENTORY_WIN_IN = '盘盈入库',
}
export const InputBillTypeStatusOptions = {
  [InputBillTypeStatus.PURCHASE_IN]: { text: InputBillTypeStatusName.PURCHASE_IN, status: '' },
  [InputBillTypeStatus.TRANSFER_IN]: {
    text: InputBillTypeStatusName.TRANSFER_IN,
    status: '',
  },
  [InputBillTypeStatus.RETURN_OF_GOODS_IN]: {
    text: InputBillTypeStatusName.RETURN_OF_GOODS_IN,
    status: '',
  },
  [InputBillTypeStatus.INVENTORY_WIN_IN]: {
    text: InputBillTypeStatusName.INVENTORY_WIN_IN,
    status: '',
  },
};
