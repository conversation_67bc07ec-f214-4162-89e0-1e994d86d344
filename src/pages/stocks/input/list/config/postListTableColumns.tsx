import AuthButton from '@/components/common/AuthButton';
import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import type { ProColumns } from '@ant-design/pro-components';
import { Link, useIntl } from '@umijs/max';
import { Space } from 'antd';
import { InPutStatus, inPutStatusOptions } from '../types/InPutStatus';
import { InputBillTypeStatusOptions } from '../types/InputBillTypeStatus';
import type { InputPostEntity } from '../types/input.post.entity';

export interface PostListTableColumnsProps {
  handleInPut: (orderNo: string, warehouseId: string) => void;
  handleDetail: (orderId: string) => void;
}

export const PostListTableColumns = (props: PostListTableColumnsProps): ProColumns<InputPostEntity>[] => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      fixed: 'left',
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.list.label.businessOrderNo' }),
      dataIndex: 'origBillNo',
      key: 'origBillNo',
      search: true,
      width: 160,
      order: 8,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.list.label.inputOrderNo' }),
      dataIndex: 'bizBillNo',
      key: 'bizBillNo',
      search: true,
      order: 7,
      width: 150,
      render: (_, record) => (
        <Link
          to={{
            pathname: '/stocks/input/detail',
            search: '?inputId=' + record.id + '&inputWarehouseId=' + record.warehouseId,
          }}
        >
          {_}
        </Link>
      ),
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.list.label.goodsInfo' }),
      dataIndex: 'keyword',
      hideInTable: true,
      fieldProps: {
        placeholder: intl.formatMessage({ id: 'stocks.input.list.placeholder.goodsNameOrCode' }),
      },
      search: true,
      order: 6,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.list.label.inputType' }),
      dataIndex: 'billType',
      key: 'billType',
      search: {
        transform: (value: string) => {
          return { billTypeList: [value] };
        },
      },
      width: 80,
      valueEnum: InputBillTypeStatusOptions,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.list.label.inputStatus' }),
      dataIndex: 'state',
      key: 'state',
      search: {
        transform: (value: string) => {
          return { stateList: [value] };
        },
      },
      width: 100,
      valueEnum: inPutStatusOptions,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.list.label.notifyInputTime' }),
      dataIndex: 'createTime',
      key: 'createTime',
      search: false,
      width: 140,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.list.label.notifyInputTime' }),
      dataIndex: 'createTimeAt',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            startCreateTime: value[0],
            endCreateTime: value[1],
          };
        },
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.list.label.inputCompleteTime' }),
      dataIndex: 'realInTime',
      key: 'realInTime',
      search: false,
      width: 140,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.list.label.inputCompleteTime' }),
      dataIndex: 'realInTimeAt',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            startRealInTime: value[0],
            endRealInTime: value[1],
          };
        },
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.list.label.inputWarehouse' }),
      dataIndex: 'warehouseName',
      key: 'warehouseName',
      order: 5,
      search: {
        transform: (value: string) => {
          return { warehouseIdList: [value] };
        },
      },
      width: 120,
      fieldProps: {
        fieldNames: { label: 'warehouseName', value: 'id' },
      },
      request: (query: any) => {
        return warehouseList({}).then((s) => {
          return s.warehouseSimpleRoList;
        });
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.list.label.inputQuantity' }),
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      search: false,
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.list.label.sender' }),
      dataIndex: 'promoter',
      key: 'promoter',
      search: false,
      ellipsis: true,
      width: 120,
    },
    {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 100,
      fixed: 'right',
      render: (text, record) => (
        <Space>
          {(InPutStatus.AUDITING == record.state || InPutStatus.TO_ARRIVAL == record.state) && (
            <AuthButton
              isHref
              authority="inWarehouse"
              onClick={() => props.handleInPut(record.id ?? '', record.warehouseId ?? '')}
            >
              {intl.formatMessage({ id: 'stocks.input.list.button.input' })}
            </AuthButton>
          )}
          {YesNoStatus.YES == record.existBatchRecord && (
            <AuthButton
              isHref
              authority="inWarehouseLog"
              onClick={() => props.handleDetail(record.id ?? '')}
            >
              {intl.formatMessage({ id: 'stocks.input.list.button.inputRecord' })}
            </AuthButton>
          )}
        </Space>
      ),
    },
  ] as ProColumns<InputPostEntity>[];
};
