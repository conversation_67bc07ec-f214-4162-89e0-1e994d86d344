import FunProTable from '@/components/common/FunProTable';
import { CommonModelForm } from '@/types/CommonModelForm';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ActionType } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import { confirmInPutPost, queryInputPutPagePost } from '../services';
import InputDetailModal from './components/InputDetailModal';
import InputModal from './components/InputModal';
import { PostListTableColumns } from './config/postListTableColumns';
import { InputPostEntity } from './types/input.post.entity';

const InputList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();

  const [inputModalProps, setInputModalProps] = useState<CommonModelForm<string, InputPostEntity>>({
    visible: false,
    recordId: '',
    readOnly: false,
    title: '',
  });

  const [inputDetailModalProps, setInputDetailModalProps] = useState<
    CommonModelForm<string, InputPostEntity>
  >({
    visible: false,
    recordId: '',
    readOnly: false,
    title: '',
  });


  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 入库页面
   * @param ids
   */
  const handleInPut = async (id: string, warehouseId: string) => {
    setInputModalProps({ visible: true, recordId: id, title: intl.formatMessage({ id: 'stocks.input.modal.title.input' }) });
    actionRef?.current?.reload();
  };
  /**
   * 入库记录
   * @param id
   */
  const handleDetail = async (id: string) => {
    setInputDetailModalProps({ visible: true, recordId: id, title: intl.formatMessage({ id: 'stocks.input.modal.title.inputRecord' }) });
    actionRef?.current?.reload();
  };

  /**
   * 确认入库
   * @param values
   */
  const hanleConfirm = async (values: any) => {
    if (values?.stockInDetailRoList) {
      delete values.stockInDetailRoList;
    }
    const data = await confirmInPutPost(values);
    if (data) {
      hideModal();
      actionRef?.current?.reload();
    }
  };

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setInputModalProps({ visible: false, recordId: '', readOnly: false, title: '' });
  };
  const hideDetailModal = () => {
    setInputDetailModalProps({ visible: false, recordId: '', readOnly: false, title: '' });
  };

  return (
    <PageContainer>
      <FunProTable<InputPostEntity, any>
        rowKey="id"
        requestPage={queryInputPutPagePost}
        scroll={{ x: 1300 }}
        actionRef={actionRef}
        search={{ labelWidth: 100, defaultCollapsed: false }}
        columns={PostListTableColumns({ handleInPut, handleDetail })}
      />
      <InputModal {...inputModalProps} onOk={hanleConfirm} onCancel={hideModal} />
      <InputDetailModal {...inputDetailModalProps} onCancel={hideDetailModal} />
    </PageContainer>
  );
};
export default withKeepAlive(InputList);
