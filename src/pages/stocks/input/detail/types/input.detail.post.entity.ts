import { InPutStatus } from '../../list/types/InPutStatus';

export interface InPutDetailPostEntity {
  /**
   * 入库明细
   */
  stockInDetailRoList?: StockInDetailRoList[];
  /**
   * 入库主单明细
   */
  stockInRo?: StockInRo;
}

export interface StockInDetailRoList {
  /**
   * 品牌Id
   */
  brandId?: string;
  locationCode?: string;
  /**
   * 品牌名称
   */
  brandName?: string;
  /**
   * 品牌件号
   */
  brandPartNo?: string;
  /**
   * 类目Id
   */
  categoryId?: string;
  /**
   * 类目名称
   */
  categoryName?: string;
  /**
   * 货位备注
   */
  code?: string;
  /**
   * 成本价
   */
  costPrice?: number;
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * 商品ID
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * oe码
   */
  oeNo?: string;
  /**
   * 预入库数量
   */
  preAmount?: number;
  /**
   * 实际入库数量
   */
  realAmount?: number;
  /**
   * 剩余数量（预入库数量-实际入库数量）
   */
  remainAmount?: number;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 扫描数量
   */
  scanAmount?: number;
  /**
   * 入库ID
   */
  stockInId?: string;
  /**
   * 三方明细行id
   */
  thirdDetailId?: string;
  /**
   * 单位id
   */
  unitId?: string;
  /**
   * 单位名称
   */
  unitName?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
  /**
   * 版本号
   */
  version?: number;
}

/**
 * 入库主单明细
 */
export interface StockInRo {
  /**
   * 业务类型
   */
  billType?: number;
  /**
   * 业务类型描述
   */
  billTypeDesc?: string;
  /**
   * 入库单号
   */
  bizBillNo?: string;
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 1:存在，0：不存在
   */
  existBatchRecord?: number;
  /**
   * 主键
   */
  id?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * 原单号
   */
  origBillNo?: string;
  /**
   * 原三方单号，比如销退入库对应的XSD单
   */
  origThirdNo?: string;
  /**
   * 发货方
   */
  promoter?: string;
  /**
   * 验货人
   */
  realInInspectPerson?: string;
  /**
   * 实际入库人
   */
  realInPerson?: string;
  /**
   * 实际入库时间
   */
  realInTime?: string;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 0.取消1.待入库2.已入库3.部分入库
   */
  state?: InPutStatus;
  /**
   * 出库单状态描述：0取消1.待出库2.已出库3.部分出库
   */
  stateDesc?: string;
  /**
   * 计划出库商品总数
   */
  totalAmount?: number;
  /**
   * 计划出库商品种类总和
   */
  totalItem?: number;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
  /**
   * 入库仓库id
   */
  warehouseId?: string;
  /**
   * 出库仓库
   */
  warehouseIdOut?: string;
  /**
   * 入库仓库名称
   */
  warehouseName?: string;
  /**
   * 出库仓库名称
   */
  warehouseOutName?: string;
}
