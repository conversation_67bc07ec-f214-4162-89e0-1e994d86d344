import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import SubTitle from '@/components/common/SubTitle';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { CommonModelForm } from '@/types/CommonModelForm';
import {
  PageContainer,
  ProCard,
  ProDescriptions,
  ProDescriptionsActionType,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Space, Tag } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { isEmpty } from 'lodash';
import { useRef, useState } from 'react';
import { useSearchParams } from 'umi';
import InputModal from '../list/components/InputModal';
import { InPutStatus, inPutStatusOptions } from '../list/types/InPutStatus';
import { InputBillTypeStatusOptions } from '../list/types/InputBillTypeStatus';
import { InputPostEntity } from '../list/types/input.post.entity';
import { confirmInPutPost, queryInPutDetailPost } from '../services';
import { InputPostListTableColumns } from './config/inputPostListTableColumns';
import { InPutDetailPostEntity } from './types/input.detail.post.entity';
export default () => {
  const intl = useIntl();
  const [form] = useForm();
  const actionRef = useRef<ProDescriptionsActionType>();
  let [searchParams, setSearchParams] = useSearchParams();
  let [recordData, setRecordData] = useState<InPutDetailPostEntity>({});

  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  const [inputModalProps, setInputModalProps] = useState<CommonModelForm<string, InputPostEntity>>({
    visible: false,
    recordId: '',
    readOnly: false,
    title: '',
  });

  const handleInput = (id: string, warehouseId: string) => {
    setInputModalProps({ visible: true, recordId: id, title: intl.formatMessage({ id: 'stocks.input.modal.title.input' }) });
  };

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setInputModalProps({ visible: false, recordId: '', readOnly: false, title: '' });
  };

  /**
   * 确认入库
   * @param values
   */
  const hanleConfirm = async (values: any) => {
    if (values?.stockInDetailRoList) {
      delete values.stockInDetailRoList;
    }
    const data = await confirmInPutPost(values);
    if (data) {
      hideModal();
      actionRef?.current?.reload();
    }
  };

  return (
    <PageContainer>
      <ProCard>
        <ProDescriptions
          actionRef={actionRef}
          title={
            <Space>
              <span>{recordData?.stockInRo?.bizBillNo}</span>
              <Tag color={inPutStatusOptions[recordData?.stockInRo?.state!]?.status}>
                {inPutStatusOptions[recordData?.stockInRo?.state!]?.text}
              </Tag>
            </Space>
          }
          extra={
            <Space>
              <AuthButton
                authority="inWarehousePrint"
                danger
                onClick={() => {
                  window.open(
                    `/print?stockInId=${searchParams.get('inputId')}&printType=${
                      PrintType.inStockOrder
                    }`,
                  );
                }}
              >
                {intl.formatMessage({ id: 'stocks.input.detail.button.print' })}
              </AuthButton>
              {(InPutStatus.AUDITING == recordData?.stockInRo?.state ||
                InPutStatus.TO_ARRIVAL == recordData?.stockInRo?.state) && (
                  <AuthButton
                    type="primary"
                    authority="inWarehouse"
                    onClick={() =>
                      handleInput(recordData?.stockInRo?.id!, recordData?.stockInRo?.warehouseId!)
                    }
                  >
                    {intl.formatMessage({ id: 'stocks.input.list.button.input' })}
                  </AuthButton>
                )}
            </Space>
          }
          params={{
            stockInId: searchParams.get('inputId'),
            warehouseId: searchParams.get('inputWarehouseId'),
          }}
          request={async (params) => {
            if (!isEmpty(params?.stockInId) && !isEmpty(params?.warehouseId)) {
              const data = await queryInPutDetailPost({ ...params });
              if (data) {
                setRecordData(data);
              }
              return { data, success: true };
            }
            return Promise.resolve({
              success: false,
              data: {},
            });
          }}
          column={4}
        >
          <ProDescriptions.Item dataIndex={['stockInRo', 'origBillNo']} label={intl.formatMessage({ id: 'stocks.input.detail.label.businessOrderNo' })} />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'stocks.input.detail.label.inputType' })}
            dataIndex={['stockInRo', 'billType']}
            valueEnum={InputBillTypeStatusOptions}
          />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'stocks.input.detail.label.notifyInputTime' })} dataIndex={['stockInRo', 'createTime']} />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'stocks.input.detail.label.inputCompleteTime' })} dataIndex={['stockInRo', 'realInTime']} />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'stocks.input.detail.label.inputWarehouse' })} dataIndex={['stockInRo', 'warehouseName']} />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'stocks.input.detail.label.inputQuantity' })} dataIndex={['stockInRo', 'totalAmount']} />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'stocks.input.detail.label.sender' })} dataIndex={['stockInRo', 'promoter']} />
        </ProDescriptions>
      </ProCard>
      <FunProTable
        className="mt-4"
        headerTitle={<SubTitle text={intl.formatMessage({ id: 'stocks.input.detail.subtitle.goodsDetail' })} />}
        columns={InputPostListTableColumns({ isDetail: true, isEdit: false })}
        rowKey="id"
        search={false}
        pagination={false}
        dataSource={recordData?.stockInDetailRoList}
        scroll={{ x: 1300 }}
      />
      <InputModal {...inputModalProps} onOk={hanleConfirm} onCancel={hideModal} />
    </PageContainer>
  );
};
