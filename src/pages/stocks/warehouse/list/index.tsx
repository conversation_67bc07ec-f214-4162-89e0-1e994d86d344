import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import type { CommonModelForm } from '@/types/CommonModelForm';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ActionType } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { Space } from 'antd';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import { useIntl } from 'umi';
import { createPost, modifyPost, modifyStatusPost, queryWarehouseList } from '../services';
import FormModal from './components/modal';
import { PostListTableColumns } from './config/postListTableColumns';
import type { PostEntity } from './types/post.entity';

const WarehouseList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [warehouseModalProps, setWarehouseModalProps] = useState<
    CommonModelForm<string, PostEntity>
  >({
    visible: false,
    recordId: '0',
    readOnly: false,
    title: '',
  });

  useActivate(() => {
    actionRef.current?.reload();
  });

  const handleUpdateItem = async (id: string) => {
    setWarehouseModalProps({
      visible: true,
      recordId: id,
      readOnly: false,
      title: intl.formatMessage({ id: 'stocks.warehouse.button.editWarehouse' }),
    });
  };

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setWarehouseModalProps({ visible: false, recordId: '0', readOnly: false, title: '' });
  };

  /**
   * 禁用事件
   * @param ids
   */
  const handleDeleteItem = async (id: string, state: number) => {
    await modifyStatusPost({ id, state });
    actionRef.current?.reload(true);
  };

  /**
   * 新增或编辑
   * @param values
   */
  const handleSaveOrUpdate = async (values: PostEntity) => {
    try {
      const { id } = values;
      let result;
      if (id) {
        //编辑
        result = await modifyPost({ ...values });
      } else {
        //新增
        result = await createPost({ ...values });
      }
      const { code } = result;
      if (code == 0) {
        hideModal();
        actionRef.current?.reload(true);
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  return (
    <PageContainer>
      <FunProTable<PostEntity, any>
        rowKey="id"
        requestPage={queryWarehouseList}
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        columns={PostListTableColumns({
          handleUpdateItem,
          handleDeleteItem,
          intl,
        })}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              key="primary"
              authority="addWarehouse"
              onClick={() =>
                setWarehouseModalProps({
                  visible: true,
                  recordId: '0',
                  readOnly: false,
                  title: intl.formatMessage({ id: 'stocks.warehouse.button.addWarehouse' }),
                })
              }
            >
              {intl.formatMessage({ id: 'stocks.warehouse.button.addWarehouse' })}
            </AuthButton>
          </Space>
        }
      />
      <FormModal {...warehouseModalProps} onOk={handleSaveOrUpdate} onCancel={hideModal} />
    </PageContainer>
  );
};
export default withKeepAlive(WarehouseList);
