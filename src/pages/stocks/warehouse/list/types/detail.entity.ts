export interface DetailEntity {
  /**
   * 仓库id
   */
  id?: string;
  /**
   * None
   */
  memberId?: string;

  /**
   * 默认门店ID
   */
  storeId?: string;
  /**
   * 关联门店ID集合
   */
  storeIdList?: string[];
  /**
   * 仓库名称
   */
  warehouseName?: string;
  /**
   * 明细
   */
  warehouseStoreInfoRoList?: WarehouseStoreInfoRoList[];
}

export interface WarehouseStoreInfoRoList {
  /**
   * None
   */
  memberId?: string;
  /**
   * 门店id
   */
  storeId?: string;
  /**
   * 门店名称
   */
  storeName?: string;
  /**
   * 关联门店是否存在：1是，0否
   */
  warehouseRelateStoreExist?: number;
}
