export interface PostEntity {
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 来源：1来源门店系统；2.自定义
   */
  sourceType?: number;
  /**
   * 状态1正常0删除
   */
  state?: number;
  /**
   * 门店ID（唯一） 默认
   */
  storeId?: string;

  storeIdList?: string[];

  storeName?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
  /**
   * 仓库别名
   */
  warehouseAlias?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
}
