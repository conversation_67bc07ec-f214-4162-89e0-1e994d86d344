import { PaginationRequest } from '@/types/PaginationRequest';

export interface QueryPostListRequest extends PaginationRequest {
  /**
   * 仓库id
   */
  idList?: string[];
  /**
   * None
   */
  memberId?: string;
  /**
   * None
   */
  pageNo?: number;
  /**
   * None
   */
  pageSize?: number;
  /**
   * None
   */
  startRow?: number;
  /**
   * 门店id
   */
  storeIdList?: string[];
  /**
   * 仓库名称模糊搜索
   */
  warehouseNameKeyword?: string;
}
