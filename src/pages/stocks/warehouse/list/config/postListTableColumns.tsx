import AuthButton from '@/components/common/AuthButton';
import {
  EnablePostStatusOptions,
  setStatusValue,
  statusAttribute,
} from '@/types/EnableDisableStatus';
import type { ProColumns } from '@ant-design/pro-components';
import { Popconfirm, Space } from 'antd';
import type { IntlShape } from 'react-intl';
import type { PostEntity } from '../types/post.entity';

export interface PostListTableColumnsProps {
  handleUpdateItem: (id: string) => void;
  handleDeleteItem: (id: string, state: number) => void;
  intl: IntlShape;
}

export const PostListTableColumns = (props: PostListTableColumnsProps) => {
  const { intl } = props;
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'stocks.warehouse.label.warehouseName' }),
      dataIndex: 'warehouseName',
      key: 'warehouseName',
      search: false,
      ellipsis: true,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.warehouse.label.warehouseName' }),
      dataIndex: 'warehouseNameKeyword',
      key: 'warehouseNameKeyword',
      search: true,
      hideInTable: true,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.warehouse.label.affiliatedStore' }),
      dataIndex: 'storeName',
      key: 'storeName',
      search: false,
      ellipsis: true,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'common.label.remark' }),
      dataIndex: 'remark',
      key: 'remark',
      search: false,
      ellipsis: true,
      width: 120,
    },

    {
      title: intl.formatMessage({ id: 'common.column.createTime' }),
      dataIndex: 'createTime',
      key: 'createTime',
      search: false,
      width: 140,
    },
    {
      title: intl.formatMessage({ id: 'common.column.status' }),
      dataIndex: 'state',
      key: 'state',
      search: false,
      width: 80,
      valueEnum: EnablePostStatusOptions,
    },
    {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 100,
      fixed: 'right',
      render: (text, record) => (
        <Space>
          <AuthButton
            isHref
            authority="editWarehouse"
            onClick={() => props.handleUpdateItem(record.id ?? '')}
          >
            {intl.formatMessage({ id: 'common.button.edit' })}
          </AuthButton>
          <Popconfirm
            title={intl.formatMessage({ id: 'stocks.warehouse.confirm.enableDisable' }, { action: statusAttribute[record.state as number] === EnablePostStatusOptions[1].text ? intl.formatMessage({ id: 'common.option.enable' }) : intl.formatMessage({ id: 'common.option.disable' }) })}
            onConfirm={() =>
              props.handleDeleteItem(record.id ?? '', Number(setStatusValue[record.state as number]))
            }
          >
            <AuthButton authority="enableOrDisableWarehouse" isHref>
              {statusAttribute[record.state as number] === EnablePostStatusOptions[1].text ? intl.formatMessage({ id: 'common.button.enable' }) : intl.formatMessage({ id: 'common.button.disable' })}
            </AuthButton>
          </Popconfirm>
        </Space>
      ),
    },
  ] as ProColumns<PostEntity>[];
};
