import { PageResponseDataType } from '@/types/PageResponseDataType';
import { ResponseDataType } from '@/types/ResponseDataType';
import { request } from '@/utils/request';
import { DetailEntity } from './list/types/detail.entity';
import { ListEntity } from './list/types/list.entity';
import { PostEntity } from './list/types/post.entity';
import { QueryPostDetailRequest } from './list/types/query.post.detail.request';
import { QueryPostListRequest } from './list/types/query.post.list.request';

/**
 * 列表查询
 * @param params
 * @returns
 */
export const queryWarehouseList = async (params: Partial<QueryPostListRequest>) => {
  return request<PageResponseDataType<PostEntity>>(`/ipmswarehouse/warehouse/queryByPage`, {
    data: params,
  });
};

/**
 * 详情查询
 * @param params
 * @returns
 */
export const queryPostDetail = async (params: Partial<QueryPostDetailRequest>) => {
  return request<DetailEntity>(`/ipmswarehouse/warehouse/queryRelationByIdList`, {
    data: params,
  });
};
/**
 * 详情查询
 * @param params
 * @returns
 */
export const queryRelationStoreByIdPost = async (params: Partial<QueryPostDetailRequest>) => {
  return request<DetailEntity>(`/ipmswarehouse/warehouse/queryRelationStoreById`, {
    data: params,
  });
};

/**
 * 新增
 * @param params
 * @returns
 */
export const createPost = async (params: Partial<PostEntity>) => {
  return request<ResponseDataType<PostEntity>>(`/ipmswarehouse/warehouse/create`, {
    origin: true,
    data: params,
  });
};
/**
 * 编辑
 * @param params
 * @returns
 */
export const modifyPost = async (params: Partial<PostEntity>) => {
  return request<ResponseDataType<PostEntity>>(`/ipmswarehouse/warehouse/edit`, {
    origin: true,
    data: params,
  });
};

/**
 * 修改状态
 * @param params
 * @returns
 */
export const modifyStatusPost = async (params: Partial<PostEntity>) => {
  return request<boolean>(`/ipmswarehouse/warehouse/enableOrDisable`, {
    data: params,
  });
};
/**
 * 查询所有仓库列表
 * @param params
 * @returns
 */
export const warehouseList = async (params?: Partial<QueryPostDetailRequest>) => {
  return request<ListEntity>(`/ipmswarehouse/warehouseStoreRelation/queryList`, {
    data: params,
  });
};
