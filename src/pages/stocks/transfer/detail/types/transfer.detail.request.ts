import type { PaginationRequest } from '@/types/PaginationRequest';

export interface TransferDetailRequest extends PaginationRequest {
  /**
     * 是否编辑场景
     */
  isEdit?: boolean;
  /**
   * 商品编码商品名称模糊搜索
   */
  keyword?: string;
  /**
   * 调拨主表id
   */
  transferId?: string;
  /**
   * 出库仓库id
   */
  warehouseIdOut?: string;
}

/**
 * 详情页面调拨单详情请求对象
 */
export interface TransferSimpleRequest {
  /**
   * 调拨单号
   */
  bizBillNo?: string;
  /**
   * 调拨id
   */
  id?: string;
}

/**
 * 详情页面删除商品行请求对象
 */
export interface DetailDeleteRequest {
  /**
   * 明细行id
   */
  idList?: string[];
  /**
   * 调拨单主表ID
   */
  transferId?: string;
}