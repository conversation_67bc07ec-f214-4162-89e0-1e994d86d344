import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import type { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { StockInOutEntity } from './detail/types/stock.inout.post.entity';
import type { TransferDetailPostEntity } from './detail/types/transfer.detail.post.entity';
import type {
  DetailDeleteRequest,
  TransferDetailRequest,
  TransferSimpleRequest,
} from './detail/types/transfer.detail.request';
import type { QueryPostListRequest } from './list/types/query.post.list.request';
import type { TransferPostEntity } from './list/types/transfer.post.entity';
import type { TransferGoodsEntity } from './operation/components/goodsModal/types/transfer.goods.entity';
import type { TransferGoodsRequest } from './operation/components/goodsModal/types/transfer.goods.request';
import type { ChangeWarehouseRequest } from './operation/types/change.warehouse.request';
import type { CreateTransferRequest } from './operation/types/create.transfer.request';
import type { SubmitTransferRequest } from './operation/types/submit.transfer.request';

/**
 * 列表查询-调拨管理页面
 * @param params
 * @returns
 */
export const queryCheckPagePost = async (
  params: Partial<QueryPostListRequest & PageRequestParamsType>,
) => {
  return request<PageResponseDataType<TransferPostEntity>>(
    `/ipmswarehouse/stocktransfer/queryByPage`,
    {
      data: params,
    },
  );
};

/**
 * 详情查询-调拨管理详情页
 * @param params
 * @returns
 */
export const queryTransferByIdOrNo = async (params: Partial<TransferSimpleRequest>) => {
  return request<TransferPostEntity>(`/ipmswarehouse/stocktransfer/queryByIdOrNo`, {
    data: params,
  });
};

/**
 * 详情查询-调拨商品列表
 * @param params
 * @returns
 */
export const queryTransferPostDetail = async (
  params: Partial<TransferDetailRequest & PageRequestParamsType>,
) => {
  return request<PageResponseDataType<TransferDetailPostEntity>>(
    `/ipmswarehouse/stocktransfer/queryDetailByPage`,
    {
      data: params,
    },
  );
};

/**
 * 作废调拨单
 * @param params
 * @returns
 */
export const cancelTransferPost = async (params: Partial<TransferSimpleRequest>) => {
  return request<boolean>(`/ipmswarehouse/stocktransfer/cancel`, {
    data: params,
  });
};

/**
 * 创建调拨单
 * @param params
 * @returns
 */
export const transferCreateOrUpdatePost = async (params: Partial<CreateTransferRequest>) => {
  console.log(params, 'params');
  return request<TransferPostEntity>(`/ipmswarehouse/stocktransfer/createOrUpdate`, {
    data: params,
  });
};

/**
 * 提交调拨单
 * @param params
 * @returns
 */
export const submitTransferPost = async (params: Partial<SubmitTransferRequest>) => {
  return request<boolean>(`/ipmswarehouse/stocktransfer/confirm`, {
    data: params,
  });
};

/**
 * 一键出库
 * @param params
 * @returns
 */
export const stockOutPost = async (params: Partial<StockInOutEntity>) => {
  return request<boolean>(`/ipmswarehouse/stocktransfer/oneClickStockOut`, {
    data: params,
  });
};

/**
 * 一键入库
 * @param params
 * @returns
 */
export const stockInPost = async (params: Partial<StockInOutEntity>) => {
  return request<boolean>(`/ipmswarehouse/stocktransfer/oneClickStockIn`, {
    data: params,
  });
};

/**
 *
 * 添加商品-详情查询（商品列表）
 * @param params
 * @returns
 */
export const queryTransferGoodsPost = async (
  params: Partial<TransferGoodsRequest> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<TransferGoodsEntity>>(
    `/ipmswarehouse/stocktransfer/queryItemByPage`,
    {
      data: params,
    },
  );
};

/**
 * 变更仓库
 * @param params
 * @returns
 */
export const changeWarehousePost = async (params: Partial<ChangeWarehouseRequest>) => {
  return request<boolean>(`/ipmswarehouse/stocktransfer/changeWarehouse`, {
    data: params,
  });
};

/**
 * 删除商品行
 * @param params
 * @returns
 */
export const deleteDetailPost = async (params: Partial<DetailDeleteRequest>) => {
  console.log(params, 'params');
  return request<boolean>(`/ipmswarehouse/stocktransfer/deleteDetail`, {
    data: params,
  });
};
