import ColumnRender from "@/components/ColumnRender";
import { queryGoodsPropertyPage } from '@/pages/goods/property/services';
import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { TransferGoodsEntity } from '../types/transfer.goods.entity';

export interface PostListTableColumnsProps {
  warehouseIdOut?: string;
}

export const PostListTableColumns = (props: PostListTableColumnsProps): ProColumns<TransferGoodsEntity>[] => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'transfer.operation.goodsModal.columns.itemCode' }),
      dataIndex: 'itemSn',
      search: false,
      hideInSearch: true,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'transfer.operation.goodsModal.columns.itemName' }),
      dataIndex: 'itemName',
      search: false,
      fixed: 'left',
      width: 140,
    },
    {
      dataIndex: 'keyword',
      search: true,
      hideInTable: true,
      fieldProps: {
        placeholder: intl.formatMessage({ id: 'transfer.operation.goodsModal.placeholder.goodsSearch' }),
      },
      formItemProps: {
        tooltip: intl.formatMessage({ id: 'transfer.operation.goodsModal.tooltip.goodsSearch' }),
        label: intl.formatMessage({ id: 'transfer.operation.goodsModal.label.goodsInfo' }),
        labelCol: { span: 6 },
      },
    },
    {
      title: intl.formatMessage({ id: 'transfer.operation.goodsModal.columns.oeNo' }),
      dataIndex: 'oeNo',
      search: false,
      width: 140,
      renderText: (text: string) => ColumnRender.ArrayColumnRender(text?.split(',')),
    },
    {
      title: intl.formatMessage({ id: 'transfer.operation.goodsModal.columns.brandPartNo' }),
      dataIndex: 'brandPartNo',
      search: false,
      width: 100,
      renderText: (text: string) => ColumnRender.ArrayColumnRender(text?.split(',')),
    },
    {
      title: intl.formatMessage({ id: 'transfer.operation.goodsModal.columns.brand' }),
      dataIndex: 'brandIdList',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        mode: 'multiple',
        showSearch: true,
      },
      request: async () => {
        const { data } = await queryGoodsPropertyPage({ pageNo: 1, pageSize: 1000 }, 'brand');
        return data.map((t) => ({
          label: t.brandName,
          value: t.brandId,
        }));
      },
    },
    {
      title: intl.formatMessage({ id: 'transfer.operation.goodsModal.columns.brand' }),
      dataIndex: 'brandName',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'transfer.operation.goodsModal.columns.category' }),
      dataIndex: 'categoryIdList',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        showSearch: true,
      },
      request: async () => {
        const { data } = await queryGoodsPropertyPage({ pageNo: 1, pageSize: 1000 }, 'category');
        return data.map((t) => ({
          label: t.categoryName,
          value: t.categoryId,
        }));
      },
    },
    {
      title: intl.formatMessage({ id: 'transfer.operation.goodsModal.columns.unit' }),
      dataIndex: 'unitName',
      width: 50,
      search: false,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'transfer.operation.goodsModal.columns.outWarehouseStock' }),
      dataIndex: 'inventoryNum',
      width: 100,
      search: false,
      ellipsis: true,
    },
  ] as ProColumns<TransferGoodsEntity>[];
};
