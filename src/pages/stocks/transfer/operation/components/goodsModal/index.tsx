import FunProTable from '@/components/common/FunProTable';
import SubTitle from '@/components/common/SubTitle';
import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import type { CommonModelForm } from '@/types/CommonModelForm';
import type { ProFormInstance } from '@ant-design/pro-components';
import { DrawerForm } from '@ant-design/pro-components';
import { useIntl, useModel } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Checkbox, Flex } from 'antd';
import { isEmpty } from 'lodash';
import { useRef, useState } from 'react';
import { queryTransferGoodsPost } from '../../../services';
import { PostListTableColumns } from './config/GoodsTableColumns';
import type { TransferGoodsEntity } from './types/transfer.goods.entity';

export default (props: CommonModelForm<string, any>) => {
  const intl = useIntl();
  const { initialState, setInitialState } = useModel('@@initialState');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [warehouseIdOut, setWarehouseIdOut] = useState('');
  const formRef = useRef<ProFormInstance>();
  const [onlyHaveInv, setOnlyHaveInv] = useState<YesNoStatus>(YesNoStatus.YES); //有库存
  useAsyncEffect(async () => {
    if (props?.recordId) {
      setWarehouseIdOut(props?.recordId);
    }
  }, [props.visible]);

  return (
    <DrawerForm
      onFinish={async (value) => {
        props.onOk!({ itemId: selectedRowKeys });
        setSelectedRowKeys([]); //清空
      }}
      drawerProps={{
        onClose: props.onCancel,
        styles: { body: { backgroundColor: '#F2F2F2' } },
        destroyOnClose: true,
      }}
      layout="horizontal"
      title={props.title}
      open={props.visible}
      formRef={formRef}
      width={1200}
    >
      <FunProTable<TransferGoodsEntity, any>
        rowKey="itemId"
        title={() => (
          <Flex justify="space-between">
            <SubTitle text={intl.formatMessage({ id: 'transfer.operation.goodsModal.title.goodsList' })} />
            <Checkbox
              className="mb-0"
              defaultChecked
              onChange={(e) => {
                if (e.target?.checked) {
                  setOnlyHaveInv(YesNoStatus.YES);
                } else {
                  setOnlyHaveInv(YesNoStatus.NO);
                }
              }}
            >
              {intl.formatMessage({ id: 'transfer.operation.goodsModal.checkbox.onlyWithStock' })}
            </Checkbox>
          </Flex>
        )}
        params={{ onlyHaveInv, warehouseIdOut }}
        requestPage={async (params) => {
          if (!isEmpty(params.warehouseIdOut)) {
            return await queryTransferGoodsPost(params);
          }
          return { data: [] };
        }}
        search={{ labelWidth: 100, defaultCollapsed: false }}
        rowSelection={{
          selectedRowKeys: selectedRowKeys,
          onChange: (selectedKeys) => {
            setSelectedRowKeys(selectedKeys);
          },
        }}
        columns={PostListTableColumns({ warehouseIdOut })}
      />
    </DrawerForm>
  );
};
