export interface CreateTransferRequest {
  /**
   * 调拨单号
   */
  bizBillNo?: string;
  /**
   * 制单人
   */
  createDocPerson?: string;
  /**
   * 制单人工号
   */
  createDocPersonNo?: string;
  /**
   * None
   */
  extRemark?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 调拨状态0:已作废,1:草稿2:待出库3:待入库4：已完成
   */
  state?: number;
  /**
   * 明细
   */
  stockTransferDetailCmdList?: StockTransferDetailCmdList[];
  /**
   * 调入门店
   */
  storeIdIn?: string;
  /**
   * 调出门店
   */
  storeIdOut?: string;
  /**
   * 总数量
   */
  totalAmount?: number;
  /**
   * 种类
   */
  totalItem?: number;
  /**
   * 入库仓库id
   */
  warehouseIdIn?: string;
  /**
   * 出库仓库id
   */
  warehouseIdOut?: string;
}

export interface StockTransferDetailCmdList {
  /**
   * 成本价
   */
  costPrice?: number;
  /**
   * None
   */
  extRemark?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 调拨主表id
   */
  transferId?: string;
  /**
   * 调拨数量
   */
  transferNum?: number;
}
