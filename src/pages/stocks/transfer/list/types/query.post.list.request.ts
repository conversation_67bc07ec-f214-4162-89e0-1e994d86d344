import type { PaginationRequest } from '@/types/PaginationRequest';

export interface QueryPostListRequest extends PaginationRequest {
  /**
    * 盘点单号
    */
  bizBillNo?: string;
  /**
   * 制单人工号
   */
  createDocPersonNo?: string;
  /**
   * 开始结束时间
   */
  endCreateTime?: string;
  /**
   * 支持输入商品编码和商品名称，模糊匹配查询
   */
  keyword?: string;
  /**
   * 开始创建时间
   */
  startCreateTime?: string;
  startRow?: number;
  /**
   * 调拨状态0:已作废,1:草稿2:待出库3:待入库4：已完成
   */
  stateList?: number[];
  /**
   * 调入门店
   */
  storeIdIn?: string;
  /**
   * 调出门店
   */
  storeIdOut?: string;
  /**
   * 入库仓库id
   */
  warehouseIdInList?: string[];
  /**
   * 调出仓库id
   */
  warehouseIdOutList?: string[];
}
