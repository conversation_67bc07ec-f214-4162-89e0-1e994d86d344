import type { TransferStatusEnum } from "./TransferStatusEnum";

export interface TransferPostEntity {
  /**
    * 调拨单号
    */
  bizBillNo?: string;
  /**
   * 制单人
   */
  createDocPerson?: string;
  /**
   * 制单人工号
   */
  createDocPersonNo?: string;
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 调拨状态0:已作废,1:草稿2:待出库3:待入库4：已完成
   */
  state?: TransferStatusEnum;
  /**
   * 调拨状态0:已作废,1:草稿2:待出库3:待入库4：已完成
   */
  stateDesc?: string;
  /**
   * 调入门店
   */
  storeIdIn?: string;
  /**
   * 调入门店
   */
  storeIdInName?: string;
  /**
   * 调出门店
   */
  storeIdOut?: string;
  /**
   * 调出门店
   */
  storeIdOutName?: string;
  /**
   * 总数量
   */
  totalAmount?: number;
  /**
   * 种类
   */
  totalItem?: number;
  /**
   * 入库仓库id
   */
  warehouseIdIn?: string;
  /**
   * 入库仓库名
   */
  warehouseIdInName?: string;
  /**
   * 出库仓库id
   */
  warehouseIdOut?: string;
  /**
   * 出库仓库名称
   */
  warehouseIdOutName?: string;
}
