import AuthButton from '@/components/common/AuthButton';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { accountListQuerySimple, queryStoreByAccount } from '@/pages/system/user/services';
import type { ProColumns } from '@ant-design/pro-components';
import { Link, history, useIntl } from '@umijs/max';
import { Space } from 'antd';
import { TransferStatusEnum, TransferStatusNameOptions } from '../types/TransferStatusEnum';
import type { CheckPostEntity } from '../types/transfer.post.entity';

export interface PostListTableColumnsProps { }

export const PostListTableColumns = (): ProColumns<CheckPostEntity>[] => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.list.label.transferOrderNo' }),
      dataIndex: 'bizBillNo',
      search: true,
      width: 150,
      render: (_, record) => (
        <Link to={{ pathname: '/stocks/transfer/detail', search: '?transferId=' + record.id }}>
          {_}
        </Link>
      ),
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.list.label.outStore' }),
      dataIndex: 'storeIdOutName',
      search: {
        transform: (value: string) => {
          return { storeIdOut: value };
        },
      },
      width: 120,
      request: async () => {
        const data = await queryStoreByAccount({});
        return data?.map(({ id, name }) => ({
          key: id,
          value: id,
          label: name,
        }));
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.list.label.outWarehouse' }),
      dataIndex: 'warehouseIdOutName',
      search: {
        transform: (value: string) => {
          return { warehouseIdOutList: [value] };
        },
      },
      width: 120,
      fieldProps: {
        fieldNames: { label: 'warehouseName', value: 'id' },
      },
      request: async (query: any) => {
        const s = await warehouseList({});
        return s.warehouseSimpleRoList;
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.list.label.inStore' }),
      dataIndex: 'storeIdInName',
      search: {
        transform: (value: string) => {
          return { storeIdIn: value };
        },
      },
      width: 120,
      request: async () => {
        const data = await queryStoreByAccount({});
        return data?.map(({ id, name }) => ({
          key: id,
          value: id,
          label: name,
        }));
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.list.label.inWarehouse' }),
      dataIndex: 'warehouseIdInName',
      search: {
        transform: (value: string) => {
          return { warehouseIdInList: [value] };
        },
      },
      width: 120,
      fieldProps: {
        fieldNames: { label: 'warehouseName', value: 'id' },
      },
      request: async (query: any) => {
        const s = await warehouseList({});
        return s.warehouseSimpleRoList;
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.list.label.documentStatus' }),
      dataIndex: 'state',
      search: {
        transform: (value: string) => {
          return { stateList: [value] };
        },
      },
      width: 80,
      valueEnum: TransferStatusNameOptions,
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.list.label.createTime' }),
      dataIndex: 'createTime',
      width: 140,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.list.label.createTime' }),
      dataIndex: 'createTime',
      valueType: 'dateRange',
      width: 140,
      hideInTable: true,
      search: {
        transform: (value: any) => {
          return {
            startCreateTime: value[0],
            endCreateTime: value[1],
          };
        },
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.list.label.creator' }),
      dataIndex: 'createDocPerson',
      width: 60,
      valueType: 'select',
      search: {
        transform: (value: string) => {
          return { createDocPersonNo: value };
        },
      },
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.list.label.remarks' }),
      dataIndex: 'remarks',
      search: false,
      ellipsis: true,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 80,
      fixed: 'right',
      render: (text, record) => (
        <Space>
          {TransferStatusEnum.DRAFT == record.state && (
            <AuthButton
              authority="editWarehouseTransfer"
              isHref
              onClick={() => {
                history.push(
                  '/stocks/transfer/operation?transferId=' +
                  record.id +
                  '&bizBillNo=' +
                  record.bizBillNo,
                );
              }}
            >
              {intl.formatMessage({ id: 'stocks.transfer.list.button.edit' })}
            </AuthButton>
          )}
        </Space>
      ),
    },
  ] as ProColumns<CheckPostEntity>[];
};
