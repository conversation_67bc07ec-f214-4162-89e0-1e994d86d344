import { CheckDetailPostEntity } from '@/pages/stocks/check/detail/types/check.detail.post.entity';
import {
  CheckDetailRequest,
  CheckPostCancelRequest,
  StockCheckSimpleRequest,
} from '@/pages/stocks/check/detail/types/check.detail.request';
import { QueryPostListRequest } from '@/pages/stocks/check/list/types/query.post.list.request';
import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { CheckApproveEntity } from './detail/types/check.approve.post.entity';
import { CheckPostEntity } from './list/types/check.post.entity';
import { GoodsCheckRquest } from './operation/components/goodsModal/config/goods.check.request';
import { CheckGoodsEntity } from './operation/components/goodsModal/types/check.goods.entity';
import { AddCheckRequest } from './operation/types/add.check.request';
import { DeleteCheckRequest } from './operation/types/delete.check.request';
import { SubmitCheckRequest } from './operation/types/submit.check.request';

/**
 * 列表查询
 * @param params
 * @returns
 */
export const queryCheckPagePost = async (params: Partial<QueryPostListRequest>) => {
  return request<PageResponseDataType<CheckPostEntity>>(`/ipmswarehouse/stockcheck/queryByPage`, {
    data: params,
  });
};

/**
 * 详情查询
 * @param params
 * @returns
 */
export const queryCheckByIdOrNo = async (params: Partial<StockCheckSimpleRequest>) => {
  return request<CheckPostEntity>(`/ipmswarehouse/stockcheck/queryByIdOrNo`, {
    data: params,
  });
};

/**
 * 详情查询
 * @param params
 * @returns
 */
export const queryCheckPostDetail = async (params: Partial<CheckDetailRequest>) => {
  return request<PageResponseDataType<CheckDetailPostEntity>>(
    `/ipmswarehouse/stockcheck/queryDetailByPage`,
    {
      data: params,
    },
  );
};

/**
 * 添加商品详情查询
 * @param params
 * @returns
 */
export const queryPostItemDetail = async (params: Partial<CheckDetailRequest>) => {
  return request<PageResponseDataType<CheckDetailPostEntity>>(
    `/ipmswarehouse/stockcheck/queryItemByPage`,
    {
      data: params,
    },
  );
};

/**
 * 取消
 * @param params
 * @returns
 */
export const cancelCheckPost = async (params: Partial<CheckPostCancelRequest>) => {
  return request<boolean>(`/ipmswarehouse/stockcheck/cancel`, {
    data: params,
  });
};

/**
 * 创建or更新盘点点
 * @param params
 * @returns
 */
export const checkCreateOrUpdatePost = async (params: Partial<AddCheckRequest>) => {
  return request<CheckPostEntity>(`/ipmswarehouse/stockcheck/createOrUpdate`, {
    data: params,
  });
};

/**
 * 待审核，审核驳回，更新盘点单状态
 * @param params
 * @returns
 */
export const changeCheckStatePost = async (params: Partial<CheckPostEntity>) => {
  return request<boolean>(`/ipmswarehouse/stockcheck/changeCheckState`, {
    data: params,
  });
};

/**
 * 盘点确认
 * @param params
 * @returns
 */
export const confirmCheckPost = async (params: Partial<SubmitCheckRequest>) => {
  return request<boolean>(`/ipmswarehouse/stockcheck/confirm`, {
    data: params,
  });
};
/**
 * 通过
 * @param params
 * @returns
 */
export const approveCheckPost = async (params: Partial<CheckApproveEntity>) => {
  return request<boolean>(`/ipmswarehouse/stockcheck/approval`, {
    data: params,
  });
};
/**
 * 拒绝
 * @param params
 * @returns
 */
export const rejectCheckPost = async (params: Partial<CheckApproveEntity>) => {
  return request<boolean>(`/ipmswarehouse/stockcheck/reject`, {
    data: params,
  });
};
/**
 * 删除明细
 * @param params
 * @returns
 */
export const modifyCheckDeletePost = async (params: Partial<DeleteCheckRequest>) => {
  return request<boolean>(`/ipmswarehouse/stockcheck/deleteDetail`, {
    data: params,
  });
};
/**
 *
 * @param params
 * @returns
 */
export const queryCheckGoodsPost = async (
  params: Partial<GoodsCheckRquest> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<CheckGoodsEntity>>(
    `/ipmswarehouse/stockcheck/queryItemByPage`,
    {
      data: params,
    },
  );
};
