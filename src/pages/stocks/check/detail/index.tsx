import ConfirmModal from '@/components/ConfirmModal';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import SubTitle from '@/components/common/SubTitle';
import { KeepAliveTabContext } from '@/layouts/context';
import AuditModal, { AuditModalProps } from '@/pages/purchase/detail/components/AuditModal';
import { AuditStatus } from '@/pages/purchase/detail/types/AuditStatus';
import { AuditPostRequest } from '@/pages/purchase/detail/types/audit.post.request';
import { CheckPostCancelRequest } from '@/pages/stocks/check/detail/types/check.detail.request';
import {
  StockCheckStatusEnum,
  StockCheckStatusNameOptions,
} from '@/pages/stocks/check/list/types/StockCheckStatusEnum';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { CommonModelForm } from '@/types/CommonModelForm';
import {
  PageContainer,
  ProCard,
  ProDescriptions,
  ProDescriptionsActionType,
} from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { GetProps, Space, Tag } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { isEmpty } from 'lodash';
import { useContext, useRef, useState } from 'react';
import { useSearchParams } from 'umi';
import { StockCheckModeOptions } from '../list/types/StockCheckModeEnum';
import { CheckPostEntity } from '../list/types/check.post.entity';
import {
  approveCheckPost,
  cancelCheckPost,
  queryCheckByIdOrNo,
  queryCheckPostDetail,
  rejectCheckPost,
} from '../services';
import { CheckDetailPostListTableColumns } from './config/checkDetailPostListTableColumns';
import { CheckDetailPostEntity } from './types/check.detail.post.entity';

export default () => {
  const intl = useIntl();
  const [form] = useForm();
  const actionRef = useRef<ProDescriptionsActionType>();
  let [searchParams, setSearchParams] = useSearchParams();
  let [recordData, setRecordData] = useState<CheckPostEntity>({});
  const { closeTab } = useContext(KeepAliveTabContext);

  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  const [checkModalProps, setCheckModalProps] = useState<CommonModelForm<string, CheckPostEntity>>({
    visible: false,
    recordId: '',
    readOnly: false,
    title: '',
  });

  const handleCheckCancel = async (values: CheckPostCancelRequest) => {
    const data = await cancelCheckPost(values);
    if (data) {
      await hideConfirmModal();
      actionRef?.current?.reload();
    }
  };

  const [confirmModalProps, setConfirmModalProps] = useState<GetProps<typeof ConfirmModal>>({
    open: false,
  });

  const hideConfirmModal = async () => {
    setConfirmModalProps((preProps) => ({
      ...preProps,
      open: false,
    }));
  };

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setCheckModalProps({ visible: false, recordId: '', readOnly: false, title: '' });
  };

  const hideAuditModal = () => {
    setAuditModalProps({ visible: false, onClose: () => { } });
  };

  const [auditModalProps, setAuditModalProps] = useState<AuditModalProps>({
    visible: false,
    onClose: hideAuditModal,
  });

  const handleAudit = async (values: AuditPostRequest) => {
    if (AuditStatus.APPROVE == values.result) {
      //审核通过
      const data = await approveCheckPost({ id: recordData.id, bizBillNo: recordData.bizBillNo });
      if (data) {
        hideAuditModal();
        actionRef.current?.reload(true);
      }
    } else if (AuditStatus.REJECT == values.result) {
      //拒绝
      const data = await rejectCheckPost({
        rejectRemark: values.auditRemark,
        id: recordData.id,
        bizBillNo: recordData.bizBillNo,
      });
      if (data) {
        hideAuditModal();
        actionRef.current?.reload(true);
      }
    }
  };
  const handleContinueCheck = () => {
    closeTab();
    history.push(
      '/stocks/check/operation?checkId=' + recordData.id + '&bizBillNo=' + recordData.bizBillNo,
    );
  };

  return (
    <PageContainer>
      <ProCard>
        <ProDescriptions
          actionRef={actionRef}
          title={
            <Space>
              <span>{recordData?.bizBillNo}</span>
              <Tag color={StockCheckStatusNameOptions[recordData?.state!]?.status}>
                {StockCheckStatusNameOptions[recordData?.state!]?.text}
              </Tag>
            </Space>
          }
          extra={
            <Space>
              {(StockCheckStatusEnum.INVENTORY_IN_PROGRESS == recordData?.state ||
                StockCheckStatusEnum.REJECTED == recordData?.state) && (
                  <>
                    <AuthButton
                      danger
                      authority="deleteWarehouseCheck"
                      onClick={() =>
                        setConfirmModalProps({
                          open: true,
                          tips: intl.formatMessage({ id: 'stocks.check.detail.confirm.void' }),
                          onOk: () =>
                            handleCheckCancel({
                              id: recordData?.id,
                              bizBillNo: recordData?.bizBillNo,
                            }),
                        })
                      }
                    >
                      {intl.formatMessage({ id: 'stocks.check.detail.button.void' })}
                    </AuthButton>
                    <AuthButton
                      danger
                      authority="warehouseContinueCheck"
                      onClick={handleContinueCheck}
                    >
                      {intl.formatMessage({ id: 'stocks.check.detail.button.continueCheck' })}
                    </AuthButton>
                  </>
                )}
              {StockCheckStatusEnum.PENDING == recordData?.state && (
                <AuthButton
                  authority="warehouseCheckAudit"
                  type="primary"
                  onClick={() => {
                    setAuditModalProps({
                      visible: true,
                    });
                  }}
                >
                  {intl.formatMessage({ id: 'stocks.check.detail.button.audit' })}
                </AuthButton>
              )}

              <AuthButton
                authority="warehouseCheckPrint"
                danger
                onClick={() => {
                  window.open(
                    `/print?printType=${PrintType.checkOrder}&checkId=${searchParams.get(
                      'checkId',
                    )}`,
                  );
                }}
              >
                {intl.formatMessage({ id: 'common.button.print' })}
              </AuthButton>
            </Space>
          }
          params={{
            id: searchParams.get('checkId'),
          }}
          request={async (params) => {
            if (!isEmpty(params?.id)) {
              const data = await queryCheckByIdOrNo({ ...params });
              if (data) {
                setRecordData(data);
              }
              return { data, success: true };
            }
            return Promise.resolve({
              success: false,
              data: {},
            });
          }}
          column={4}
        >
          <ProDescriptions.Item label={intl.formatMessage({ id: 'stocks.check.list.label.warehouseName' })} dataIndex="warehouseName" />
          <ProDescriptions.Item
            label={intl.formatMessage({ id: 'stocks.check.detail.label.checkMode' })}
            dataIndex="mode"
            valueEnum={StockCheckModeOptions}
          />

          <ProDescriptions.Item label={intl.formatMessage({ id: 'stocks.check.list.label.createDocPerson' })} dataIndex="createDocPerson" />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'stocks.check.list.label.createDocTime' })} dataIndex="createTime" />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'stocks.check.detail.label.remarks' })} dataIndex="remarks" span={4} ellipsis />
          {recordData?.profitNum && (
            <ProDescriptions.Item span={4} className="bg-[#FFF4F4] pt-4 pl-4 text-[14px]">
              <Space size={40}>
                <span>
                  {intl.formatMessage({ id: 'stocks.check.list.label.profitNum' })}：<text className="text-[#F83431]">{recordData?.profitNum}</text>
                </span>
                <span>
                  {intl.formatMessage({ id: 'stocks.check.list.label.profitAmount' })}：<text className="text-[#F83431]">{recordData?.profitAmount}</text>
                </span>
                <span>
                  {intl.formatMessage({ id: 'stocks.check.list.label.lossNum' })}：<text className="text-[#33CC47]">{recordData?.lossNum}</text>
                </span>
                <span>
                  {intl.formatMessage({ id: 'stocks.check.list.label.lossAmount' })}：<text className="text-[#33CC47]">{recordData?.lossAmount}</text>
                </span>
                {StockCheckStatusEnum.REJECTED == recordData?.state && (
                  <>
                    <span>{intl.formatMessage({ id: 'stocks.check.detail.label.auditPerson' })}：{recordData?.auditPerson}</span>
                    <span>{intl.formatMessage({ id: 'stocks.check.detail.label.auditTime' })}：{recordData?.auditTime}</span>
                    <span>{intl.formatMessage({ id: 'stocks.check.detail.label.rejectReason' })}：{recordData?.rejectRemark}</span>
                  </>
                )}
              </Space>
            </ProDescriptions.Item>
          )}
        </ProDescriptions>
      </ProCard>
      <FunProTable<CheckDetailPostEntity, any>
        className="mt-4"
        headerTitle={<SubTitle text={intl.formatMessage({ id: 'stocks.check.detail.title.checkGoods' })} />}
        columns={CheckDetailPostListTableColumns({ isDetail: true, isEdit: false })}
        rowKey="id"
        search={false}
        params={{
          checkId: searchParams.get('checkId') ?? '',
        }}
        requestPage={async (params) => {
          if (!isEmpty(params?.checkId)) {
            return await queryCheckPostDetail({ ...params });
          }
          return { data: [], total: 0 };
        }}
        scroll={{ x: 'max-content' }}
        revalidateOnFocus={false}
      />
      <ConfirmModal {...confirmModalProps} onCancel={hideConfirmModal} />

      <AuditModal {...auditModalProps} onOk={handleAudit} onClose={hideAuditModal} />
    </PageContainer>
  );
};
