export interface CheckDetailPostEntity {
  /**
   * 品牌Id
   */
  brandId?: string;
  /**
   * 品牌名称
   */
  brandName?: string;
  /**
   * 品牌件号
   */
  brandPartNo?: string;
  /**
   * 类目Id
   */
  categoryId?: string;
  /**
   * 类目名称
   */
  categoryName?: string;
  /**
   * 盘点库存
   */
  checkAmount?: number;
  /**
   * 盘点主单ID
   */
  checkId?: string;
  /**
   * 货位编码
   */
  code?: string;
  /**
   * 成本价
   */
  costPrice?: number;
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 差异库存(盘点库存-当前库存)
   */
  diffAmount?: number;
  /**
   * 主键
   */
  id?: string;
  /**
   * 账面库存
   */
  inventoryNum?: number;
  /**
   * None
   */
  isDelete?: number;
  /**
   * 商品ID
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * oe码
   */
  oeNo?: string;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 当前库存(提交损益的库存快照)
   */
  stockAmount?: number;
  locationCode?: string;
  /**
   * 单位id
   */
  unitId?: string;
  /**
   * 单位名称
   */
  unitName?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
}
