import { PaginationRequest } from '@/types/PaginationRequest';

export interface CheckDetailRequest extends PaginationRequest {
  /**
   * 盘点主单ID
   */
  checkId?: string;
}

/**
 * 详情页面主盘点信息请求对象
 */
export interface StockCheckSimpleRequest {
  /**
   * 盘点主单ID
   */
  id?: string;
  /**
   * 盘点仓库仓库id
   */
  warehouseId?: string;
  /**
   * 盘点单号
   */
  bizBillNo?: string;
}

/**
 * 取消请求对象
 */
export interface CheckPostCancelRequest {
  /**
   * 盘点id
   */
  id?: string;

  /**
   * 盘点单号
   */
  bizBillNo?: string;
}
