import type { ProColumns } from '@ant-design/pro-components';
import type { CheckDetailPostEntity } from '../types/check.detail.post.entity';
import ColumnRender from "@/components/ColumnRender";
import { useIntl } from '@umijs/max';

export interface CheckDetailPostListTableColumnsProps {
  isEdit?: boolean;
  isDetail?: boolean;
  onShow?: boolean;
}

export const CheckDetailPostListTableColumns = (props: CheckDetailPostListTableColumnsProps) => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      editable: false,
      fixed: 'left',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.detail.label.itemSn' }),
      dataIndex: 'itemSn',
      key: 'itemSn',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.detail.label.itemName' }),
      dataIndex: 'itemName',
      key: 'itemName',
      search: false,
      editable: false,
      width: 120,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.detail.label.oeNo' }),
      dataIndex: 'oeNo',
      key: 'oeNo',
      search: false,
      editable: false,
      width: 140,
      renderText: (text: string) => ColumnRender.ArrayColumnRender(text?.split(',')),
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.detail.label.brandPartNo' }),
      dataIndex: 'brandPartNo',
      key: 'brandPartNo',
      search: false,
      editable: false,
      ellipsis: true,
      width: 100,
      renderText: (text: string) => ColumnRender.ArrayColumnRender(text?.split(',')),
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.detail.label.brandName' }),
      dataIndex: 'brandName',
      key: 'brandName',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.detail.label.categoryName' }),
      dataIndex: 'categoryName',
      key: 'categoryName',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.detail.label.unitName' }),
      dataIndex: 'unitName',
      key: 'unitName',
      search: false,
      editable: false,
      width: 50,
    },
    /* {
       title: '库位',
       dataIndex: 'code',
       key: 'code',
       search: false,
       editable: false,
       fixed: 'right',
       width: 100,
     },*/
    {
      title: intl.formatMessage({ id: 'stocks.check.detail.label.stockAmount' }),
      dataIndex: 'stockAmount',
      key: 'stockAmount',
      search: false,
      editable: false,
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.detail.label.checkAmount' }),
      dataIndex: 'checkAmount',
      key: 'checkAmount',
      search: false,
      editable: false,
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.detail.label.diffAmount' }),
      dataIndex: 'diffAmount',
      key: 'diffAmount',
      search: false,
      editable: false,
      width: 80,
    },
  ] as ProColumns<CheckDetailPostEntity>[];
}