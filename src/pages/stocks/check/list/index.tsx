import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ActionType } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { useRef } from 'react';
import { useActivate } from 'react-activation';
import { queryCheckPagePost } from '../services';
import { PostListTableColumns } from './config/postListTableColumns';
import type { CheckPostEntity } from './types/check.post.entity';

const CheckList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();

  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 新增或编辑
   * @param values
   */
  const handleSaveOrUpdate = async (values: CheckPostEntity) => {
    try {
      const { id } = values;
      if (id) {
        //编辑
      } else {
        //新增
      }
      actionRef.current?.reload(true);
      return true;
    } catch (error) {
      return false;
    }
  };

  return (
    <PageContainer>
      <FunProTable<CheckPostEntity, any>
        rowKey="id"
        requestPage={queryCheckPagePost}
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        columns={PostListTableColumns()}
        headerTitle={
          <AuthButton
            type="primary"
            key="primary"
            authority="addWarehouseCheck"
            onClick={() => history.push('/stocks/check/operation')}
          >
            {intl.formatMessage({ id: 'stocks.check.list.button.addCheck' })}
          </AuthButton>
        }
      />
    </PageContainer>
  );
};
export default withKeepAlive(CheckList);
