export enum StockCheckStatusEnum {
  CANCEL = 0,
  INVENTORY_IN_PROGRESS = 1,
  PENDING = 2,
  REJECTED = 3,
  COMPLETED = 4,
}

export enum StockCheckStatusName {
  CANCEL = '已关闭',
  INVENTORY_IN_PROGRESS = '盘点中',
  PENDING = '待审核',
  REJECTED = '已驳回',
  COMPLETED = '已完成',
}

export const StockCheckStatusNameOptions = {
  [StockCheckStatusEnum.CANCEL]: {text: StockCheckStatusName.CANCEL, status: 'default'},
  [StockCheckStatusEnum.INVENTORY_IN_PROGRESS]: {text: StockCheckStatusName.INVENTORY_IN_PROGRESS, status: 'processing'},
  [StockCheckStatusEnum.PENDING]: {text: StockCheckStatusName.PENDING, status: 'processing'},
  [StockCheckStatusEnum.REJECTED]: {text: StockCheckStatusName.REJECTED, status: 'error'},
  [StockCheckStatusEnum.COMPLETED]: {text: StockCheckStatusName.COMPLETED, status: 'success'},
};
