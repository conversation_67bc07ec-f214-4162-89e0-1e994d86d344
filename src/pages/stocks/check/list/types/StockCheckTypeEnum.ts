export enum StockCheckTypeEnum {
  PART = 1,
  ALL = 2,
  ALL_IN_STOCK = 3
}

export enum StockCheckTypeEnumName {
  PART = '抽盘',
  ALL = '全盘',
  ALL_IN_STOCK = '有货全盘'
}

export const StockCheckTypeNameOptions = {
  [StockCheckTypeEnum.PART]: {text: StockCheckTypeEnumName.PART, status: ''},
  [StockCheckTypeEnum.ALL]: {text: StockCheckTypeEnumName.ALL, status: ''},
  [StockCheckTypeEnum.ALL_IN_STOCK]: {text: StockCheckTypeEnumName.ALL_IN_STOCK, status: ''},
};
