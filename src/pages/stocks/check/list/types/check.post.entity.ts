import { StockCheckModeEnum } from './StockCheckModeEnum';
import { StockCheckStatusEnum } from './StockCheckStatusEnum';
import { StockCheckTypeEnum } from './StockCheckTypeEnum';

export interface CheckPostEntity {
  /**
   * 审核人
   */
  auditPerson?: string;
  /**
   * 审核时间
   */
  auditTime?: string;
  /**
   * 盘点单号
   */
  bizBillNo?: string;
  /**
   * 制单人
   */
  createDocPerson?: string;
  /**
   * 制单人工号
   */
  createDocPersonNo?: string;
  /**
   * 创建人（供大数据使用）
   */
  createPerson?: string;
  /**
   * 创建时间（供大数据使用）
   */
  createTime?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 盘亏金额
   */
  lossCostPrice?: number;
  /**
   * 盘亏数量
   */
  lossNum?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * 盘点方式1:明盘2:盲盘
   */
  mode?: StockCheckModeEnum;
  /**
   * 盘盈金额
   */
  profitCostPrice?: number;
  /**
   * 盘盈数量
   */
  profitNum?: number;
  /**
   * 驳回原因
   */
  rejectRemark?: string;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 盘点状态0:已关闭(取消),1:盘点中2:待审核3:已驳回4：已完成
   */
  state?: StockCheckStatusEnum;
  /**
   * 盘点状态描述
   */
  stateDesc?: string;
  /**
   * 盘点类型1:全盘2:抽盘
   */
  type?: StockCheckTypeEnum;
  /**
   * 仓库id
   */
  warehouseId?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
}
