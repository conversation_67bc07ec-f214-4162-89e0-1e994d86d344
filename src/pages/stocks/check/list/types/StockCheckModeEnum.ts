export enum StockCheckModeEnum {
  MING_PAN = 1,
  AN_PAN = 2,
}

export enum StockCheckModeEnumName {
  MING_PAN = '明盘',
  AN_PAN = '盲盘',
}

export const StockCheckModeNameOptions = [
  {
    label: StockCheckModeEnumName.MING_PAN,
    value: StockCheckModeEnum.MING_PAN,
    tooltip: '盘点时可以实时查看库存数和盘点差异',
  },
  {
    label: StockCheckModeEnumName.AN_PAN,
    value: StockCheckModeEnum.AN_PAN,
    tooltip: '盘点时看不到库存数，提交盘点结果后才能看到盘点差异',
  },
];

export const StockCheckModeOptions = {
  [StockCheckModeEnum.MING_PAN]: { text: StockCheckModeEnumName.MING_PAN, status: 'default' },
  [StockCheckModeEnum.AN_PAN]: { text: StockCheckModeEnumName.AN_PAN, status: 'processing' },
};
