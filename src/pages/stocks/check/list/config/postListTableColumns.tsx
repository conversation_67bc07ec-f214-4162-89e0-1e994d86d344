import AuthButton from '@/components/common/AuthButton';
import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { accountListQuerySimple } from '@/pages/system/user/services';
import type { ProColumns } from '@ant-design/pro-components';
import { Link, history, useIntl } from '@umijs/max';
import { Space } from 'antd';
import { StockCheckStatusEnum, StockCheckStatusNameOptions } from '../types/StockCheckStatusEnum';
import type { CheckPostEntity } from '../types/check.post.entity';

export interface PostListTableColumnsProps { }

export const PostListTableColumns = () => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.list.label.bizBillNo' }),
      dataIndex: 'bizBillNo',
      key: 'bizBillNo',
      search: true,
      order: 6,
      width: 160,
      render: (_, record) => (
        <Link to={{ pathname: '/stocks/check/detail', search: '?checkId=' + record.id }}>{_}</Link>
      ),
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.list.label.createTime' }),
      dataIndex: 'createTime',
      key: 'createTime',
      search: false,
      width: 140,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.list.label.warehouseName' }),
      dataIndex: 'warehouseName',
      key: 'warehouseName',
      order: 5,
      search: {
        transform: (value: string) => {
          return { warehouseIdList: [value] };
        },
      },
      width: 120,
      fieldProps: {
        fieldNames: { label: 'warehouseName', value: 'id' },
      },
      request: async (query: any) => {
        const s = await warehouseList({ state: YesNoStatus.YES });
        return s.warehouseSimpleRoList;
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.list.label.state' }),
      dataIndex: 'state',
      key: 'state',
      search: {
        transform: (value: string) => {
          return { stateList: [value] };
        },
      },
      width: 100,
      valueEnum: StockCheckStatusNameOptions,
      order: 4,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.list.label.goodsInfo' }),
      dataIndex: 'keyword',
      hideInTable: true,
      search: true,
      fieldProps: {
        placeholder: intl.formatMessage({ id: 'stocks.check.placeholder.goodsNameOrCode' }),
      },
      order: 3,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.list.label.createDocPerson' }),
      dataIndex: 'createDocPersonNo',
      key: 'createDocPersonNo',
      width: 60,
      order: 2,
      hideInTable: true,
      valueType: 'select',
      search: {
        transform: (value: string) => {
          return { createDocPersonNo: value };
        },
      },
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.list.label.createDocTime' }),
      dataIndex: 'createTimeAt',
      valueType: 'dateRange',
      order: 1,
      hideInTable: true,
      search: {
        transform: (value: any) => {
          return {
            startCreateTime: value[0],
            endCreateTime: value[1],
          };
        },
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.list.label.profitNum' }),
      dataIndex: 'profitNum',
      key: 'profitNum',
      search: false,
      width: 80,
      renderText: (text) => (text ? text : ''),
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.list.label.lossNum' }),
      dataIndex: 'lossNum',
      key: 'lossNum',
      search: false,
      width: 80,
      renderText: (text) => (text ? text : ''),
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.list.label.profitAmount' }),
      dataIndex: 'profitCostPrice',
      key: 'profitCostPrice',
      search: false,
      width: 80,
      renderText: (text) => (text ? text : ''),
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.list.label.lossAmount' }),
      dataIndex: 'lossCostPrice',
      key: 'lossCostPrice',
      search: false,
      width: 80,
      renderText: (text) => (text ? text : ''),
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.list.label.createDocPerson' }),
      dataIndex: 'createDocPerson',
      key: 'createDocPerson',
      search: false,
      width: 60,
    },
    {
      title: intl.formatMessage({ id: 'common.label.remark' }),
      dataIndex: 'remarks',
      key: 'remarks',
      search: false,
      ellipsis: true,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 80,
      fixed: 'right',
      render: (text, record) => (
        <Space>
          {(StockCheckStatusEnum.INVENTORY_IN_PROGRESS == record.state ||
            StockCheckStatusEnum.REJECTED == record.state) && (
              <AuthButton
                authority="continueWarehouseCheck"
                isHref
                onClick={() => {
                  history.push(
                    '/stocks/check/operation?checkId=' + record.id + '&bizBillNo=' + record.bizBillNo,
                  );
                }}
              >
                {intl.formatMessage({ id: 'stocks.check.list.button.continueCheck' })}
              </AuthButton>
            )}
        </Space>
      ),
    },
  ] as ProColumns<CheckPostEntity>[];
}