import AuthButton from '@/components/common/AuthButton';
import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Popconfirm, Space } from 'antd';
import type { CheckDetailPostEntity } from '../../detail/types/check.detail.post.entity';
import ColumnRender from "@/components/ColumnRender";

export interface CheckListTableColumnsProps {
  handleDeleteItem: (id: string) => void;
}

export const CheckListTableColumns = (props: CheckListTableColumnsProps): ProColumns<CheckDetailPostEntity>[] => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      dataIndex: 'xh',
      width: 40,
      fixed: 'left',
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.columns.itemCode' }),
      dataIndex: 'itemSn',
      key: 'itemSn',
      readonly: true,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.columns.itemName' }),
      dataIndex: 'itemName',
      key: 'itemName',
      readonly: true,
      width: 120,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.columns.oeNo' }),
      dataIndex: 'oeNo',
      key: 'oeNo',
      readonly: true,
      width: 140,
      renderText: (text: string) => ColumnRender.ArrayColumnRender(text?.split(',')),
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.columns.brandPartNo' }),
      dataIndex: 'brandPartNo',
      key: 'brandPartNo',
      readonly: true,
      width: 100,
      renderText: (text: string) => ColumnRender.ArrayColumnRender(text?.split(',')),
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.columns.brand' }),
      dataIndex: 'brandName',
      key: 'brandName',
      readonly: true,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.columns.category' }),
      dataIndex: 'categoryName',
      key: 'categoryName',
      readonly: true,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.columns.unit' }),
      dataIndex: 'unitName',
      key: 'unitName',
      readonly: true,
      width: 50,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.columns.inventoryQuantity' }),
      dataIndex: 'inventoryNum',
      key: 'inventoryNum',
      readonly: true,
      width: 80,
      fixed: 'right',
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.columns.checkInventory' }),
      dataIndex: 'checkAmount',
      key: 'checkAmount',
      valueType: 'digit',
      readonly: false,
      width: 120,
      fixed: 'right',
      fieldProps: (_, config) => {
        return {
          min: 0,
        };
      },
      formItemProps: (_, { entity }) => {
        return {
          rules: [
            {
              validator: async (_, value) => {
                if (value == null) {
                  return Promise.reject(new Error(intl.formatMessage({ id: 'stocks.check.operation.validation.checkInventoryRequired' })));
                } else {
                  return Promise.resolve();
                }
              },
            },
          ],
        };
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.columns.differenceQuantity' }),
      dataIndex: 'diffAmount',
      key: 'diffAmount',
      readonly: true,
      width: 80,
      fixed: 'right',
      editable: false,
      renderText(text, record, index, action) {
        if (record?.diffAmount! > 0) {
          return <span className="text-[#F83431]">+{text}</span>;
        } else if (record?.diffAmount! < 0) {
          return <span className="text-[#33CC47]">{text}</span>;
        } else {
          return text;
        }
      },
    },
    {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 80,
      fixed: 'right',
      editable: false,
      render: (text, record) => (
        <Space>
          <Popconfirm
            title={intl.formatMessage({ id: 'stocks.check.operation.confirm.delete' })}
            onConfirm={() => props.handleDeleteItem(record.id ?? '')}
          >
            <AuthButton isHref authority="checkItemRemove">
              {intl.formatMessage({ id: 'stocks.check.operation.button.delete' })}
            </AuthButton>
          </Popconfirm>
        </Space>
      ),
    },
  ] as ProColumns<CheckDetailPostEntity>[];
};
