import ColumnRender from "@/components/ColumnRender";
import { queryGoodsPropertyPage } from '@/pages/goods/property/services';
import { queryLocationByWhId } from '@/pages/stocks/location/services';
import { transformCategoryTree } from '@/utils/transformCategoryTree';
import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Space } from 'antd';
import type { CheckGoodsEntity } from '../types/check.goods.entity';

export interface PostListTableColumnsProps {
  warehouseId?: string;
}

export const PostListTableColumns = (props: PostListTableColumnsProps): ProColumns<CheckGoodsEntity>[] => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.goodsModal.columns.itemName' }),
      dataIndex: 'itemName',
      search: false,
      width: 120,
    },
    {
      dataIndex: 'keyword',
      search: true,
      hideInTable: true,
      fieldProps: {
        placeholder: intl.formatMessage({ id: 'stocks.check.operation.goodsModal.placeholder.goodsSearch' }),
      },
      formItemProps: {
        tooltip: intl.formatMessage({ id: 'stocks.check.operation.goodsModal.tooltip.goodsSearch' }),
        label: intl.formatMessage({ id: 'stocks.check.operation.goodsModal.label.goodsInfo' }),
        labelCol: { span: 6 },
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.goodsModal.columns.itemCode' }),
      dataIndex: 'itemSn',
      search: false,
      hideInSearch: true,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.goodsModal.columns.oeNo' }),
      dataIndex: 'oeNo',
      search: false,
      width: 140,
      renderText: (text: string) => ColumnRender.ArrayColumnRender(text?.split(',')),
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.goodsModal.columns.brandPartNo' }),
      dataIndex: 'brandPartNo',
      search: false,
      width: 100,
      renderText: (text: string) => ColumnRender.ArrayColumnRender(text?.split(',')),
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.goodsModal.columns.brand' }),
      dataIndex: 'brandIdList',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        mode: 'multiple',
        showSearch: true,
        maxTagCount: 3,
        optionRender: (option: any) => (
          <Space>
            {option.data.label}
          </Space>
        ),
      },
      request: async () => {
        const { data } = await queryGoodsPropertyPage({ pageNo: 1, pageSize: 1000 }, 'brand');
        return data.map((t) => ({
          label: t.brandName,
          dataType: t.dataType,
          value: t.brandId,
        }));
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.goodsModal.columns.brand' }),
      dataIndex: 'brandName',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.goodsModal.columns.category' }),
      dataIndex: 'categoryName',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.goodsModal.columns.category' }),
      dataIndex: 'categoryIdList',
      hideInTable: true,
      valueType: 'treeSelect',
      fieldProps: {
        treeCheckable: true,
        maxTagCount: 3,
        filterTreeNode: (text: string, treeNode: any) => treeNode.text?.includes(text),
      },
      request: () => {
        return queryGoodsPropertyPage(
          { categoryStatus: 1, pageSize: 999, pageNo: 1, isReturnTree: true },
          'category',
        ).then((result) => transformCategoryTree(result.data));
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.goodsModal.columns.unit' }),
      dataIndex: 'unitName',
      width: 50,
      search: false,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.goodsModal.columns.location' }),
      dataIndex: 'code',
      width: 100,
      search: false,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'stocks.check.operation.goodsModal.columns.location' }),
      dataIndex: 'locationIdList',
      width: 100,
      params: { warehouseIdList: [props.warehouseId] },
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
        fieldNames: { label: 'code', value: 'id' },
      },

      request: async (params) => {
        if (props.warehouseId != '0') {
          const data = await queryLocationByWhId({ ...params });
          return data?.warehouseLocationRoList;
        } else {
          return [];
        }
      },
      hideInTable: true,
    },
  ] as ProColumns<CheckGoodsEntity>[];
};
