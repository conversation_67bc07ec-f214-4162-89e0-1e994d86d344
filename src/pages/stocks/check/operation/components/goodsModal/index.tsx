import FunProTable from '@/components/common/FunProTable';
import SubTitle from '@/components/common/SubTitle';
import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import type { CommonModelForm } from '@/types/CommonModelForm';
import type { ProFormInstance } from '@ant-design/pro-components';
import { DrawerForm } from '@ant-design/pro-components';
import { useIntl, useModel } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Checkbox, Flex } from 'antd';
import { isEmpty } from 'lodash';
import { useRef, useState } from 'react';
import { queryCheckGoodsPost } from '../../../services';
import { PostListTableColumns } from './config/GoodsTableColumns';
import type { CheckGoodsEntity } from './types/check.goods.entity';

export default (props: CommonModelForm<string, any>) => {
  const intl = useIntl();
  const { initialState, setInitialState } = useModel('@@initialState');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [warehouseId, setWarehouseId] = useState('');
  const formRef = useRef<ProFormInstance>();
  useAsyncEffect(async () => {
    if (props?.recordId) {
      setWarehouseId(props?.recordId);
    }
  }, [props.visible]);
  const [onlyHaveInv, setOnlyHaveInv] = useState<YesNoStatus>(YesNoStatus.NO); //有库存
  return (
    <DrawerForm
      onFinish={async (value) => {
        props.onOk!({ itemId: selectedRowKeys });
        setSelectedRowKeys([]); //清空
      }}
      drawerProps={{
        onClose: props.onCancel,
        styles: { body: { backgroundColor: '#F2F2F2' } },
        destroyOnClose: true,
        maskClosable: false,
      }}
      layout="horizontal"
      title={props.title}
      open={props.visible}
      formRef={formRef}
      width={1200}
    >
      <FunProTable<CheckGoodsEntity, any>
        rowKey="itemId"
        scroll={{ x: 1000 }}
        title={() => (
          <Flex justify="space-between">
            <SubTitle text={intl.formatMessage({ id: 'stocks.check.operation.goodsModal.title.goodsList' })} />
            <Checkbox
              className="mb-0"
              onChange={(e) => {
                if (e.target?.checked) {
                  setOnlyHaveInv(YesNoStatus.YES);
                } else {
                  setOnlyHaveInv(YesNoStatus.NO);
                }
              }}
            >
              {intl.formatMessage({ id: 'stocks.check.operation.goodsModal.checkbox.onlyWithStock' })}
            </Checkbox>
          </Flex>
        )}
        params={{ onlyHaveInv, warehouseId }}
        requestPage={async (params) => {
          if (!isEmpty(params.warehouseId)) {
            return await queryCheckGoodsPost(params);
          }
          return { data: [] };
        }}
        search={{ labelWidth: 100, defaultCollapsed: false }}
        rowSelection={{
          selectedRowKeys: selectedRowKeys,
          onChange: (selectedKeys) => {
            setSelectedRowKeys(selectedKeys);
          },
        }}
        columns={PostListTableColumns({ warehouseId })}
      />
    </DrawerForm>
  );
};
