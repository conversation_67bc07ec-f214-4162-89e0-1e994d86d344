import AuthButton from '@/components/common/AuthButton';
import SubTitle from '@/components/common/SubTitle';
import { KeepAliveTabContext } from '@/layouts/context';
import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import {
  StockCheckModeEnum,
  StockCheckModeNameOptions,
} from '@/pages/stocks/check/list/types/StockCheckModeEnum';
import { CommonModelForm } from '@/types/CommonModelForm';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { QuestionCircleOutlined } from '@ant-design/icons';
import {
  ActionType,
  EditableFormInstance,
  EditableProTable,
  PageContainer,
  ProCard,
  ProForm,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { history, useIntl, useSearchParams } from '@umijs/max';
import { useAsyncEffect, useDebounceFn } from 'ahooks';
import { Checkbox, Flex, Input, Space, Tooltip } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { isEmpty, uniqueId } from 'lodash';
import React, { useContext, useRef, useState } from 'react';
import { warehouseList } from '../../warehouse/services';
import { CheckDetailPostEntity } from '../detail/types/check.detail.post.entity';
import { StockCheckStatusNameOptions } from '../list/types/StockCheckStatusEnum';
import { StockCheckTypeEnum, StockCheckTypeNameOptions } from '../list/types/StockCheckTypeEnum';
import { CheckPostEntity } from '../list/types/check.post.entity';
import {
  checkCreateOrUpdatePost,
  confirmCheckPost,
  modifyCheckDeletePost,
  queryCheckByIdOrNo,
  queryCheckPostDetail,
} from '../services';
import GoodsModal from './components/goodsModal';
import { CheckListTableColumns } from './config/checkListTableColumns';
const CheckOperationList = () => {
  const intl = useIntl();
  const [form] = useForm();
  const actionRef = useRef<ActionType>();
  const { Search } = Input;
  const [searchParams, setSearchParams] = useSearchParams();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);
  const checkId = searchParams.get('checkId') ?? '';
  const bizBillNo = searchParams.get('bizBillNo') ?? '';
  const formRef = useRef<ProFormInstance>();
  const formRefOne = useRef<ProFormInstance>();
  const formRefTwo = useRef<EditableFormInstance>();

  const [detailData, setDetailData] = useState<CheckPostEntity>({});
  const [onlyUnInventoried, setOnlyUnInventoried] = useState<YesNoStatus>(YesNoStatus.NO); //未盘
  const [onlyDiff, setOnlyDiff] = useState<YesNoStatus>(YesNoStatus.NO); //差异
  const [keyword, setKeyword] = useState('');
  const [tabKey, setTabKey] = useState('tabKey_001');

  const { closeTab } = useContext(KeepAliveTabContext);
  /**
   * 选择key
   */
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const warehouseId = ProForm.useWatch('warehouseId', formRef.current ?? {});
  const mode = ProForm.useWatch('mode', formRef.current ?? {});

  const [goodsModalProps, setGoodsModalProps] = useState<CommonModelForm<string, CheckPostEntity>>({
    visible: false,
    recordId: '0',
    readOnly: false,
    title: '',
  });

  useAsyncEffect(async () => {
    if (!isEmpty(checkId)) {
      const data = await queryCheckByIdOrNo({ bizBillNo, id: checkId });
      if (data) {
        setDetailData(data);
        formRef?.current?.setFieldsValue(data);
        formRefOne?.current?.setFieldsValue(data);
      }
    } else {
      setDetailData({});
      formRef?.current?.resetFields();
      formRefOne?.current?.resetFields();
    }
  }, [checkId]);

  /**
   * 全盘 ，有货全盘  1:全盘2:抽盘
   */
  const handleCheckOpt = (type: StockCheckTypeEnum) => {
    formRef.current?.validateFields().then(async (result) => {
      if (type == StockCheckTypeEnum.PART) {
        //抽盘
        setGoodsModalProps({ visible: true, recordId: result?.warehouseId });
      } else {
        console.log(result);
        const data = await checkCreateOrUpdatePost({ ...result, type });
        if (data) {
          //成功跳编辑
          history.push(
            '/stocks/check/operation?checkId=' + data.id + '&bizBillNo=' + data.bizBillNo,
          );
        }
      }
    });
  };

  const hideModal = () => {
    setGoodsModalProps({ visible: false, recordId: '0', readOnly: false, title: '' });
  };
  const handleCheckFormDataChange = async (changedValues: any, allValues: any) => {
    const rowData = formRefTwo.current?.getRowData?.(changedValues.id);
    if (rowData?.checkAmount == undefined) {
      rowData.checkAmount = 0;
    }
    if (rowData && rowData?.checkAmount != undefined) {
      const data = await checkCreateOrUpdatePost({
        id: checkId,
        warehouseId,
        stockCheckDetailCmdList: [changedValues],
      });
      if (data) {
        actionRef?.current?.reload();
      }
    } else {
      formRefTwo.current?.setRowData?.(changedValues.id, { checkAmount: 0 });
    }
    return;
  };
  const { run } = useDebounceFn(
    (changedValues: any, allValues: any) => handleCheckFormDataChange(changedValues, allValues),
    {
      wait: 500,
    },
  );

  const handleOnSelectGoods = async (values: any) => {
    if (!isEmpty(values?.itemId) && warehouseId && mode) {
      const data = await checkCreateOrUpdatePost({
        id: isEmpty(checkId) ? undefined : checkId,
        warehouseId,
        mode,
        type: StockCheckTypeEnum.PART,
        stockCheckDetailCmdList: values?.itemId.map((s: string) => ({
          itemId: s,
          checkId: isEmpty(checkId) ? undefined : checkId,
        })),
      });
      if (data) {
        if (isEmpty(checkId)) {
          //成功跳编辑
          hideModal();
          history.push(
            '/stocks/check/operation?checkId=' + data.id + '&bizBillNo=' + data.bizBillNo,
          );
        } else {
          actionRef?.current?.reload();
          hideModal();
        }
      }
    }
  };

  const handleCancel = () => {
    closeTab();
    history.push('/stocks/check');
  };
  const handleSubmit = async () => {
    if (!isEmpty(checkId)) {
      //不为空
      const remarks = formRefOne?.current?.getFieldValue('remarks');
      const data = await confirmCheckPost({ id: checkId, bizBillNo, remarks });
      if (data) {
        closeTab();
        history.push('/stocks/check/detail?checkId=' + checkId);
      }
    }
  };

  /**
   * 删除判断事件
   * @param ids
   */
  const handleDeleteItem = async (id: string) => {
    await modifyCheckDeletePost({ checkId, idList: [id] });
    actionRef.current?.reload(true);
    setTabKey(uniqueId('pd_'));
  };
  return (
    <PageContainer>
      <ProCard wrap bodyStyle={{ paddingBottom: 0 }}>
        <ProForm
          labelAlign={'left'}
          layout="horizontal"
          submitter={false}
          formRef={formRef}
          initialValues={{ mode: StockCheckModeEnum.MING_PAN }}
        >
          <ProFormSelect
            name="warehouseId"
            label={intl.formatMessage({ id: 'stocks.check.operation.label.checkWarehouse' })}
            rules={[
              {
                required: true,
                message: intl.formatMessage({ id: 'stocks.check.operation.placeholder.selectCheckWarehouse' }),
              },
            ]}
            disabled={!isEmpty(checkId)}
            fieldProps={{ fieldNames: { label: 'warehouseName', value: 'id' } }}
            request={() =>
              warehouseList({ state: YesNoStatus.YES }).then((s) => {
                const list = s?.warehouseSimpleRoList ?? [];
                if (list.length) {
                  formRef.current?.setFieldValue('warehouseId', list[0].id);
                }
                return list;
              })
            }
            width={'md'}
          />
          <ProFormRadio.Group
            name="mode"
            rules={[
              {
                required: true,
                message: intl.formatMessage({ id: 'stocks.check.operation.placeholder.selectCheckMode' }),
              },
            ]}
            disabled={!isEmpty(checkId)}
            label={intl.formatMessage({ id: 'stocks.check.operation.label.checkMode' })}
            options={StockCheckModeNameOptions.map((option) => ({
              label: (
                <Tooltip title={option.tooltip}>
                  <span className="pr-1">{option.label}</span> <QuestionCircleOutlined />
                </Tooltip>
              ),
              value: option.value,
            }))}
          />
        </ProForm>
      </ProCard>
      <ProCard
        title={
          <Space>
            <SubTitle text={intl.formatMessage({ id: 'stocks.check.operation.subtitle.checkGoods' })} />
            {detailData?.bizBillNo && (
              <span className="font-normal text-base text-[14px] text-black/[0.8] pl-4">
                {intl.formatMessage({ id: 'stocks.check.operation.label.checkOrderNo' })}：{detailData?.bizBillNo}
              </span>
            )}
            {StockCheckStatusNameOptions[detailData?.state!]?.text && (
              <span className="font-normal text-base text-[14px] text-black/[0.8]">
                {intl.formatMessage({ id: 'stocks.check.operation.label.documentStatus' })}：{StockCheckStatusNameOptions[detailData?.state!]?.text}
              </span>
            )}
            {StockCheckTypeNameOptions[detailData?.type!]?.text && (
              <span className="font-normal text-base text-[14px] text-black/[0.8]">
                {intl.formatMessage({ id: 'stocks.check.operation.label.checkType' })}：{StockCheckTypeNameOptions[detailData?.type!]?.text}
              </span>
            )}
          </Space>
        }
        className="mt-4"
        bodyStyle={{ padding: 0 }}
      >
        <EditableProTable<CheckDetailPostEntity>
          rowKey="id"
          search={false}
          columns={CheckListTableColumns({ handleDeleteItem })}
          actionRef={actionRef}
          key={tabKey}
          editableFormRef={formRefTwo}
          scroll={{ x: 'max-content', y: 'max-content' }}
          pagination={{
            showQuickJumper: true,
            defaultPageSize: 10,
            showSizeChanger: false,
          }}
          title={() => (
            <Flex justify="space-between">
              <Space>
                <AuthButton
                  type="primary"
                  key="addPart"
                  authority="addCheckPart"
                  onClick={() => handleCheckOpt(StockCheckTypeEnum.PART)}
                >
                  {intl.formatMessage({ id: 'stocks.check.operation.button.addGoods' })}
                </AuthButton>
                <AuthButton
                  danger
                  key="AddAll"
                  authority="addCheckAll"
                  disabled={!isEmpty(checkId)}
                  onClick={() => handleCheckOpt(StockCheckTypeEnum.ALL)}
                >
                  {intl.formatMessage({ id: 'stocks.check.operation.button.fullCheck' })}
                </AuthButton>
                <AuthButton
                  danger
                  key="addStock"
                  authority="addCheckAllInStock"
                  disabled={!isEmpty(checkId)}
                  onClick={() => handleCheckOpt(StockCheckTypeEnum.ALL_IN_STOCK)}
                >
                  {intl.formatMessage({ id: 'stocks.check.operation.button.fullCheckWithStock' })}
                </AuthButton>
              </Space>
              <Space>
                <Checkbox
                  className="mb-0"
                  onChange={(e) => {
                    if (e.target?.checked) {
                      setOnlyUnInventoried(YesNoStatus.YES);
                    } else {
                      setOnlyUnInventoried(YesNoStatus.NO);
                    }
                  }}
                >
                  {intl.formatMessage({ id: 'stocks.check.operation.checkbox.onlyUnchecked' })}
                </Checkbox>
                <Checkbox
                  className="mb-0"
                  onChange={(e) => {
                    if (e.target?.checked) {
                      setOnlyDiff(YesNoStatus.YES);
                    } else {
                      setOnlyDiff(YesNoStatus.NO);
                    }
                  }}
                >
                  {intl.formatMessage({ id: 'stocks.check.operation.checkbox.onlyDifference' })}
                </Checkbox>
                <Search
                  placeholder={intl.formatMessage({ id: 'stocks.check.operation.placeholder.goodsCodeOrName' })}
                  allowClear
                  onSearch={setKeyword}
                  style={{ width: 237 }}
                />
              </Space>
            </Flex>
          )}
          recordCreatorProps={false}
          params={{
            onlyUnInventoried,
            onlyDiff,
            keyword,
            checkId,
            warehouseId,
            mode,
            isEdit: true,
          }}
          request={async (params) => {
            if (!isEmpty(checkId) && !isEmpty(warehouseId) && mode) {
              const result = await queryCheckPostDetail(params);
              const { data } = result;
              setEditableRowKeys(data?.map((item) => item.id!));
              return result;
            }
            return { data: [], success: true };
          }}
          editable={{
            type: 'multiple',
            editableKeys,
            onChange: setEditableRowKeys,
            actionRender: (row, config, defaultDoms) => {
              return [];
            },
            onValuesChange: run,
          }}
        />
      </ProCard>
      <ProCard className="mt-4">
        <ProForm labelAlign={'left'} layout="horizontal" submitter={false} formRef={formRefOne}>
          <ProFormTextArea
            name="remarks"
            label={intl.formatMessage({ id: 'stocks.check.operation.label.checkRemarks' })}
            placeholder={intl.formatMessage({ id: 'stocks.check.operation.placeholder.checkRemarks' })}
            fieldProps={{ count: { max: 100, show: true }, maxLength: 100 }}
            rules={[{ max: 100 }]}
          />
        </ProForm>
      </ProCard>
      <ProCard className="mt-[1px]">
        <Flex justify="flex-end" align="center">
          <Space>
            <AuthButton authority="checkCancel" key="cancel" onClick={handleCancel}>
              {intl.formatMessage({ id: 'stocks.check.operation.button.cancel' })}
            </AuthButton>
            <AuthButton
              authority="checkSubmit"
              key="checkSubmit"
              type="primary"
              onClick={handleSubmit}
            >
              {intl.formatMessage({ id: 'stocks.check.operation.button.submitCheck' })}
            </AuthButton>
          </Space>
        </Flex>
      </ProCard>
      <GoodsModal {...goodsModalProps} onCancel={hideModal} onOk={handleOnSelectGoods} />
    </PageContainer>
  );
};

export default withKeepAlive(CheckOperationList);
