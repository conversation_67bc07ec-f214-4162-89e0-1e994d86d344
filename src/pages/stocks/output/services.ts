import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { OutPutDetailPostEntity } from './detail/types/output.detail.post.entity';
import { OutPutDetailRequest } from './detail/types/output.detail.request';
import { OutputPostConfirmRequest } from './list/types/output.post.confirm.request';
import { OutPutPostEntity } from './list/types/output.post.entity';
import { OutputPostWithdrawRequest } from './list/types/output.post.withdraw.request';
import { OutputStockBatchList } from './list/types/output.stock.batch.list';
import { QueryPostListRequest } from './list/types/query.post.list.request';

/**
 * 列表查询
 * @param params
 * @returns
 */
export const queryOutPutPagePost = async (
  params: Partial<QueryPostListRequest> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<OutPutPostEntity>>(`/ipmswarehouse/stockout/queryByPage`, {
    data: params,
  });
};

/**
 * 详情查询
 * @param params
 * @returns
 */
export const queryOutPutDetailPost = async (params: Partial<OutPutDetailRequest>) => {
  return request<OutPutDetailPostEntity>(`/ipmswarehouse/stockout/queryDetail`, {
    data: params,
  });
};
/**
 * 出库确认
 * @param params
 * @returns
 */
export const confirmOutPutPost = async (params: Partial<OutputPostConfirmRequest>) => {
  return request<boolean>(`/ipmswarehouse/stockout/confirm`, {
    data: params,
  });
};
/**
 * 出库记录
 * @param params
 * @returns
 */
export const queryBatchDetailPost = async (params: Partial<OutPutDetailRequest>) => {
  return request<OutputStockBatchList>(
    `/ipmswarehouse/stockout/queryBatchDetail
  `,
    {
      data: params,
    },
  );
};
/**
 * 作废
 * @param params
 * @returns
 */
export const withdrawOutputPost = async (params: Partial<OutputPostWithdrawRequest>) => {
  return request<boolean>(`/ipmswarehouse/stockout/withdraw`, {
    origin: true,
    data: params,
  });
};
