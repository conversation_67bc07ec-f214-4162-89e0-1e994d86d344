import AuthButton from '@/components/common/AuthButton';
import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import type { ProColumns } from '@ant-design/pro-components';
import { Link, useIntl } from '@umijs/max';
import { Space } from 'antd';
import { distributionModeStatusOptions } from '../types/DistributionModeStatus';
import { OutPutBillTypeStatusOptions } from '../types/OutPutBillTypeStatus';
import { OutPutStatus, outPutStatusOptions } from '../types/OutPutStatus';
import type { OutPutPostEntity } from '../types/output.post.entity';

export interface PostListTableColumnsProps {
  handleOutPut: (orderNo: string, warehouseId: string) => void;
  handleDetail: (orderId: string) => void;
}

export const PostListTableColumns = (props: PostListTableColumnsProps): ProColumns<OutPutPostEntity>[] => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      fixed: 'left',
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.businessOrderNo' }),
      dataIndex: 'origBillNo',
      key: 'origBillNo',
      search: true,
      width: 160,
      order: 8,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.outputOrderNo' }),
      dataIndex: 'bizBillNo',
      key: 'bizBillNo',
      search: true,
      order: 7,
      width: 150,
      render: (_, record) => (
        <Link
          to={{
            pathname: '/stocks/output/detail',
            search: '?outputId=' + record.id + '&outputWarehouseId=' + record.warehouseId,
          }}
        >
          {_}
        </Link>
      ),
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.goodsInfo' }),
      dataIndex: 'keyword',
      hideInTable: true,
      search: true,
      fieldProps: {
        placeholder: intl.formatMessage({ id: 'stocks.output.list.placeholder.goodsNameOrCode' }),
      },
      order: 6,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.outputType' }),
      dataIndex: 'billType',
      key: 'billType',
      search: {
        transform: (value: string) => {
          return { billTypeList: [value] };
        },
      },
      order: 4,
      width: 120,
      valueEnum: OutPutBillTypeStatusOptions,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.outputStatus' }),
      dataIndex: 'state',
      key: 'state',
      search: {
        transform: (value: string) => {
          return { stateList: [value] };
        },
      },
      order: 3,
      width: 80,
      valueEnum: outPutStatusOptions,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.notifyOutputTime' }),
      dataIndex: 'createTime',
      key: 'createTime',
      search: false,
      width: 140,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.notifyOutputTime' }),
      dataIndex: 'createTimeAt',
      valueType: 'dateRange',
      order: 2,
      hideInTable: true,
      search: {
        transform: (value: any) => {
          return {
            startCreateTime: value[0],
            endCreateTime: value[1],
          };
        },
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.outputCompleteTime' }),
      dataIndex: 'realOutTime',
      key: 'realOutTime',
      search: false,
      width: 140,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.outputCompleteTime' }),
      dataIndex: 'endRealOutTimeAt',
      valueType: 'dateRange',
      hideInTable: true,
      order: 1,
      search: {
        labelWidth: 'auto',
        transform: (value: any) => {
          return {
            startRealOutTime: value[0],
            endRealOutTime: value[1],
          };
        },
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.outputWarehouse' }),
      dataIndex: 'warehouseName',
      key: 'warehouseName',
      order: 5,
      search: {
        transform: (value: string) => {
          return { warehouseIdList: [value] };
        },
      },
      width: 140,
      fieldProps: {
        fieldNames: { label: 'warehouseName', value: 'id' },
      },
      request: (query: any) => {
        return warehouseList({}).then((s) => {
          return s.warehouseSimpleRoList;
        });
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.outputQuantity' }),
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      search: false,
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.recipient' }),
      dataIndex: 'customer',
      key: 'customer',
      search: false,
      ellipsis: true,
      width: 120,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.deliveryMethod' }),
      dataIndex: 'distributionMode',
      key: 'distributionMode',
      search: false,
      width: 80,
      valueEnum: distributionModeStatusOptions,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.deliveryAddress' }),
      dataIndex: 'deliveryAddress',
      key: 'deliveryAddress',
      ellipsis: true,
      search: false,
      width: 120,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.logisticsCompany' }),
      dataIndex: 'logisticsCompanyName',
      key: 'logisticsCompanyName',
      ellipsis: true,
      search: false,
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.list.label.logisticsNo' }),
      dataIndex: 'logisticsNo',
      key: 'logisticsNo',
      ellipsis: true,
      search: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 100,
      fixed: 'right',
      render: (text, record) => (
        <Space>
          {(OutPutStatus.AUDITING == record.state || OutPutStatus.TO_ARRIVAL == record.state) && (
            <AuthButton
              isHref
              authority="outWarehouse"
              onClick={() => props.handleOutPut(record.id ?? '', record.warehouseId ?? '')}
            >
              {intl.formatMessage({ id: 'stocks.output.list.button.output' })}
            </AuthButton>
          )}
          {YesNoStatus.YES == record.existBatchRecord && (
            <AuthButton
              isHref
              authority="outWarehouseLog"
              onClick={() => props.handleDetail(record.id ?? '')}
            >
              {intl.formatMessage({ id: 'stocks.output.list.button.outputRecord' })}
            </AuthButton>
          )}
        </Space>
      ),
    },
  ] as ProColumns<OutPutPostEntity>[];
};
