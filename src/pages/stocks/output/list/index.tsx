import FunProTable from '@/components/common/FunProTable';
import type { CommonModelForm } from '@/types/CommonModelForm';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ActionType } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import { confirmOutPutPost, queryOutPutPagePost } from '../services';
import OutputDetailModal from './components/OutputDetailModal';
import OutputModal from './components/OutputModal';
import { PostListTableColumns } from './config/postListTableColumns';
import { OutPutPostEntity } from './types/output.post.entity';

const OutputList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();

  const [outputModalProps, setOutputModalProps] = useState<
    CommonModelForm<string, OutPutPostEntity>
  >({
    visible: false,
    recordId: '',
    readOnly: false,
    title: '',
  });

  const [outputDetailModalProps, setOutputDetailModalProps] = useState<
    CommonModelForm<string, OutPutPostEntity>
  >({
    visible: false,
    recordId: '',
    readOnly: false,
    title: '',
  });

  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 出库页面
   * @param ids
   */
  const handleOutPut = async (id: string, warehouseId: string) => {
    setOutputModalProps({ visible: true, recordId: id, title: intl.formatMessage({ id: 'stocks.output.modal.title.output' }) });
  };
  /**
   * 出库记录
   * @param id
   */
  const handleDetail = async (id: string) => {
    setOutputDetailModalProps({ visible: true, recordId: id, title: intl.formatMessage({ id: 'stocks.output.modal.title.outputRecord' }) });
  };

  const hanleConfirm = async (values: any) => {
    const data = await confirmOutPutPost(values);
    if (data) {
      hideModal();
      actionRef?.current?.reload(true);
    }
  };

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setOutputModalProps({ visible: false, recordId: '', readOnly: false, title: '' });
    actionRef?.current?.reload();
  };
  const hideDetailModal = () => {
    setOutputDetailModalProps({ visible: false, recordId: '', readOnly: false, title: '' });
    actionRef?.current?.reload();
  };

  return (
    <PageContainer>
      <FunProTable<OutPutPostEntity, any>
        rowKey="id"
        requestPage={queryOutPutPagePost}
        scroll={{ x: 1300 }}
        actionRef={actionRef}
        search={{ labelWidth: 100, defaultCollapsed: false }}
        columns={PostListTableColumns({ handleOutPut, handleDetail })}
      />
      <OutputModal {...outputModalProps} onOk={hanleConfirm} onCancel={hideModal} />
      <OutputDetailModal {...outputDetailModalProps} onCancel={hideDetailModal} />
    </PageContainer>
  );
};
export default withKeepAlive(OutputList);
