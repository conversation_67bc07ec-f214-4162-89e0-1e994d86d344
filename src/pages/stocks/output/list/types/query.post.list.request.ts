import { PaginationRequest } from '@/types/PaginationRequest';

export interface QueryPostListRequest extends PaginationRequest {
  /**
   * 业务类型（参考：com.ipms.core.message.objects.stocks.enums.BillTypeEnum）
   */
  billTypeList?: number[];
  /**
   * 出库单号
   */
  bizBillNo?: string;
  /**
   * 开始结束时间
   */
  endCreateTime?: string;
  /**
   * 出库完成结束时间
   */
  endRealOutTime?: string;
  /**
   * 支持输入商品编码和商品名称，模糊匹配查询
   */
  keyword?: string;
  /**
   * 业务单号
   */
  origBillNo?: string;
  /**
   * 开始创建时间
   */
  startCreateTime?: string;
  /**
   * 出库完成开始时间
   */
  startRealOutTime?: string;
  /**
   * 出库单状态：0取消1.待出库2.已出库3.部分出库
   */
  stateList?: number[];
  /**
   * 仓库id
   */
  warehouseIdList?: string[];
}
