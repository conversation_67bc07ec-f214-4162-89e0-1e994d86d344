import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';

export interface OutPutPostEntity {
  /**
   * 业务类型
   */
  billType?: number;
  /**
   * 业务类型描述
   */
  billTypeDesc?: string;
  /**
   * 出库单号
   */
  bizBillNo?: string;
  /**
   * 创建人（供大数据使用）
   */
  createPerson?: string;
  /**
   * 创建时间（供大数据使用）
   */
  createTime?: string;
  /**
   * 收货方
   */
  customer?: string;
  /**
   * 配送地址
   */
  deliveryAddress?: string;
  /**
   * 配送方式（1：客户自提、2：商家配送、3：快递物流）
   */
  distributionMode?: number;
  /**
   * 主键
   */
  id?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * 物流公司id
   */
  logisticsCompanyCode?: string;
  /**
   * 物流公司名称
   */
  logisticsCompanyName?: string;
  /**
   * 物流单号
   */
  logisticsNo?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * 源(父)单号
   */
  origBillNo?: string;
  /**
   * 实际出库人
   */
  realOutPerson?: string;
  /**
   * 实际出库时间
   */
  realOutTime?: string;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 出库单状态：0取消1.待出库2.已出库3.部分出库
   */
  state?: number;
  /**
   * 出库单状态描述：0取消1.待出库2.已出库3.部分出库
   */
  stateDesc?: string;
  /**
   * 计划出库商品总数
   */
  totalAmount?: number;
  /**
   * 计划出库商品种类总和
   */
  totalItem?: number;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
  /**
   * 版本号
   */
  version?: number;
  /**
   * 出库仓库id
   */
  warehouseId?: string;
  /**
   * 入库仓库id
   */
  warehouseIdIn?: string;
  /**
   * 入库仓库名称
   */
  warehouseInName?: string;
  /**
   * 出库仓库名称
   */
  warehouseName?: string;

  existBatchRecord?: YesNoStatus;
}
