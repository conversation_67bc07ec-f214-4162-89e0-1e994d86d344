export enum OutPutBillTypeStatus {
  PURCHASE_OUT = 200,
  TRANSFER_OUT = 201,
  SALE_GOODS_OUT = 202,
  OUTSIDE_RETURN_OUT = 203,
}

export enum OutPutBillTypeStatusName {
  PURCHASE_OUT = '采购退货出库',
  TRANSFER_OUT = '调拨出库',
  SALE_GOODS_OUT = '销售出库',
  OUTSIDE_RETURN_OUT = '盘亏出库',
}
export const OutPutBillTypeStatusOptions = {
  [OutPutBillTypeStatus.PURCHASE_OUT]: {
    text: OutPutBillTypeStatusName.PURCHASE_OUT,
    status: 'Success',
  },
  [OutPutBillTypeStatus.TRANSFER_OUT]: {
    text: OutPutBillTypeStatusName.TRANSFER_OUT,
    status: 'Success',
  },
  [OutPutBillTypeStatus.SALE_GOODS_OUT]: {
    text: OutPutBillTypeStatusName.SALE_GOODS_OUT,
    status: 'Success',
  },
  [OutPutBillTypeStatus.OUTSIDE_RETURN_OUT]: {
    text: OutPutBillTypeStatusName.OUTSIDE_RETURN_OUT,
    status: 'Success',
  },
};
