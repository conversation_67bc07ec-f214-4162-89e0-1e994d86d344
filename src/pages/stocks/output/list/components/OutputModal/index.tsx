import SubTitle from '@/components/common/SubTitle';
import type { CommonModelForm } from '@/types/CommonModelForm';
import type { EditableFormInstance } from '@ant-design/pro-components';
import {
  DrawerForm,
  EditableProTable,
  ProCard,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { ConfigProvider, Row, Space, message } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { add, toString } from 'lodash';
import { useRef, useState } from 'react';
import { OutPutPostListTableColumns } from '../../../detail/config/outputPostListTableColumns';
import type {
  OutPutDetailPostEntity,
  StockOutDetailRoList,
} from '../../../detail/types/output.detail.post.entity';
import { queryOutPutDetailPost } from '../../../services';
import { distributionModeStatusOptions } from '../../types/DistributionModeStatus';

export default (props: CommonModelForm<string, StockOutDetailRoList>) => {
  const intl = useIntl();
  const [recordData, setRecordData] = useState<OutPutDetailPostEntity>({});
  // 可编辑行
  const [editorRows, setEditorRows] = useState<string[]>([]);
  const [form] = useForm();
  const formRef = useRef<EditableFormInstance>();
  useAsyncEffect(async () => {
    if (props.recordId) {
      const data = await queryOutPutDetailPost({ stockOutId: props.recordId });
      if (data) {
        setRecordData(data);
        form.setFieldsValue(data?.stockOutRo);
        form.setFieldsValue(data);
        if (data?.stockOutDetailRoList) {
          setEditorRows(data?.stockOutDetailRoList.map((s) => s.id ?? ''));
        }
      }
    }
  }, [props.visible]);

  const onFinish = async () => {
    form
      ?.validateFields()
      .then((values) => {
        const sum = values?.stockOutDetailRoList
          ?.map((s: StockOutDetailRoList) => s.remainAmount as number)
          ?.reduce(
            (accumulator: number, currentValue: number) =>
              add(Number(accumulator), Number(currentValue)),
            0,
          );
        if (sum === 0) {
          message.error(intl.formatMessage({ id: 'stocks.output.modal.message.atLeastOneProduct' }));
          return;
        }
        return props.onOk!({ ...values, stockOutDetailCmdList: values.stockOutDetailRoList });
      })
      .catch((info) => {
        message.error(info.errorFields[0]?.errors[0]);
        return false;
      });
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#F49C1F',
        },
        components: {
          InputNumber: {
            controlWidth: 160,
          },
        },
      }}
    >
      <DrawerForm
        title={props.title}
        width={1080}
        layout="horizontal"
        form={form}
        drawerProps={{
          classNames: {
            body: 'bg-[#f2f2f2]',
          },
          destroyOnClose: true,
          onClose: props.onCancel,
          maskClosable: false,
        }}
        submitter={{
          searchConfig: {
            submitText: intl.formatMessage({ id: 'stocks.output.modal.button.confirmOutput' }),
          },
        }}
        open={props.visible}
        validateTrigger={'onchang'}
        onFinish={onFinish}
      >
        <ProFormText name="id" hidden />
        <ProFormText name="origBillNo" hidden />
        <ProCard className="mb-4" bodyStyle={{ paddingTop: 0, paddingBottom: 16 }}>
          <EditableProTable
            headerTitle={<SubTitle text={intl.formatMessage({ id: 'stocks.output.modal.subtitle.goodsDetail' })} />}
            columns={OutPutPostListTableColumns({ isDetail: true, isEdit: true })}
            rowKey="id"
            search={false}
            options={false}
            name="stockOutDetailRoList"
            recordCreatorProps={false}
            scroll={{ x: 1000 }}
            editable={{
              type: 'multiple',
              editableKeys: editorRows,
              actionRender: (row, config, defaultDom) => [],
            }}
          />
        </ProCard>
        {recordData?.stockOutRo?.distributionMode && (
          <ProCard title={<SubTitle text={intl.formatMessage({ id: 'stocks.output.modal.subtitle.deliveryInfo' })} />}>
            <Row style={{ paddingBottom: 16 }}>
              {intl.formatMessage({ id: 'stocks.output.modal.label.deliveryAddress' })}：{recordData?.stockOutRo.deliveryAddress}
            </Row>
            <Row>
              <Space>
                <ProFormSelect
                  label={intl.formatMessage({ id: 'stocks.output.modal.label.deliveryMethod' })}
                  name="distributionMode"
                  convertValue={(s) => toString(s)}
                  width="sm"
                  valueEnum={distributionModeStatusOptions}
                />
                <ProFormText name="logisticsCompanyName" placeholder={intl.formatMessage({ id: 'stocks.output.modal.placeholder.logisticsCompany' })} />
                <ProFormText name="logisticsNo" placeholder={intl.formatMessage({ id: 'stocks.output.modal.placeholder.logisticsNo' })} />
              </Space>
            </Row>
          </ProCard>
        )}
      </DrawerForm>
    </ConfigProvider>
  );
};
