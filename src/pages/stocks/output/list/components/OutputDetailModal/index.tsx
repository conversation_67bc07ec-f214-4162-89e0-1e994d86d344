import ConfirmModal from '@/components/ConfirmModal';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import type { CommonModelForm } from '@/types/CommonModelForm';
import { DrawerForm, ProCard, ProFormText } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import type { GetProps } from 'antd';
import { Button } from 'antd';
import { useState } from 'react';
import { OutPutPostListTableColumns } from '../../../detail/config/outputPostListTableColumns';
import type { StockOutDetailRoList } from '../../../detail/types/output.detail.post.entity';
import { queryBatchDetailPost, withdrawOutputPost } from '../../../services';
import { distributionModeStatusOptions } from '../../types/DistributionModeStatus';
import type { OutputPostWithdrawRequest } from '../../types/output.post.withdraw.request';
import type { OutputStockBatchList, StockOutBatchRoList } from '../../types/output.stock.batch.list';

export default (props: CommonModelForm<string, StockOutDetailRoList>) => {
  const intl = useIntl();
  const [recordData, setRecordData] = useState<OutputStockBatchList>({});
  useAsyncEffect(async () => {
    if (props.recordId) {
      loadData();
    }
  }, [props.visible]);

  const loadData = async () => {
    const data = await queryBatchDetailPost({ stockOutId: props.recordId });
    setRecordData(data);
  };

  const handleWithdrawOutput = async (values: OutputPostWithdrawRequest) => {
    const data = await withdrawOutputPost(values);
    if (data) {
      hideConfirmModal();
      loadData();
    }
  };

  const [confirmModalProps, setConfirmModalProps] = useState<GetProps<typeof ConfirmModal>>({
    open: false,
  });

  const hideConfirmModal = async () => {
    setConfirmModalProps((preProps) => ({
      ...preProps,
      open: false,
    }));
  };

  return (
    <DrawerForm
      title={props.title}
      width={1080}
      layout="horizontal"
      drawerProps={{
        classNames: {
          body: 'bg-[#f2f2f2]',
        },
        onClose: props.onCancel,
        maskClosable: false,
      }}
      submitter={false}
      open={props.visible}
      validateTrigger={'onchang'}
    >
      <ProFormText name="id" hidden />
      <ProFormText name="origBillNo" hidden />
      {recordData?.stockOutBatchRoList?.map((s) => (
        <ProCard
          className="mb-4"
          bodyStyle={{ padding: 0 }}
          title={
            <div>
              <div className="text-[14px] font-normal text-black/[0.85]">
                <span>{intl.formatMessage({ id: 'stocks.output.modal.label.outputTime' })}：{s.stockOutTime}</span>
                <span className="pl-[40px]">{intl.formatMessage({ id: 'stocks.output.modal.label.operator' })}：{s.stockOutPerson}</span>
                {s.cancelTime && (
                  <>
                    <span className="pl-[80px]">{intl.formatMessage({ id: 'stocks.output.modal.label.voidTime' })}：{s.cancelTime}</span>
                    <span className="pl-[40px]">{intl.formatMessage({ id: 'stocks.output.modal.label.operator' })}：{s.cancelPerson}</span>
                  </>
                )}
              </div>
              <div className="text-[14px] font-normal text-black/[0.6] flex">
                {s.distributionMode && (
                  <div>
                    {intl.formatMessage({ id: 'stocks.output.modal.label.deliveryMethod' })}：{distributionModeStatusOptions[s.distributionMode]?.text}{' '}
                    {s.logisticsCompanyName && `|${s.logisticsCompanyName}`}{' '}
                    {s.logisticsNo && `|${s.logisticsNo}`}
                  </div>
                )}
                {s.deliveryAddress && (
                  <div className="pl-[40px]">{intl.formatMessage({ id: 'stocks.output.modal.label.deliveryAddress' })}：{s.deliveryAddress}</div>
                )}
              </div>
            </div>
          }
          extra={
            <>
              {s.state == 0 && <Button>{intl.formatMessage({ id: 'stocks.output.modal.button.voided' })}</Button>}
              {s.state == 1 && (
                <AuthButton
                  danger
                  authority="outWarehouseDelete"
                  onClick={() =>
                    setConfirmModalProps({
                      open: true,
                      tips: intl.formatMessage({ id: 'stocks.output.modal.confirm.void' }),
                      onOk: () =>
                        handleWithdrawOutput({
                          id: s.id,
                          stockOutId: s.stockOutId,
                          origBillNo: s.origBillNo,
                        }),
                    })
                  }
                >
                  {intl.formatMessage({ id: 'stocks.output.modal.button.void' })}
                </AuthButton>
              )}
            </>
          }
        >
          <FunProTable<StockOutBatchRoList, any>
            rowKey="id"
            scroll={{ x: 1300 }}
            search={false}
            options={false}
            pagination={false}
            dataSource={s?.stockOutBatchDetailRoList}
            columns={OutPutPostListTableColumns({ onShow: true })}
          />
        </ProCard>
      ))}
      <ConfirmModal {...confirmModalProps} onCancel={hideConfirmModal} />
    </DrawerForm>
  );
};
