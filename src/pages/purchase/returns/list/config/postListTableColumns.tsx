import AuthButton from '@/components/common/AuthButton';
import { SupplierIdColumn, TimeAt } from '@/pages/common/config/tableColumns';
import { payStatusOptions } from '@/pages/purchase/list/types/PayStatus';
import { payTypeStatusOptions } from '@/pages/purchase/list/types/PayTypeStatus';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { accountListQuerySimple, queryStoreByAccount } from '@/pages/system/user/services';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { Link } from '@umijs/max';
import { Popconfirm, Space } from 'antd';
import type { MutableRefObject } from 'react';
import { RetrunOrderStatus, retrunOrderStatusOptions } from '../types/RetrunOrderStatus';
import type { PostEntity } from '../types/post.entity';

export interface PostListTableColumnsProps {
  intl: any;
  handleAgainItem: (id: string) => void;
  handleCancelItem: (id: string, orderNo: string) => void;
  handleRestItem: (id: string, orderNo: string) => void;
  formRef: MutableRefObject<ProFormInstance | undefined>;
}

export const PostListTableColumns = (props: PostListTableColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      fixed: 'left',
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.list.columns.returnOrderNo' }),
      dataIndex: 'orderNo',
      key: 'orderNo',
      search: true,
      width: 170,
      order: 8,
      render: (_, record) => (
        <Link to={{ pathname: '/purchase/returns/detail', search: '?returnId=' + record.id }}>
          {_}
        </Link>
      ),
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.list.columns.supplierName' }),
      dataIndex: 'supplierName',
      key: 'supplierName',
      search: false,
      ellipsis: true,
      width: 180,
    },
    { ...SupplierIdColumn, order: 4 },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.list.columns.returnTime' }),
      dataIndex: 'returnTime',
      key: 'returnTime',
      search: false,
      width: 140,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.list.columns.orderStatus' }),
      dataIndex: 'orderStatus',
      key: 'orderStatus',
      search: true,
      order: 7,
      width: 100,
      valueEnum: retrunOrderStatusOptions,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.list.columns.returnStore' }),
      dataIndex: 'storeName',
      key: 'storeName',
      search: false,
      width: 100,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.list.columns.returnStore' }),
      dataIndex: 'storeIdList',
      key: 'storeIdList',
      width: 120,
      ellipsis: true,
      hideInTable: true,
      order: 6,
      search: {
        transform: (value: any) => {
          return {
            storeIdList: [value],
          };
        },
      },
      fieldProps: {
        fieldNames: { label: 'name', value: 'id' },
        onChange: () => {
          props.formRef?.current?.setFieldValue('outWarehouseName', '');
        },
      },
      request: async () => await queryStoreByAccount(),
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.list.columns.outWarehouse' }),
      dataIndex: 'outWarehouseName',
      key: 'outWarehouseName',
      width: 120,
      order: 5,
      search: {
        transform: (value: any) => {
          return {
            outWarehouseId: value,
          };
        },
      },
      ellipsis: true,
      fieldProps: {
        fieldNames: { label: 'warehouseName', value: 'id' },
      },
      dependencies: ['storeIdList'],
      request: (params) => {
        if (params?.storeIdList.length > 0) {
          return warehouseList({ ...params }).then((s) => {
            return s.warehouseSimpleRoList;
          });
        }
      },
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.list.columns.returnAmount' }),
      dataIndex: 'sumAmount',
      key: 'sumAmount',
      search: false,
      width: 100,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.list.columns.payType' }),
      dataIndex: 'payType',
      key: 'payType',
      search: false,
      width: 60,
      valueEnum: payTypeStatusOptions,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.list.columns.payStatus' }),
      dataIndex: 'payStatus',
      key: 'payStatus',
      search: false,
      width: 80,
      valueEnum: payStatusOptions,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.list.columns.completeTime' }),
      dataIndex: 'completeTime',
      key: 'completeTime',
      search: false,
      width: 140,
    },
    {
      dataIndex: 'itemSn',
      search: true,
      hideInTable: true,
      fieldProps: {
        placeholder: props.intl.formatMessage({ id: 'purchase.returns.list.search.productInfo.placeholder' }),
      },
      formItemProps: {
        tooltip: props.intl.formatMessage({ id: 'purchase.returns.list.search.productInfo.tooltip' }),
        label: props.intl.formatMessage({ id: 'purchase.returns.list.search.productInfo' }),
        labelCol: { span: 6 },
      },
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.list.columns.creator' }),
      dataIndex: 'creator',
      key: 'creator',
      search: false,
      width: 60,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.list.columns.creator' }),
      dataIndex: 'createPerson',
      key: 'createPerson',
      search: true,
      width: 60,
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: (query: any) => {
        return accountListQuerySimple({ name: query.keyWords });
      },
    },
    TimeAt(props.intl.formatMessage({ id: 'purchase.returns.list.columns.returnTime' })),
    {
      title: props.intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 80,
      fixed: 'right',
      render: (text, record: PostEntity) => (
        <Space>
          {RetrunOrderStatus.DRAFT == record.orderStatus && (
            <AuthButton
              isHref
              authority="editPurchaseReturn"
              onClick={() => props.handleAgainItem(record.id)}
            >
              {props.intl.formatMessage({ id: 'purchase.returns.list.button.edit' })}
            </AuthButton>
          )}
          {(RetrunOrderStatus.DRAFT == record.orderStatus ||
            RetrunOrderStatus.TO_OUTBOUND == record.orderStatus) && (
              <Popconfirm
                title={props.intl.formatMessage({ id: 'purchase.returns.list.confirm.void' })}
                onConfirm={() => props.handleCancelItem(record.id, record.orderNo ?? '')}
              >
                <AuthButton isHref authority="deletePurchaseReturn">
                  {props.intl.formatMessage({ id: 'purchase.returns.list.button.void' })}
                </AuthButton>
              </Popconfirm>
            )}
          {RetrunOrderStatus.TO_OUTBOUND == record.orderStatus && (
            <Popconfirm
              title={props.intl.formatMessage({ id: 'purchase.returns.list.confirm.withdraw' })}
              onConfirm={() => props.handleRestItem(record.id, record.orderNo ?? '')}
            >
              <AuthButton isHref authority="withdrawPurchaseReturn">
                {props.intl.formatMessage({ id: 'purchase.returns.list.button.withdraw' })}
              </AuthButton>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ] as ProColumns<PostEntity>[];
