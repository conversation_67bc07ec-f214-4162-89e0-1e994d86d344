import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { PostEntity } from './types/post.entity';

/**
 * 列表查询
 * @param params
 * @returns
 */
export const queryReturnsPagePost = async (params: Partial<PostEntity> & PageRequestParamsType) => {
  return request<PageResponseDataType<PostEntity>>(
    `/ipmspurchase/refundsupply/PurchaseReturnOrderFacade/queryPage`,
    {
      data: params,
    },
  );
};
/**
 * 作废采购单
 * @param params
 * @returns
 */
export const cancelReturnOrderPost = async (params: { orderId?: string; orderNo?: string }) => {
  return request<boolean>(
    '/ipmspurchase/refundsupply/PurchaseReturnOrderFacade/cancelReturnOrder',
    {
      data: params,
    },
  );
};
/**
 * 撤回
 * @param params
 * @returns
 */
export const resetReturnOrderPost = async (params: { orderId?: string; orderNo?: string }) => {
  return request<boolean>('/ipmspurchase/refundsupply/PurchaseReturnOrderFacade/resetReturnOrder', {
    data: params,
  });
};
