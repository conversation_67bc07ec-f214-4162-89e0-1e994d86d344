import { PaginationRequest } from '@/types/PaginationRequest';

export interface QueryPostListRequest extends PaginationRequest {
  /**
   * 登录人账号
   */
  accountId?: string;
  /**
   * 开始时间
   */
  beginTime?: string;
  /**
   * 制单人
   */
  createPerson?: string;
  /**
   * 结束时间
   */
  endTime?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * 采购单号
   */
  orderNo?: string;
  /**
   * 采购状态
   */
  orderStatus?: string;
  /**
   * 收货仓id
   */
  receiveWarehouseId?: string;
  /**
   * 商品编码,模糊匹配名称编码OE品牌件号
   */
  skuCode?: string;
  /**
   * None
   */
  startRow?: number;
  /**
   * 发起门店id列表
   */
  storeIdList?: string[];
  /**
   * 供应商id
   */
  supplierId?: string;
}
