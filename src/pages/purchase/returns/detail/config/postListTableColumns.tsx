import type { ProColumns } from '@ant-design/pro-components';
import type { ReturnLineList } from '../types/line.post.entity';

export interface PostListTableColumnsProps {}

export const PostListTableColumns = () =>
  [
    {
      title: '序号',
      valueType: 'index',
      width: 40,
      fixed: 'left',
    },
    {
      title: '商品名称',
      dataIndex: 'skuName',
      key: 'skuName',
      search: false,
      width: 120,
    },
    {
      title: '商品编码',
      dataIndex: 'itemSn',
      key: 'itemSn',
      search: false,
      width: 100,
    },
    {
      title: 'OE',
      dataIndex: 'oe',
      key: 'oe',
      search: false,
      ellipsis: true,
      width: 140,
    },
    {
      title: '品牌件号',
      dataIndex: 'brandPartNo',
      key: 'brandPartNo',
      search: false,
      ellipsis: true,
      width: 100,
    },
    {
      title: '品牌',
      dataIndex: 'brandName',
      key: 'brandName',
      search: false,
      width: 100,
    },
    {
      title: '分类',
      dataIndex: 'categoryName',
      key: 'categoryName',
      search: false,
      ellipsis: true,
      width: 100,
    },
    {
      title: '库位',
      dataIndex: 'locationCode',
      key: 'locationCode',
      search: false,
      width: 100,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      search: false,
      width: 50,
    },
    {
      title: '采购单价',
      dataIndex: 'price',
      key: 'price',
      search: false,
      width: 80,
    },
    {
      title: '采购数量',
      dataIndex: 'num',
      key: 'num',
      search: false,
      width: 80,
    },
    {
      title: '小计',
      dataIndex: 'sumPrice',
      key: 'sumPrice',
      search: false,
      width: 80,
    },
  ] as ProColumns<ReturnLineList>[];
