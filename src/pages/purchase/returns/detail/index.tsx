import ConfirmModal from '@/components/ConfirmModal';
import PaymentExternalFormModal from '@/components/PaymentExternalFormModal';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import MoneyText from '@/components/common/MoneyText';
import SubTitle from '@/components/common/SubTitle';
import { KeepAliveTabContext } from '@/layouts/context';
import { PayStatus, payStatusOptions } from '@/pages/purchase/list/types/PayStatus';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import type { ProDescriptionsActionType } from '@ant-design/pro-components';
import { PageContainer, ProCard, ProDescriptions } from '@ant-design/pro-components';
import { history, useSearchParams } from '@umijs/max';
import type { GetProps } from 'antd';
import { Flex, Space, Spin, Tag } from 'antd';
import { useContext, useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import CapitalFlow from '../../capitalFlow';
import { payTypeStatusOptions } from '../../list/types/PayTypeStatus';
import OperationDetail from '../../operationDetail';
import { cancelReturnOrderPost, resetReturnOrderPost } from '../list/services';
import {
  RetrunOrderStatus,
  retrunOrderColorOptions,
  retrunOrderStatusOptions,
} from '../list/types/RetrunOrderStatus';
import { confirmPayReturnPost, confirmStockOutReturnPost } from '../operation/services';
import { queryReturnOrderFacadeById } from '../services';
import { PostListTableColumns } from './config/postListTableColumns';
import type { ReturnLineList } from './types/line.post.entity';
import type { ReutrnPostEntity } from './types/return.post.entity';

export default () => {
  const actionRef = useRef<ProDescriptionsActionType>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [recordData, setRecordData] = useState<ReutrnPostEntity>({});
  const [loading, setLoading] = useState<boolean>(false);
  const { closeTab } = useContext(KeepAliveTabContext);

  useActivate(() => {
    actionRef.current?.reload(true);
  });

  /**
   * 一键出库
   * @param params
   */
  const hanldConfirmStockOut = async () => {
    setLoading(true);
    const data = await confirmStockOutReturnPost({
      orderId: recordData?.returnOrder?.id,
      orderNo: recordData?.returnOrder?.orderNo,
    });
    if (data) {
      hideModal();
      setTimeout(() => {
        //延时刷新
        actionRef.current?.reload(true);
      }, 1500);
    }
    setLoading(false);
  };

  const [paymentVisible, setPaymentVisible] = useState(false);
  /**
   * 编辑
   */
  const hanldUpdateReturns = async (orderId: string | undefined) => {
    if (orderId) {
      //关闭当前页面
      closeTab();
      history.push(`/purchase/returns/operation?returnOrderId=${orderId}`);
    }
  };
  /**
   * 作废
   */
  const hanldCancelReturnOrder = async () => {
    const data = await cancelReturnOrderPost({
      orderId: recordData?.returnOrder?.id,
      orderNo: recordData?.returnOrder?.orderNo,
    });
    if (data) {
      hideModal();
      actionRef?.current?.reload(true);
    }
  };
  /**
   * 撤回
   */
  const handleDetailRestItem = async () => {
    const result = await resetReturnOrderPost({
      orderId: recordData?.returnOrder?.id,
      orderNo: recordData?.returnOrder?.orderNo,
    });
    if (result) {
      hideModal();
      actionRef?.current?.reload(true);
    }
  };

  const hideModal = async () => {
    setConfirmModalProps((preProps) => ({
      ...preProps,
      open: false,
    }));
  };

  /**
   * 确认结算
   */
  const hanldConfirmPay = async (values: any) => {
    const data = await confirmPayReturnPost({
      orderId: recordData?.returnOrder?.id,
      orderNo: recordData?.returnOrder?.orderNo,
      ...values,
    });
    if (data) {
      setPaymentVisible(false);
      setTimeout(() => {
        //延时刷新
        actionRef.current?.reload(true);
      }, 1500);
    }
  };

  const [confirmModalProps, setConfirmModalProps] = useState<GetProps<typeof ConfirmModal>>({
    open: false,
  });

  return (
    <PageContainer>
      <Spin spinning={loading}>
        <ProCard>
          <ProDescriptions
            actionRef={actionRef}
            title={
              <Flex vertical>
                <Space>
                  <span>{recordData?.returnOrder?.orderNo}</span>
                  <Tag
                    color={retrunOrderColorOptions[recordData?.returnOrder?.orderStatus!]?.status}
                  >
                    {retrunOrderStatusOptions[recordData?.returnOrder?.orderStatus!]?.text}
                  </Tag>
                  {![RetrunOrderStatus.DRAFT, RetrunOrderStatus.CLOSE].includes(
                    recordData?.returnOrder?.orderStatus!,
                  ) && (
                      <Tag color={payStatusOptions[recordData?.returnOrder?.payStatus!]?.status}>
                        {payStatusOptions[recordData?.returnOrder?.payStatus!]?.text}
                      </Tag>
                    )}
                </Space>
                {recordData?.returnOrder?.sourceNo && (
                  <span className="text-[14px] text-black/[0.8] font-normal pt-2">
                    一体系售后单号：{recordData?.returnOrder?.sourceNo}
                  </span>
                )}
              </Flex>
            }
            extra={
              <Space>
                {(RetrunOrderStatus.DRAFT == recordData?.returnOrder?.orderStatus ||
                  RetrunOrderStatus.TO_OUTBOUND == recordData?.returnOrder?.orderStatus) && (
                    <Space>
                      {RetrunOrderStatus.DRAFT == recordData?.returnOrder?.orderStatus && (
                        <AuthButton
                          authority="editPurchaseReturn"
                          danger
                          onClick={() => hanldUpdateReturns(recordData?.returnOrder?.id)}
                        >
                          编辑
                        </AuthButton>
                      )}
                      <AuthButton
                        authority="deletePurchaseReturn"
                        danger
                        onClick={() =>
                          setConfirmModalProps({
                            open: true,
                            tips: '是否确认作废？',
                            onOk: hanldCancelReturnOrder,
                          })
                        }
                      >
                        作废
                      </AuthButton>
                    </Space>
                  )}
                {RetrunOrderStatus.TO_OUTBOUND == recordData?.returnOrder?.orderStatus && (
                  <AuthButton
                    authority="withdrawPurchaseReturn"
                    danger
                    onClick={() =>
                      setConfirmModalProps({
                        open: true,
                        tips: '是否确认撤回？',
                        onOk: handleDetailRestItem,
                      })
                    }
                  >
                    撤回
                  </AuthButton>
                )}

                <AuthButton
                  authority="purchaseReturnPrint"
                  danger
                  onClick={() => {
                    window.open(
                      `/print?returnId=${searchParams.get('returnId')}&printType=${PrintType.purchaseReturnOrder
                      }`,
                    );
                  }}
                >
                  打印
                </AuthButton>
                {(RetrunOrderStatus.TO_OUTBOUND == recordData?.returnOrder?.orderStatus ||
                  RetrunOrderStatus.OUTBOUND == recordData?.returnOrder?.orderStatus) &&
                  PayStatus.UN_BALANCE == recordData?.returnOrder?.payStatus && (
                    <AuthButton
                      type="primary"
                      authority="purchaseReturnSettlement"
                      onClick={() => setPaymentVisible(true)}
                    >
                      确认结算
                    </AuthButton>
                  )}
                {RetrunOrderStatus.TO_OUTBOUND == recordData?.returnOrder?.orderStatus && (
                  <AuthButton
                    authority="purchaseReturnOutWareHouse"
                    type="primary"
                    onClick={() =>
                      setConfirmModalProps({
                        open: true,
                        tips: '是否确认一键出库？',
                        onOk: hanldConfirmStockOut,
                      })
                    }
                  >
                    一键出库
                  </AuthButton>
                )}
              </Space>
            }
            params={{ id: searchParams.get('returnId') ?? '' }}
            request={async (param) => {
              if (param?.id) {
                const data = await queryReturnOrderFacadeById({ ...param });
                setRecordData(data);

                return { data, success: true };
              }
              return [];
            }}
            column={4}
          >
            <ProDescriptions.Item label="供应商" dataIndex={['returnOrder', 'supplierName']} />
            <ProDescriptions.Item
              label="订单状态"
              dataIndex={['returnOrder', 'orderStatus']}
              valueEnum={retrunOrderStatusOptions}
            />
            <ProDescriptions.Item label="退款金额" dataIndex={['returnOrder', 'sumAmount']} />
            <ProDescriptions.Item
              label="退款方式"
              dataIndex={['returnOrder', 'payType']}
              valueEnum={payTypeStatusOptions}
            />
            <ProDescriptions.Item label="退款门店" dataIndex={['returnOrder', 'storeName']} />
            <ProDescriptions.Item
              label="发货仓库"
              dataIndex={['returnOrder', 'outWarehouseName']}
            />
            <ProDescriptions.Item label="下单时间" dataIndex={['returnOrder', 'createTime']} />
            <ProDescriptions.Item label="制单人" dataIndex={['returnOrder', 'creator']} />
            <ProDescriptions.Item label="订单备注" dataIndex={['returnOrder', 'remark']} />
          </ProDescriptions>
        </ProCard>
        <ProCard
          className="mt-4"
          bodyStyle={{ padding: 0 }}
          actions={
            <div className="flex p-6 justify-end ">
              <div className="flex text-[16px] text-black/[0.85] font-semibold items-center ">
                <div>
                  商品总数：<span>{recordData?.returnOrder?.num}</span>
                </div>
                <div className="flex items-center  pl-[40px]">
                  退款总金额：
                  <span className="text-[24px] font-medium text-[#F83431]">
                    <MoneyText text={recordData?.returnOrder?.sumAmount} />
                  </span>
                </div>
              </div>
            </div>
          }
        >
          <FunProTable<ReturnLineList, any>
            headerTitle={<SubTitle text="商品明细" />}
            columns={PostListTableColumns()}
            pagination={false}
            rowKey="id"
            dataSource={recordData?.returnLineList}
            search={false}
          />
        </ProCard>
        <CapitalFlow
          capitalData={recordData?.finCapitalFlowList?.map((s) => ({
            amount: s.amountYuan,
            confirmTime: s.bizTime,
            desc: s.accountName,
          }))}
        />
        <OperationDetail logList={recordData.logList} />
        <ConfirmModal
          {...confirmModalProps}
          onCancel={() => {
            setConfirmModalProps((preProps) => ({
              ...preProps,
              open: false,
            }));
          }}
        />
        <PaymentExternalFormModal
          totalAmount={recordData?.returnOrder?.sumAmount}
          visible={paymentVisible}
          storeId={recordData?.returnOrder?.storeId}
          onClose={() => setPaymentVisible(false)}
          onSubmit={hanldConfirmPay}
          dataSource={{
            payType: recordData?.returnOrder?.payType,
            paySubTypeList: recordData?.returnOrder?.paySubTypeList,
          }}
        />
      </Spin>
    </PageContainer>
  );
};
