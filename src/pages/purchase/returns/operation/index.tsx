import ConfirmModal from '@/components/ConfirmModal';
import GoodsSearch from '@/components/GoodsSearch';
import { GoodsSearchBizType } from '@/components/GoodsSearch/types/BizType';
import LeftTitle from '@/components/LeftTitle';
import PaymentExternalForm from '@/components/PaymentExternalForm';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import MoneyText from '@/components/common/MoneyText';
import { KeepAliveTabContext } from '@/layouts/context';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { queryStoreByAccount } from '@/pages/system/user/services';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { FileAddOutlined, FileSearchOutlined } from '@ant-design/icons';
import type { EditableFormInstance, ProFormInstance } from '@ant-design/pro-components';
import {
  CheckCard,
  PageContainer,
  ProCard,
  ProForm,
  ProFormDependency,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { history, useAccess, useIntl, useLocation, useSearchParams } from '@umijs/max';
import { useDebounceFn } from 'ahooks';
import { Button, Checkbox, Col, ConfigProvider, Flex, GetProps, Row, Space, Spin, message } from 'antd';
import { isEmpty } from 'lodash';
import { useContext, useEffect, useRef, useState } from 'react';
import { PayTypeStatus } from '../../list/types/PayTypeStatus';
import { YesNoStatus } from '../../supplier/operation/types/YesNo';
import { querySupplierList } from '../../supplier/services';
import type { ReturnLineList } from '../detail/types/line.post.entity';
import type { ReutrnPostEntity } from '../detail/types/return.post.entity';
import type { RetrunOrderStatus } from '../list/types/RetrunOrderStatus';
import { retrunOrderStatusOptions } from '../list/types/RetrunOrderStatus';
import { queryReturnOrderFacadeById } from '../services';
import { OrderReturnsColumns } from './config/OrderReturnsColumns';
import { PurchaseReturnsDetailColumns } from './config/PurchaseReturnsDetailColumns';
import {
  addItemReturnsPost,
  confirmPayReturnPost,
  confirmStockOutReturnPost,
  createReturnOrderPost,
  deleteItemsReturnsPost,
  queryOrderLineFacadeListPost,
  returnUpdateOutWarehousePost,
  submitReturnOrderPost,
  updateItemPost,
  updateRemarkPost,
  updateReturnReasonPost,
  updateReturnTypePost,
} from './services';
import { SourceStatus } from './types/SoureStatus';
import type { PostOrderLineEntity } from './types/post.order.line.entity';
import type { UpdateReturnRequest } from './types/update.return.request';
import type { UpdateReturnTypeRequest } from './types/update.return.type.request';

const PurchaseReturns = () => {
  const intl = useIntl();
  // 加载中
  const [loading, setLoading] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const access = useAccess();
  // 动态设置
  const [tabActiveKey, setTabActiveKey] = useState<SourceStatus>(SourceStatus.RETURN_BY_ORDER);
  const [orderNo, setOrderNo] = useState<string | undefined>('');
  const [orderStatus, setOrderStatus] = useState<RetrunOrderStatus>();
  const [addItemSn, setAddItemSn] = useState<string[]>([]);
  const formRef = useRef<ProFormInstance>();
  const formRefOne = useRef<ProFormInstance>();
  const formRefTwo = useRef<ProFormInstance>();
  const editFormRef = useRef<EditableFormInstance>();
  const [dataSourceCache, setDataSourceCache] = useState<ReturnLineList[]>([]);
  const [resultDataCache, setResultDataCache] = useState<ReutrnPostEntity>({});
  const [orderDataCache, setOrderDataCache] = useState<PostOrderLineEntity[]>([]);
  const [accountList, setAccountList] = useState<any[]>([]);
  // 可编辑行
  const [editorRows, setEditorRows] = useState<string[]>([]);

  const [orderEditorRows, setOrderEditorRows] = useState<string[]>([]);

  const orderId = searchParams.get('returnOrderId') ?? '';
  // 发货仓信息监听
  const warehouseId = ProForm.useWatch('outWarehouseId', formRef.current ?? {});
  // 销售门店信息监听
  const storeId = ProForm.useWatch('storeId', formRef.current ?? {});
  const location = useLocation();
  const { closeTab } = useContext(KeepAliveTabContext);

  useEffect(() => {
    if (orderId) {
      queryDetail();
    }
    return () => {
      setOrderNo('');
      setResultDataCache({});
      formRef?.current?.resetFields();
      setDataSourceCache([]);
    };
  }, [orderId]);

  const queryDetail = async () => {
    if (orderId) {
      const result = await queryReturnOrderFacadeById({ id: orderId });
      if (result?.returnLineList) {
        result?.returnLineList.forEach((s) => {
          s.returnsId = s.purchaseOrderNo + '' + s.itemSn;
          s.storeName = result?.returnOrder?.storeName;
        });
        setDataSourceCache(result?.returnLineList);
        setEditorRows(result.returnLineList?.map((item) => (item as ReturnLineList).id ?? ''));
      } else {
        setDataSourceCache([]);
      }
      if (result?.returnOrder) {
        formRef?.current?.setFieldsValue(result.returnOrder);
        formRefOne.current?.setFieldsValue(result.returnOrder);
        setOrderNo(result?.returnOrder?.orderNo);
        setOrderStatus(result?.returnOrder?.orderStatus);
        setResultDataCache(result);
        setTabActiveKey(result?.returnOrder.source!);
      }
    }
  };

  type RowKeyType = 'id' | 'brandId';
  const plainOptions = [];
  if (access.hasButtonPerms('purchaseConfirmSettlement')) {
    plainOptions.push({ label: intl.formatMessage({ id: 'purchase.returns.operation.checkbox.confirmSettlement' }), value: '0' });
  }
  if (access.hasButtonPerms('purchaseInput')) {
    plainOptions.push({ label: intl.formatMessage({ id: 'purchase.returns.operation.checkbox.directOutbound' }), value: '1' });
  }
  if (access.hasButtonPerms('purchaseSubmitPrint')) {
    plainOptions.push({ label: intl.formatMessage({ id: 'purchase.returns.operation.checkbox.printAfterSubmit' }), value: '2' });
  }

  const actionRef = useRef<ActionType>();
  const actionRefOne = useRef<ActionType>();

  const onCheckChange = (key: any) => {
    //actionRef.current?.reload(true);
    setTabActiveKey(key);
  };

  const [checkedList, setCheckedList] = useState<string[]>([]);
  const onCheckboxChange = (list: string[]) => {
    setCheckedList(list);
  };

  const [confirmModalProps, setConfirmModalProps] = useState<GetProps<typeof ConfirmModal>>({
    open: false,
  });

  const rules = [{ required: true }];
  /**
   * 删除
   */
  const onRemoveClick = async (props: ReturnLineList) => {
    const data = await deleteItemsReturnsPost({ idList: [props.id], orderId, orderNo });
    if (data) {
      queryDetail();
    }
  };

  const handleCreate = async (itemList: any[]) => {
    const item = itemList[0];
    formRef.current
      ?.validateFields?.()
      .then(async (values) => {
        setLoading(true);
        const { id } = await createReturnOrderPost({
          itemList: [
            {
              itemSn: item.itemSn,
              price: item.price,
              num: item.number,
              purchaseOrderNo: item.purchaseOrderNo,
            },
          ],
          orderBase: { ...values, source: tabActiveKey },
        });
        if (!isEmpty(id)) {
          history.push(`/purchase/returns/operation?returnOrderId=${id}`);
        }
        setLoading(false);
      })
      .catch((e) => {
        if (e?.errorFields?.length > 0) {
          message.warning(intl.formatMessage({ id: 'purchase.returns.operation.message.completeRequired' }));
        }
        setLoading(false);
      });
  };

  /**
   * order 可退货订单新增行
   * @param item
   * @returns
   */
  const handleAdd = async (itemList: any[]) => {
    const item = itemList[0];
    if (dataSourceCache.length >= 200) {
      message.warning(intl.formatMessage({ id: 'purchase.returns.operation.message.maxItems' }));
      return;
    }
    setLoading(true);
    //编辑
    // @ts-ignore
    const result = await addItemReturnsPost({
      itemSn: item.itemSn,
      num: item.number,
      price: item.price,
      purchaseOrderNo: item.purchaseOrderNo,
      orderNo,
      orderId,
    });
    if (result) {
      message.success(intl.formatMessage({ id: 'purchase.returns.operation.message.addSuccess' }));
      queryDetail();
    }
    setLoading(false);
  };
  const handleDetailUpdate = async (item: UpdateReturnRequest) => {
    const result = await updateItemPost({
      num: item.num,
      price: item.price,
      id: item.id,
      orderId,
      orderNo,
    });
    if (result) {
      queryDetail();
    }
  };
  const { run: updaterun } = useDebounceFn(
    (data) => {
      handleDetailUpdate(data);
    },
    { wait: 300 },
  );

  useEffect(() => {
    if (storeId) {
      queryMemberAccountPage({
        belongToStore: [storeId],
      }).then((result) => {
        if (result?.data?.length) {
          setAccountList(
            result.data.map((item) => ({
              label: item.memberAccountName,
              value: item.id,
            })),
          );
        }
      });
    }
  }, [storeId]);

  const handleDetailReasonUpdate = async (item: UpdateReturnRequest) => {
    const result = await updateReturnReasonPost({
      id: item.id,
      orderId,
      orderNo,
      reason: item.reason,
    });
    if (result) {
      queryDetail();
    }
  };
  /**
   * 提交采购退货
   */
  const handlesubmitReturn = async () => {
    const result = await submitReturnOrderPost({ orderId, orderNo });
    if (result) {
      //checkedList
      if (checkedList.length > 0) {
        if (checkedList.includes('0')) {
          //确认结算
          const payValues = await formRefOne.current?.validateFields();
          const data = await confirmPayReturnPost({ orderId, orderNo, ...payValues });
          if (data && checkedList.includes('1')) {
            //直接入库
            const data = await confirmStockOutReturnPost({ orderId, orderNo });
          }
        }
        if (checkedList.includes('1') && !checkedList.includes('0')) {
          //单独存在 直接入库
          const data = await confirmStockOutReturnPost({ orderId, orderNo });
        }
        if (checkedList.includes('2')) {
          window.open(`/print?returnId=${orderId}&printType=${PrintType.purchaseReturnOrder}`);
        }
      }
      //提交成功关闭当前页面 回到列表也
      closeTab();
      history.push('/purchase/returns/detail?returnId=' + orderId); //跳详情
    }
  };

  /**
   * 编辑 头部信息
   * @param item
   */
  const handleUpdateReturns = async (item: UpdateReturnTypeRequest) => {
    const data = await updateReturnTypePost({ ...item, orderId, orderNo });
    if (data) {
      queryDetail();
    }
  };

  const handleUpdatePayType = async (value: any, values: any) => {
    if (value?.payType || value?.paySubTypeList) {
      if (PayTypeStatus.HANGING_ACCOUNTS == values?.payType) {
        //挂账直接保存
        handleUpdateReturns({ ...values, paySubTypeList: null });
      } else {
        if (values?.paySubTypeList == undefined || values?.paySubTypeList?.length == 0) {
          //直接塞默认值
          handleUpdateReturns({
            payType: PayTypeStatus.CASH,
            paySubTypeList: [
              { id: accountList?.[0]?.value, amount: resultDataCache?.returnOrder?.sumAmount + '' },
            ],
          });
          return;
        }
        // 手动触发校验
        const newTimeout = window.setTimeout(async () => {
          try {
            const validatedFields = await formRefOne.current?.validateFields();
            if (validatedFields) {
              handleUpdateReturns(validatedFields);
            }
          } catch (error) {
            // 有字段未通过校验
          }
        }, 1000);
      }
    }
    if (value?.remark) {
      await updateRemarkPost({ orderId, orderNo, remark: value?.remark });
    }
  };

  const { run } = useDebounceFn(
    (changedValues: any, allValues: any) => handleUpdatePayType(changedValues, allValues),
    {
      wait: 500,
    },
  );

  /**
   * 更新仓库
   * @param e
   */
  const returnUpdateOutWarehouse = async (e: string) => {
    if (!isEmpty(orderId)) {
      const data = await returnUpdateOutWarehousePost({ orderId, orderNo, outWarehouseId: e });
      if (data) {
        queryDetail();
        actionRef.current?.reload(true);
        formRefTwo.current?.resetFields();
      }
    }
  };

  /**
   * 采购退货
   * @param rowKey
   */
  const handleOrderAdd = async (rowKey: string) => { };

  return (
    <PageContainer>
      <ProCard>
        <ProForm submitter={false} formRef={formRef} layout="horizontal">
          <Row gutter={40}>
            <Col span={8}>
              <ProFormSelect
                name="supplierId"
                label={intl.formatMessage({ id: 'purchase.returns.operation.form.supplier' })}
                placeholder={intl.formatMessage({ id: 'purchase.returns.operation.form.supplier.placeholder' })}
                rules={rules}
                disabled={!isEmpty(orderNo)}
                fieldProps={{ fieldNames: { label: 'supplierName', value: 'supplierId' } }}
                request={querySupplierList}
              />
            </Col>

            <Col span={8}>
              <ProFormSelect
                name="storeId"
                label={intl.formatMessage({ id: 'purchase.returns.operation.form.store' })}
                disabled={!isEmpty(orderNo)}
                rules={rules}
                placeholder={intl.formatMessage({ id: 'purchase.returns.operation.form.store.placeholder' })}
                fieldProps={{ fieldNames: { label: 'name', value: 'id' } }}
                request={() =>
                  queryStoreByAccount({ status: 1 }).then((s) => {
                    if (s[0] && isEmpty(searchParams.get('returnOrderId'))) {
                      formRef.current?.setFieldValue('storeId', s[0].id);
                    }
                    return s;
                  })
                }
              />
            </Col>
            <Col span={8}>
              <ProFormDependency name={['storeId']}>
                {({ storeId }) => {
                  return (
                    <ProFormSelect
                      label={intl.formatMessage({ id: 'purchase.returns.operation.form.warehouse' })}
                      rules={rules}
                      // disabled={!isEmpty(orderNo)}
                      name="outWarehouseId"
                      fieldProps={{
                        fieldNames: { label: 'warehouseName', value: 'id' },
                      }}
                      onChange={returnUpdateOutWarehouse}
                      params={{ storeIdList: [storeId] }}
                      request={(params) => {
                        if (params?.storeIdList.length > 0) {
                          return warehouseList({ ...params, state: YesNoStatus.YES }).then((s) => {
                            if (
                              s?.warehouseSimpleRoList?.length! > 0 &&
                              isEmpty(searchParams.get('returnOrderId'))
                            ) {
                              formRef.current?.setFieldValue(
                                'outWarehouseId',
                                s?.warehouseSimpleRoList?.[0].id,
                              );
                            }
                            if (s?.warehouseSimpleRoList?.length == 0) {
                              //无仓库信息
                              formRef.current?.setFieldValue('outWarehouseId', undefined);
                            }
                            return s.warehouseSimpleRoList ?? [];
                          });
                        }
                        return new Promise((resolve, reject) => {
                          return reject([]);
                        });
                      }}
                    />
                  );
                }}
              </ProFormDependency>
            </Col>
          </Row>
        </ProForm>
      </ProCard>
      <ProCard className="mt-4" bodyStyle={{ paddingLeft: 24, paddingTop: 0, paddingBottom: 0 }}>
        <CheckCard.Group
          className="pt-6"
          defaultValue={SourceStatus.RETURN_BY_ORDER}
          value={tabActiveKey}
          onChange={(checked) => {
            if (checked) {
              onCheckChange(checked);
            }
          }}
          disabled={!isEmpty(orderNo)}
        >
          <CheckCard
            avatar={<FileSearchOutlined style={{ fontSize: 22 }} />}
            title={intl.formatMessage({ id: 'purchase.returns.operation.tab.returnByOrder' })}
            description={intl.formatMessage({ id: 'purchase.returns.operation.tab.returnByOrder.description' })}
            value={SourceStatus.RETURN_BY_ORDER}
            style={{ width: 240 }}
          />
          <CheckCard
            title={intl.formatMessage({ id: 'purchase.returns.operation.tab.returnByItem' })}
            avatar={<FileAddOutlined style={{ fontSize: 22 }} />}
            description={intl.formatMessage({ id: 'purchase.returns.operation.tab.returnByItem.description' })}
            value={SourceStatus.RETURN_BY_ITEM}
            style={{ width: 240 }}
          />
        </CheckCard.Group>
      </ProCard>
      <Spin spinning={loading}>
        {tabActiveKey == SourceStatus.RETURN_BY_ORDER && (
          <FunProTable<PostOrderLineEntity, any>
            className="reset-ant-pro-table-search"
            key="reset-ant-pro-table-operation"
            editable={{
              type: 'single',
              editableKeys: orderEditorRows,
              formProps: {
                formRef: formRefTwo,
              },
              onValuesChange: (record, recordList) => {
                setOrderDataCache(recordList);
              },
            }}
            onDataSourceChange={(dataSource) => {
              setOrderDataCache(dataSource);
            }}
            rowKey="returnsId"
            manualRequest={true}
            formRef={editFormRef}
            requestPage={async (params) => {
              const values = await formRef.current?.validateFieldsReturnFormatValue?.();
              if (values.supplierId && values.storeId && values.outWarehouseId) {
                const paramsVal = {
                  ...params,
                  supplierId: values.supplierId,
                  storeIdList: [values.storeId],
                  receiveWarehouseId: values.outWarehouseId,
                };
                const result = await queryOrderLineFacadeListPost(paramsVal);
                if (result) {
                  const { data, total } = result;
                  data?.forEach((s) => {
                    s.returnsId = `${s.orderNo}${s.itemSn}`;
                  });
                  setOrderDataCache(data);
                  setOrderEditorRows(
                    data?.map((item) => (item as PostOrderLineEntity).returnsId ?? ''),
                  );
                  return {
                    data: data?.map((t) => ({
                      ...t,
                      number: t.returnableQuantity ?? 0,
                      returnPrice: t.price ?? 0,
                    })),
                    success: true,
                    total,
                  };
                }
              }
              return { data: [], total: 0 };
            }}
            scroll={{ x: 1300 }}
            actionRef={actionRef}
            columns={OrderReturnsColumns({
              intl,
              addedItemPurchaseOrderNo: dataSourceCache?.map((item) => item.returnsId ?? ''),
              handleAdd: (item) => (isEmpty(orderId) ? handleCreate([item]) : handleAdd([item])),
              orderDataCache,
            })}
          />
        )}
        {tabActiveKey === SourceStatus.RETURN_BY_ITEM && (
          <ProCard bodyStyle={{ paddingTop: 24 }}>
            <GoodsSearch
              bizType={GoodsSearchBizType.ExternalPurchaseReturn}
              addedItemSns={dataSourceCache?.map((item) => item.itemSn ?? '')}
              storeId={storeId}
              warehouseId={warehouseId}
              onAdd={(itemList) =>
                isEmpty(orderId) ? handleCreate(itemList) : handleAdd(itemList)
              }
            />
          </ProCard>
        )}
        <ConfigProvider
          theme={{
            token: {
              colorPrimary: '#F49C1F',
            },
            components: {
              InputNumber: {
                controlWidth: 120,
              },
            },
          }}
        >
          <FunProTable<ReturnLineList, any>
            className="mt-4"
            rowKey="id"
            pagination={false}
            headerTitle={
              <Space size={16}>
                <LeftTitle title={intl.formatMessage({ id: 'purchase.returns.operation.detail.title' })} />
                <span className="text-[14px] text-black/[0.8]">
                  {intl.formatMessage({ id: 'purchase.returns.operation.detail.orderNo' })}：{orderNo ? orderNo : '-'}
                </span>
                <span className="text-[14px] text-black/[0.8]">
                  {intl.formatMessage({ id: 'purchase.returns.operation.detail.status' })}：{orderStatus ? retrunOrderStatusOptions[orderStatus]?.text : '-'}
                </span>
              </Space>
            }
            editable={{
              editableKeys: editorRows,
              actionRender: (row) => {
                return [
                  <Button key="delete" type="link" onClick={() => onRemoveClick(row)}>
                    {intl.formatMessage({ id: 'purchase.returns.operation.detail.delete' })}
                  </Button>,
                ];
              },
            }}
            scroll={{ x: 1300 }}
            search={false}
            dataSource={dataSourceCache}
            actionRef={actionRefOne}
            columns={PurchaseReturnsDetailColumns({
              intl,
              handleDetailUpdate: updaterun,
              handleDetailReasonUpdate,
            })}
          />
        </ConfigProvider>
        <ProCard bodyStyle={{ padding: 0 }}>
          <ProForm
            submitter={false}
            formRef={formRefOne}
            disabled={isEmpty(orderId)}
            onValuesChange={run}
          >
            <Flex justify="space-between" className="px-6">
              <PaymentExternalForm storeId={resultDataCache?.returnOrder?.storeId} />
              <ProFormText
                name="remark"
                disabled={isEmpty(orderNo)}
                width={'lg'}
                label={intl.formatMessage({ id: 'purchase.returns.operation.form.remark' })}
                placeholder={intl.formatMessage({ id: 'purchase.returns.operation.form.remark.placeholder' })}
                fieldProps={{ count: { max: 100, show: true }, maxLength: 100 }}
                rules={[{ max: 100 }]}
              />
            </Flex>
          </ProForm>
        </ProCard>
        <ProCard className="mt-[1px]">
          <Flex justify="space-between">
            <Flex key="summary" gap={80} justify="flex-start" align="center">
              <span className="text-[16px] font-semibold text-[#000000D9]">
                {intl.formatMessage({ id: 'purchase.returns.operation.summary.returnQuantity' })}：{resultDataCache?.returnOrder?.num ?? '-'}
              </span>
              <span className="flex flex-row items-center">
                <span className="text-[16px] font-semibold text-[#000000D9]">{intl.formatMessage({ id: 'purchase.returns.operation.summary.returnAmount' })}：</span>
                <span className="text-[24px] font-medium text-[#F83431]">
                  <MoneyText text={resultDataCache?.returnOrder?.sumAmount} />
                </span>
              </span>
            </Flex>
            <Space>
              <Checkbox.Group
                options={plainOptions}
                value={checkedList}
                onChange={onCheckboxChange}
              />
              <AuthButton
                authority="purchaseReturnsSubmit"
                type="primary"
                onClick={handlesubmitReturn}
                disabled={dataSourceCache?.length == 0}
              >
                {intl.formatMessage({ id: 'purchase.returns.operation.button.submit' })}
              </AuthButton>
            </Space>
          </Flex>
        </ProCard>
      </Spin>
      <ConfirmModal
        {...confirmModalProps}
        onCancel={() => {
          setConfirmModalProps((preProps) => ({
            ...preProps,
            open: false,
          }));
        }}
      />
    </PageContainer>
  );
};

export default withKeepAlive(PurchaseReturns);
