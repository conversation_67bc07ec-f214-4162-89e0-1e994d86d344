import ColumnRender from '@/components/ColumnRender';
import AuthButton from '@/components/common/AuthButton';
import { requiredProps } from '@/types/validateRules';
import { MAX_AMOUNT } from '@/utils/Constants';
import { type ProColumns } from '@ant-design/pro-components';
import { message } from 'antd';
import type { PostOrderLineEntity } from '../types/post.order.line.entity';
export interface OrderReturnsColumnsProps {
  intl: any;
  /**
   * 已选退款订单NO集合
   */
  addedItemPurchaseOrderNo: string[];
  /**
   * 添加商品事件
   * @param item
   */
  handleAdd: (item: PostOrderLineEntity) => void;
  orderDataCache: PostOrderLineEntity[];
}

export const OrderReturnsColumns = (props: OrderReturnsColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      fixed: 'left',
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.productInfo' }),
      hideInTable: true,
      dataIndex: 'itemSn',
      fixed: 'left',
      editable: false,
      fieldProps: {
        placeholder: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.productInfo.placeholder' }),
      },
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.purchaseOrderNo' }),
      dataIndex: 'orderNo',
      width: 150,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.purchaseTime' }),
      dataIndex: 'orderTime',
      width: 140,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.productCode' }),
      dataIndex: 'itemSn',
      search: false,
      width: 100,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.productName' }),
      dataIndex: 'skuName',
      search: false,
      width: 120,
      ellipsis: true,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.oe' }),
      dataIndex: 'oe',
      search: false,
      width: 100,
      editable: false,
      render: (_, entity) => ColumnRender.ArrayColumnRender(entity.oeList ?? []),
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.brandPartNo' }),
      dataIndex: 'brandPartNo',
      search: false,
      width: 100,
      editable: false,
      render: (_, entity) => ColumnRender.ArrayColumnRender(entity.brandPartNoList ?? []),
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.brand' }),
      dataIndex: 'brandName',
      search: false,
      width: 100,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.purchaseStore' }),
      dataIndex: 'storeName',
      search: false,
      width: 120,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.receiveWarehouse' }),
      dataIndex: 'receiveWarehouseName',
      search: false,
      width: 120,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.paymentMethod' }),
      dataIndex: 'payTypeDesc',
      search: false,
      width: 80,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.actualPrice' }),
      dataIndex: 'price',
      search: false,
      width: 80,
      editable: false,
      valueType: 'money',
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.purchaseQuantity' }),
      dataIndex: 'receivedQuantity',
      search: false,
      width: 80,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.returnableQuantity' }),
      dataIndex: 'returnableQuantity',
      search: false,
      width: 80,
      fixed: 'right',
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.returnAmount' }),
      dataIndex: 'returnPrice',
      search: false,
      width: 100,
      fixed: 'right',
      valueType: 'digit',
      editable: true,
      fieldProps(_, config) {
        return {
          disabled:
            props.addedItemPurchaseOrderNo?.includes(config.entity?.returnsId as string) ||
            (config.entity?.returnableQuantity ?? 0) <= 0,
          min: 0.01,
          max: MAX_AMOUNT,
          precision: 2,
          onPressEnter: (e) => {
            e.stopPropagation();
          },
        };
      },
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.returnQuantity' }),
      dataIndex: 'number',
      width: 100,
      fixed: 'right',
      editable: true,
      search: false,
      valueType: 'digit',
      fieldProps(_, config) {
        return {
          disabled:
            props.addedItemPurchaseOrderNo?.includes(config.entity?.returnsId as string) ||
            (config.entity?.returnableQuantity ?? 0) <= 0,
          min: 1,
          max: config?.entity?.returnableQuantity,
          onPressEnter: (e) => {
            e.stopPropagation();
            const item = props.orderDataCache?.find((s) => s.returnsId == config.entity.returnsId);
            props.handleAdd({
              itemSn: item?.itemSn,
              number: item?.number,
              price: item?.returnPrice,
              purchaseOrderNo: item?.orderNo,
            });
          },
        };
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.operation' }),
      width: 60,
      editable: false,
      key: 'operation',
      fixed: 'right',
      search: false,
      render: (text, row) => (
        <AuthButton
          type="link"
          authority="purchaseReturnsAdd"
          disabled={
            props.addedItemPurchaseOrderNo?.includes(row?.returnsId as string) ||
            row?.returnableQuantity! < 1
          }
          onClick={async () => {
            console.log(row);
            const item = props.orderDataCache?.find((s) => s.returnsId == row.returnsId);
            if (item) {
              if (!item.returnPrice) {
                message.warning(props.intl.formatMessage({ id: 'purchase.returns.operation.message.fillReturnAmount' }));
                return;
              }
              if (!item.number) {
                message.warning(props.intl.formatMessage({ id: 'purchase.returns.operation.message.fillReturnQuantity' }));
                return;
              }
              props.handleAdd({
                itemSn: item.itemSn,
                number: item.number,
                price: item.returnPrice,
                purchaseOrderNo: item.orderNo,
              });
            }
          }}
        >
          {props.intl.formatMessage({ id: 'purchase.returns.operation.orderColumns.button.return' })}
        </AuthButton>
      ),
    },
  ] as ProColumns<PostOrderLineEntity>[];
