import ColumnRender from '@/components/ColumnRender';
import { requiredProps } from '@/types/validateRules';
import { MAX_AMOUNT, MAX_COUNT } from '@/utils/Constants';
import { type ProColumns } from '@ant-design/pro-components';
import { Input } from 'antd';
import { isEmpty } from 'lodash';
import type { ReturnLineList } from '../../detail/types/line.post.entity';
import type { UpdateReturnRequest } from '../types/update.return.request';

export interface PurchaseReturnsDetailColumnsProps {
  intl: any;
  handleDetailUpdate: (item: UpdateReturnRequest) => void;
  handleDetailReasonUpdate: (item: UpdateReturnRequest) => void;
}

export const PurchaseReturnsDetailColumns = (props: PurchaseReturnsDetailColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      editable: false,
      fixed: 'left',
    },

    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.detailColumns.productCode' }),
      dataIndex: 'itemSn',
      width: 100,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.detailColumns.productName' }),
      dataIndex: 'skuName',
      width: 120,
      ellipsis: true,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.detailColumns.oe' }),
      dataIndex: 'oe',
      search: false,
      width: 140,
      editable: false,
      render: (_, entity) => ColumnRender.ArrayColumnRender(entity.oeList ?? []),
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.detailColumns.brandPartNo' }),
      dataIndex: 'brandPartNo',
      search: false,
      width: 100,
      editable: false,
      render: (_, entity) => ColumnRender.ArrayColumnRender(entity.brandPartNoList ?? []),
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.detailColumns.brand' }),
      dataIndex: 'brandName',
      search: false,
      width: 100,
      ellipsis: true,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.detailColumns.purchaseOrderNo' }),
      dataIndex: 'purchaseOrderNo',
      search: false,
      width: 150,
      ellipsis: true,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.detailColumns.purchaseStore' }),
      dataIndex: 'storeName',
      search: false,
      width: 120,
      ellipsis: true,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.detailColumns.returnAmount' }),
      dataIndex: 'sumPrice',
      search: false,
      width: 130,
      valueType: 'money',
      fixed: 'right',
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
      fieldProps(_, config) {
        return {
          min: 0.01,
          precision: 2,
          max: MAX_AMOUNT,
          onPressEnter: (e) => {
            if (!isEmpty(e)) {
              props.handleDetailUpdate({
                id: config.entity?.id!,
                price: Number(e),
                num: config.entity?.num,
              });
            }
          },
          onChange: (e: any) => {
            if (!isEmpty(e) || e > 0) {
              props.handleDetailUpdate({
                id: config.entity?.id!,
                price: Number(e),
                num: config.entity?.num,
              });
            }
          },
        };
      },
      editable: () => true,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.detailColumns.returnQuantity' }),
      dataIndex: 'num',
      search: false,
      width: 130,
      fixed: 'right',
      valueType: 'digit',
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
      fieldProps(_, config) {
        return {
          min: 1,
          precision: 0,
          max: MAX_COUNT,
          onPressEnter: (e) => {
            if (!isEmpty(e)) {
              props.handleDetailUpdate({
                id: config.entity?.id!,
                num: Number(e),
                price: config.entity?.price,
              });
            }
          },
          onChange: (e: any) => {
            if (!isEmpty(e) || e > 0) {
              props.handleDetailUpdate({
                id: config.entity?.id!,
                num: Number(e),
                price: config.entity?.price,
              });
            }
          },
        };
      },
      editable: () => true,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.returns.operation.detailColumns.returnReason' }),
      dataIndex: 'reason',
      search: false,
      editable: () => true,
      width: 140,
      valueType: 'text',
      fixed: 'right',
      renderFormItem: (item, config) => {
        return (
          <Input
            placeholder={props.intl.formatMessage({ id: 'purchase.returns.operation.detailColumns.returnReason.placeholder' })}
            onBlur={(value) => {
              props.handleDetailReasonUpdate({
                id: config.record?.id!,
                reason: value?.target.value,
              });
            }}
          />
        );
      },
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.operation' }),
      valueType: 'option',
      fixed: 'right',
      width: 100,
    },
  ] as ProColumns<ReturnLineList>[];
