import { type PropertyModalFromType } from '@/types/PropertyModalFromType';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { useAsyncEffect } from 'ahooks';
import { useForm } from 'antd/lib/form/Form';
export default ({
  title,
  recordId,
  visible,
  onCancel,
  onOk,
  readOnly,
  inputFieldName,
  inputFieldLabel,
}: PropertyModalFromType<number>) => {
  const [form] = useForm();
  useAsyncEffect(async () => {
    if (recordId == 0) {
      form.resetFields();
    } else {
      //const { data } = await get({ id: recordId });
      //form.setFieldsValue(data);
    }
  }, [visible]);

  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };
  const rules = [{ required: !readOnly }];
  return (
    <ModalForm
      {...layout}
      form={form}
      layout="horizontal"
      title={title}
      open={visible}
      width="40%"
      modalProps={{
        centered: true,
        onCancel: onCancel,
      }}
      submitTimeout={2000}
      onFinish={onOk}
    >
      <ProFormText
        rules={rules}
        name={inputFieldName}
        label={inputFieldLabel}
        placeholder="请输入"
      />
      <ProFormText name="id" hidden={true} />
    </ModalForm>
  );
};
