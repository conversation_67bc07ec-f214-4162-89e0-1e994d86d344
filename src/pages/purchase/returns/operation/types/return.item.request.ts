export interface ReturnItemRequest {
    /**
     * 商品编码
     */
    itemSn?: string;
    /**
     * 门店系统侧零售商ID
     */
    memberId?: string;
    /**
     * 退货数量
     */
    num?: number;
    /**
     * 退货单id
     */
    orderId?: string;
    /**
     * 退货单号
     */
    orderNo?: string;
    /**
     * 商品退货单价
     */
    price?: number;
    /**
     * 采购单号
     */
    purchaseOrderNo?: string;
    /**
     * 退货原因
     */
    reason?: string;
    /**
     * 更新人
     */
    updatePerson?: string;
}