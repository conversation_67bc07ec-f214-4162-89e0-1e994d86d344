import { SourceStatus } from './SoureStatus';

export interface CreateReturnOrderRequest {
  /**
   * 明细信息
   */
  itemList?: ItemList[];
  orderBase?: OrderBase;
}

export interface ItemList {
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 门店系统侧零售商ID
   */
  memberId?: string;
  /**
   * 退货数量
   */
  num?: number;
  /**
   * 退货单id
   */
  orderId?: string;
  /**
   * 退货单号
   */
  orderNo?: string;
  /**
   * 商品退货单价
   */
  price?: number;
  /**
   * 采购单号
   */
  purchaseOrderNo?: string;
  /**
   * 退货原因
   */
  reason?: string;
  /**
   * 更新人
   */
  updatePerson?: string;
}

export interface OrderBase {
  /**
   * 一体系零售商id
   */
  memberId?: string;
  /**
   * 一体系零售商name
   */
  memberName?: string;
  /**
   * 出库仓id
   */
  outWarehouseId?: string;
  /**
   * 出库仓name
   */
  outWarehouseName?: string;
  /**
   * 具体退款方式，现款时不允许为空
   */
  paySubTypeList?: PaySubTypeList[];
  /**
   * 退款方式0挂帐1现款
   */
  payType?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 退货门店
   */
  storeId?: string;
  /**
   * 退货门店名称
   */
  storeName?: string;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 供应商name
   */
  supplierName?: string;

  source?: SourceStatus;
}

export interface PaySubTypeList {
  /**
   * 金额
   */
  amount?: string;
  /**
   * 子支付方式描述
   */
  desc?: string;
  /**
   * 子支付方式id
   */
  id?: string;
}
