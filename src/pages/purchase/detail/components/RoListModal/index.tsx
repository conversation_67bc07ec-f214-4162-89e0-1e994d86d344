import { RightOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { Flex, Image, Modal, Space, message } from 'antd';
import { useEffect, useState } from 'react';
import { useIntl } from 'umi';
import icon from '../../image/rolist_icon.png';
import { queryEtcAfterDetailPagePost } from '../../services';
import { RecordRoList } from '../../types/ro.list.entity';

export interface RoListProps {
  recordRolist?: RecordRoList[];
  visible: boolean;
  onClose: () => void;
}

const RoList = (props: RoListProps) => {
  const { visible, onClose, recordRolist } = props;
  const [localCurrentId, setLocalCurrentId] = useState<string>();
  const intl = useIntl();

  useEffect(() => {
    if (!visible) {
      setLocalCurrentId(undefined);
    }
  }, [visible]);

  const openEtcAfter = async (afterSalesNo?: string, etcSkuId?: string) => {
    const data = await queryEtcAfterDetailPagePost({ afterSalesNo, etcSkuId });
    if (data) {
      //打开一体系页面
      window.open(data);
    }
  };

  const handleOK = () => {
    if (localCurrentId) {
      onClose();
    } else {
      message.warning(intl.formatMessage({ id: 'purchase.afterSales.validation.selectVehicle' }));
    }
  };

  return (
    <Modal title={intl.formatMessage({ id: 'purchase.afterSales.modal.title' })} open={visible} onCancel={onClose} footer={null} centered>
      <ProCard style={{ marginBlockStart: 8 }} gutter={[16, 16]} wrap>
        {recordRolist?.map((item) => (
          <ProCard bordered onClick={() => openEtcAfter(item.returnNo, item.etcSkuId)}>
            <Flex justify="space-between">
              <Flex align="center">
                <Flex>
                  <Image src={icon} width={32} height={32}></Image>
                </Flex>
                <Flex vertical className="pl-4">
                  <span>{item.returnNo}</span>
                  <span>{item.returnTime}</span>
                </Flex>
              </Flex>
              <Space>
                {intl.formatMessage({ id: 'purchase.afterSales.button.viewDetail' })}
                <RightOutlined />
              </Space>
            </Flex>
          </ProCard>
        ))}
      </ProCard>
    </Modal>
  );
};

export default RoList;
