import { queryWarehouseList } from '@/pages/stocks/warehouse/services';
import { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';
import { useIntl } from 'umi';

export interface WareHouseModalProps {
  wareHouseId?: string;
  storeId?: string;
  visible: boolean;
  onClose?: () => void;
  onOk?: (value: any) => Promise<boolean | void>;
}

const WareHouseModal = (props: WareHouseModalProps) => {
  const { visible, onClose, wareHouseId, onOk, storeId } = props;
  const [selectWareHouseId, setSelectWareHouseId] = useState<string>();
  const intl = useIntl();

  useEffect(() => {
    if (!visible) {
      setSelectWareHouseId(undefined);
    } else {
      form.setFieldValue('storeId', storeId);
      form.setFieldValue('warehouseId', wareHouseId);
    }
  }, [visible]);

  const [form] = useForm();
  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  return (
    <ModalForm
      {...layout}
      form={form}
      layout="horizontal"
      title={intl.formatMessage({ id: 'purchase.warehouse.modal.title' })}
      open={visible}
      width="40%"
      modalProps={{
        maskClosable: false,
        centered: true,
        onCancel: onClose,
        destroyOnClose: true,
      }}
      submitTimeout={2000}
      onFinish={onOk}
    >
      <ProFormText hidden={true} name="storeId" />
      <ProFormSelect
        width={300}
        debounceTime={300}
        label={intl.formatMessage({ id: 'purchase.warehouse.label.receiveWarehouse' })}
        placeholder={intl.formatMessage({ id: 'purchase.warehouse.placeholder.selectWarehouse' })}
        name="warehouseId"
        showSearch={true}
        allowClear={false}
        dependencies={['storeId']}
        rules={[
          {
            required: true,
            message: intl.formatMessage({ id: 'purchase.warehouse.validation.selectWarehouse' }),
          },
        ]}
        request={(query) => {
          if (query.storeId) {
            return queryWarehouseList({
              warehouseNameKeyword: query.keyWords,
              storeIdList: [query.storeId],
              pageSize: 99,
            }).then((result) => {
              return result.data.map((item) => ({ label: item.warehouseName, value: item.id }));
            });
          } else {
            return new Promise((resolve, reject) => {
              return reject([]);
            });
          }
        }}
      />
    </ModalForm>
  );
};

export default WareHouseModal;
