import { CheckCard, ProForm } from '@ant-design/pro-components';
import { Form, Modal } from 'antd';
import { useForm } from 'antd/es/form/Form';
import TextArea from 'antd/es/input/TextArea';
import { useState } from 'react';
import { useIntl } from 'umi';
import { AuditStatus } from '../../types/AuditStatus';

export interface AuditModalProps {
  visible: boolean;
  onClose?: () => void;
  onOk?: (value: any) => Promise<boolean | void>;
}

const AuditModal = (props: AuditModalProps) => {
  const { visible, onClose, onOk } = props;
  const [form] = useForm();
  const intl = useIntl();
  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  const resultChange = (e: any) => {
    if (e == 'reject') {
      setIsCheck(true);
    } else {
      setIsCheck(false);
    }
  };
  /**
   * 提交
   */
  const handleOk = () => {
    form.validateFields?.().then(async (values) => {
      onOk!(values);
    });
  };
  const [isCheck, setIsCheck] = useState<boolean>(false);

  return (
    <Modal title={intl.formatMessage({ id: 'purchase.audit.modal.title' })} open={visible} onCancel={onClose} width={'480px'} centered onOk={handleOk}>
      <ProForm form={form} layout="horizontal" submitter={false}>
        <Form.Item
          name="result"
          rules={[{ required: true, message: intl.formatMessage({ id: 'purchase.audit.validation.selectResult' }) }]}
          style={{ marginBottom: 0 }}
        >
          <CheckCard.Group onChange={resultChange}>
            <CheckCard
              description={<span className="flex justify-center font-medium text-base">{intl.formatMessage({ id: 'purchase.audit.option.approve' })}</span>}
              value={AuditStatus.APPROVE}
              style={{ width: '208px' }}
            />
            <CheckCard
              description={
                <span className="flex justify-center font-medium text-base">{intl.formatMessage({ id: 'purchase.audit.option.reject' })}</span>
              }
              value={AuditStatus.REJECT}
              style={{ width: '208px' }}
            />
          </CheckCard.Group>
        </Form.Item>
        <Form.Item name="auditRemark" rules={[{ required: isCheck, message: intl.formatMessage({ id: 'purchase.audit.validation.rejectReason' }) }]}>
          <TextArea
            placeholder={isCheck ? intl.formatMessage({ id: 'purchase.audit.placeholder.reason' }) : intl.formatMessage({ id: 'purchase.audit.placeholder.remark' })}
            allowClear
            maxLength={100}
          />
        </Form.Item>
      </ProForm>
    </Modal>
  );
};

export default AuditModal;
