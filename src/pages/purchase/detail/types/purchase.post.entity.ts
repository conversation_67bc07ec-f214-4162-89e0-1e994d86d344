import { BalanceStatus } from '../../list/types/BalanceStatus';
import { OrderSourceStatus } from '../../list/types/OrderSourceStatus';
import { OrderStatus } from '../../list/types/OrderStatus';
import { PayStatus } from '../../list/types/PayStatus';
import { PayTypeStatus } from '../../list/types/PayTypeStatus';

export interface PurchasePostEntity {
  /**
   * 发货单号
   */
  deliveryNo?: string;
  /**
   * 预计到货时间
   */
  deliveryTime?: string;
  /**
   * 运费
   */
  freightAmount?: number;
  /**
   * 订单id
   */
  id?: string;
  /**
   * 零售商name
   */
  memberName?: string;
  /**
   * 订单号
   */
  orderNo?: string;
  /**
   * 操作日志
   */
  logList?: LogList[];
  /**
   * 数据来源
   */
  orderSource?: OrderSourceStatus;
  /**
   * 采购状态
   */
  orderStatus?: OrderStatus;
  /**
   * 采购时间
   */
  orderTime?: string;
  /**
   * 结算状态
   */
  payStatus?: PayStatus;
  /**
   * 付款状态
   */
  balanceStatus?: BalanceStatus;
  /**
   * 付款方式
   */
  payType?: PayTypeStatus;
  /**
   * 采购人
   */
  purchaseUser?: string;
  /**
   * 采购人id
   */
  purchaseUserId?: string;
  /**
   * 收货仓库id
   */
  receiveWarehouseId?: string;
  /**
   * 收货仓库
   */
  receiveWarehouseName?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 订单来源-子订单号
   */
  sourceNo?: string;

  storeId?: string;
  /**
   * 发起门店
   */
  storeName?: string;
  /**
   * 采购总金额
   */
  sumAmount?: number;
  /**
   * 采购数量
   */
  sumQuantity?: number;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 供应商name
   */
  supplierName?: string;
  /**
   * 是否需要审批
   */
  needApprove?: boolean;

  /**
   * 审核信息
   */
  auditInfo?: AuditInfo;

  /**
   * 子支付方式
   */
  paySubTypeList?: PaySubTypeList[];
}

/**
 * 审核信息
 */
export interface AuditInfo {
  /**
   * 操作时间
   */
  operationTime?: string;
  /**
   * 操作类型
   */
  operationType?: string;
  /**
   * 操作人
   */
  operator?: string;
  /**
   * 备注
   */
  remark?: string;
}

export interface PaySubTypeList {
  /**
   * 金额
   */
  amount?: string;
  /**
   * 子支付方式描述
   */
  desc?: string;
  /**
   * 子支付方式id
   */
  id?: string;

  confirmTime?: string;
}

export interface LogList {
  /**
   * 模块
   */
  module?: string;
  /**
   * 操作人名称
   */
  operatePerson?: string;
  /**
   * 操作业务单号
   */
  operationNo?: string;
  /**
   * 操作时间
   */
  operationTime?: string;
  /**
   * 操作类型
   */
  operationType?: string;
  /**
   * 操作类型描述
   */
  operationTypeDesc?: string;
  /**
   * 操作人
   */
  operator?: string;
  /**
   * 备注
   */
  remark?: string;
}
