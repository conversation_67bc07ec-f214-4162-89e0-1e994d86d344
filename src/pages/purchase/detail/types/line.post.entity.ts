import { RecordRoList } from './ro.list.entity';

export interface LinePostEntity {
  /**
   * 车型备注
   */
  adaptModel?: string;
  /**
   * 是否有售后记录0-无记录>0有记录
   */
  afterRecordNum?: number;
  /**
   * 商品行金额
   */
  amount?: number;
  /**
   * 品牌id
   */
  brandId?: string;
  /**
   * 品牌name
   */
  brandName?: string;
  /**
   * 品牌件号
   */
  brandPartNo?: string;
  /**
   * 品牌件号,前端下拉展示
   */
  brandPartNoList?: string[];
  /**
   * 品类id
   */
  categoryId?: string;
  /**
   * 品类name
   */
  categoryName?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 采购明细id
   */
  id?: string;
  /**
   * 本地库存
   */
  inventoryNum?: number;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 库位
   */
  locationCode?: string;
  /**
   * 库存下限
   */
  lowerLimit?: number;
  /**
   * 数量
   */
  num?: number;
  /**
   * oe号
   */
  oe?: string;
  /**
   * oe号,前端下拉展示
   */
  oeList?: string[];
  /**
   * 采购订单号
   */
  orderNo?: string;
  /**
   * 产地
   */
  originRegionName?: string;
  /**
   * 商品实付单价
   */
  price?: number;
  /**
   * 入库数量
   */
  receivedQuantity?: number;
  /**
   * 售后记录
   */
  recordRoList?: RecordRoList[];
  /**
   * 商品id
   */
  skuId?: string;
  /**
   * 商品名称
   */
  skuName?: string;
  /**
   * 商品备注
   */
  skuRemark?: string;
  /**
   * 规格
   */
  spec?: string;
  /**
   * 单位
   */
  unit?: string;
  /**
   * 修改人
   */
  updatePerson?: string;
  /**
   * 修改时间
   */
  updateTime?: string;
  /**
   * 库存上限
   */
  upperLimit?: number;
}
