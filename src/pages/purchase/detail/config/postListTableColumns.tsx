import ColumnRender from '@/components/ColumnRender';
import type { ProColumns } from '@ant-design/pro-components';
import type { IntlShape } from 'react-intl';
import type { LinePostEntity } from '../types/line.post.entity';
import type { PurchasePostEntity } from '../types/purchase.post.entity';

export interface PostListTableColumnsProps {
  recordData?: PurchasePostEntity;
  intl?: IntlShape;
}

export const PostListTableColumns = (props: PostListTableColumnsProps) =>
  [
    {
      title: props.intl?.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
    },
    {
      title: props.intl?.formatMessage({ id: 'purchase.detail.columns.itemName' }),
      dataIndex: 'skuName',
      key: 'skuName',
      search: false,
      width: 120,
      ellipsis: true,
    },
    {
      title: props.intl?.formatMessage({ id: 'purchase.detail.columns.itemCode' }),
      dataIndex: 'itemSn',
      key: 'itemSn',
      search: false,
      width: 100,
    },
    {
      title: props.intl?.formatMessage({ id: 'purchase.detail.columns.oe' }),
      dataIndex: 'oe',
      key: 'oe',
      search: false,
      ellipsis: true,
      render: (text, entity) => {
        return ColumnRender.ArrayColumnRender(entity.oeList as string[]);
      },
      width: 140,
    },
    {
      title: props.intl?.formatMessage({ id: 'purchase.detail.columns.brandPartNo' }),
      dataIndex: 'brandPartNo',
      key: 'brandPartNo',
      search: false,
      ellipsis: true,
      render: (text, entity) => {
        return ColumnRender.ArrayColumnRender(entity.brandPartNoList as string[]);
      },
      width: 100,
    },
    {
      title: props.intl?.formatMessage({ id: 'purchase.detail.columns.brand' }),
      dataIndex: 'brandName',
      key: 'brandName',
      search: false,
      width: 100,
    },
    {
      title: props.intl?.formatMessage({ id: 'purchase.detail.columns.category' }),
      dataIndex: 'categoryName',
      key: 'categoryName',
      search: false,
      ellipsis: true,
      width: 100,
    },
    {
      title: props.intl?.formatMessage({ id: 'purchase.detail.columns.unit' }),
      dataIndex: 'unit',
      key: 'unit',
      search: false,
      width: 50,
    },
    {
      title: props.intl?.formatMessage({ id: 'purchase.detail.columns.purchasePrice' }),
      dataIndex: 'price',
      key: 'price',
      search: false,
      width: 60,
    },
    {
      title: props.intl?.formatMessage({ id: 'purchase.detail.columns.purchaseQuantity' }),
      dataIndex: 'num',
      key: 'num',
      search: false,
      width: 60,
    },
    {
      title: props.intl?.formatMessage({ id: 'purchase.detail.columns.subtotal' }),
      dataIndex: 'amount',
      key: 'amount',
      search: false,
      width: 80,
    },
  ] as ProColumns<LinePostEntity>[];
