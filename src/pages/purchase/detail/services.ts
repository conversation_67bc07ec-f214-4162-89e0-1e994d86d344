import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { ResponseDataType } from '@/types/ResponseDataType';
import { request } from '@/utils/request';
import { CapitalEntity } from '../capitalFlow/types/CapitalEntity';
import { OperationEntity } from '../operationDetail/type/operation.entity';
import { AuditPostRequest } from './types/audit.post.request';
import { LinePostEntity } from './types/line.post.entity';
import { LogisticsInfoEntity } from './types/logistics.info.entity';
import { PurchasePostEntity } from './types/purchase.post.entity';

/**
 * 列表查询
 * @param params
 * @returns
 */
export const queryPurchaseOrderDetailPost = async (params: { id?: string }) => {
  return request<PurchasePostEntity>(
    `/ipmspurchase/purchase/PurchaseOrderFacade/queryPurchaseOrderDetail`,
    {
      data: params,
    },
  );
};
/**
 * 采购商品列表
 * @param params
 * @returns
 */
export const queryPurchaseLinePagePost = async (
  params: { orderNo?: string } & PageRequestParamsType,
) => {
  return request<PageResponseDataType<LinePostEntity>>(
    `/ipmspurchase/purchase/PurchaseOrderLineFacade/queryPurchaseLinePage`,
    {
      data: params,
    },
  );
};
/**
 * 查询一体系接口展示物流信息
 * @param params
 */
export const queryLogisticsInfoPost = async (params: {
  deliveryNo: string;
  subOrderNo: string;
}) => {
  return request<LogisticsInfoEntity[]>(
    `/ipmspurchase/purchase/PurchaseOrderFacade/getLogisticsInfo`,
    {
      data: params,
    },
  );
};
/**
 * 发起售后
 * @param params etcSkuId 订单商品id subOrderNo 一体系子订单号
 * @returns boolean
 */
export const submitAfterSalesPost = async (params: { etcSkuId?: string; subOrderNo?: string }) => {
  return request<boolean>(`/ipmspurchase/refundsupply/PurchaseReturnOrderFacade/submitAfterSales`, {
    data: params,
  });
};

/**
 * 体系售后详情免登录URL 点击查看
 * @param params
 * @returns
 */
export const queryEtcAfterDetailPagePost = async (params: {
  afterSalesNo?: string;
  etcSkuId?: string;
}) => {
  return request<string>(`/ipmspurchase/refundsupply/ReturnCommonFacade/queryEtcAfterDetailPage`, {
    data: params,
  });
};
/**
 * 发起售后 获取一体系url
 * @param params
 * @returns
 */
export const queryEtcSubmitAfterPagePost = async (params: {
  etcNo?: string;
  etcSkuId?: string;
}) => {
  return request<string>(`/ipmspurchase/refundsupply/ReturnCommonFacade/queryEtcSubmitAfterPage`, {
    data: params,
  });
};
/**
 * 作废
 * @param params
 * @returns
 */
export const closePurchasePost = async (params: { orderNo?: string }) => {
  return request<string>(`/ipmspurchase/purchase/ExternalPurchaseFacade/closePurchase`, {
    data: params,
  });
};
/**
 * 撤回
 */
export const resetPurchasePost = async (params: { orderNo?: string }) => {
  return request<string>(`/ipmspurchase/purchase/ExternalPurchaseFacade/resetPurchase`, {
    data: params,
  });
};

/**
 * 修改入库仓库

 * @param params
 * @returns
 */
export const changePurchaseWarehousePost = async (params: {
  orderNo?: string;
  receiveWarehouseId?: string;
}) => {
  return request<string>(`/ipmspurchase/purchase/PurchaseOrderFacade/changePurchaseWarehouse`, {
    data: params,
  });
};

/**
 * 通过
 */
export const approvePurchasePost = async (params: Partial<AuditPostRequest>) => {
  return request<string>(`/ipmspurchase/purchase/ExternalPurchaseFacade/approvePurchase`, {
    data: params,
  });
};

/**
 * 拒绝
 */
export const rejectPurchasePost = async (params: Partial<AuditPostRequest>) => {
  return request<string>(`/ipmspurchase/purchase/ExternalPurchaseFacade/rejectPurchase`, {
    data: params,
  });
};
/**
 * 操作记录
 */
export const purchaseQueryLogListPost = async (params: { operationNo: string }) => {
  return request<ResponseDataType<OperationEntity[]>>(
    `/ipmspurchase/purchase/PurchaseCommonFacade/queryLogList`,
    {
      origin: true,
      data: params,
    },
  );
};
/**
 * 查询资金流水
 * @param params
 * @returns
 */
export const queryCapitalFlowPost = async (params: { bizNo: string }) => {
  return request<CapitalEntity[]>(`/ipmsaccount/queryCapitalFlow`, {
    data: params,
  });
};
