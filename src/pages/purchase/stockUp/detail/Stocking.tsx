import LeftTitle from '@/components/LeftTitle';
import type { PriceInfoDrawerProps } from '@/components/PriceInfoDrawer';
import PriceInfoDrawer from '@/components/PriceInfoDrawer';
import AuthButton from '@/components/common/AuthButton';
import GoodsCreateDrawerForm from '@/pages/goods/list/components/GoodsCreateDrawerForm';
import type { GoodsCreateDrawerFormType } from '@/pages/goods/list/types/GoodsCreateDrawerFormType';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type {
    ActionType,
    EditableFormInstance,
    ProDescriptionsActionType,
    ProFormInstance,
} from '@ant-design/pro-components';
import {
    EditableProTable,
    PageContainer,
    ProCard,
    ProDescriptions,
    ProForm,
    ProFormSelect,
} from '@ant-design/pro-components';
import { Flex, Space, Tag, message } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import _, { defaultTo, isEmpty } from 'lodash';
import { useRef, useState } from 'react';
import { useSearchParams } from 'umi';
import { submitPurchaseCart } from '../../platform/services';
import type {
    SkuInfoList,
    SubmitPurchaseCartRequest,
} from '../../platform/types/submit.purchase.cart.request';
import { YesNoStatus } from '../../supplier/operation/types/YesNo';
import { StockUpStatusOptions } from '../list/types/StockUpStatus';
import { PostListTableColumns } from './config/postListTableColumns';
import { queryPurchaseSuggestionDetail, queryPurchaseSuggestionListPage } from './services';
import type {
    DetailResponseEntity,
    SuggestionItemTableEntity,
} from './types/detail.response.entity';

const Stocking = () => {
    const [searchParams] = useSearchParams();
    const suggestionNo = searchParams.get('suggestionNo');
    const [form] = useForm();
    const actionRef = useRef<ProDescriptionsActionType>();
    const tableActionRef = useRef<ActionType>();
    const formRef = useRef<ProFormInstance>();
    const tableFormRef = useRef<EditableFormInstance>();
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);
    const [suggestionDetail, setSuggestionDetail] = useState<DetailResponseEntity>({});
    const [dataSourceCache, setDataSourceCache] = useState<SuggestionItemTableEntity[]>([]);
    const warehouseId = ProForm.useWatch('warehouseId', formRef.current ?? {});
    // 显示价格信息
    const [priceDrawer, setPriceDrawer] = useState<PriceInfoDrawerProps>({
        visible: false,
    });
    // 商品总数
    const [totalAmount, setTotalAmount] = useState<number>();
    // 总金额
    const [totalPrice, setTotalPrice] = useState<number>();
    // 新增商品
    const [createModalProps, setCreateModalProps] = useState<GoodsCreateDrawerFormType>({
        visible: false,
        recordId: 0,
        readOnly: false,
        title: `新增商品`,
        onCancel: () => null,
    });

    /**
     * 查看价格信息面板
     */
    const handleViewPriceInfo = (data: PriceInfoDrawerProps) => {
        setPriceDrawer({
            ...data,
            visible: true,
        });
    };

    /**
     * 添加为门店商品
     */
    const handleAgainItem = async (id: string) => { };

    /**
     * 更新列表数据
     */
    const updateItem = async (item: any) => {
        dataSourceCache.forEach((dataItem) => {
            if (dataItem.id == item.id) {
                dataItem.num = item.num;
            }
        });
        setDataSourceCache([...dataSourceCache]);
        updateTotal(selectedRowKeys);
    };

    /**
     * 采购数量更新后
     */
    const handleUpdateItem = async (item: SubmitPurchaseCartRequest) => {
        updateItem(item);
    };

    /**
     * 更新总金额，总数量
     */
    const updateTotal = async (selectedKeys: React.Key[]) => {
        let price: number = 0,
            amount: number = 0;
        dataSourceCache.forEach((m) => {
            if (selectedKeys.includes(m.id)) {
                price = _.round(_.add(price, _.multiply(m.num!, m.price!)), 2);
                amount = _.round(_.add(amount, m.num!), 0);
            }
        });
        setTotalPrice(price);
        setTotalAmount(amount);
    };

    /**
     * 立即补货
     */
    const handleSubmit = async () => {
        if (selectedRowKeys.length === 0) {
            message.error('请选择商品');
            return;
        }
        const skuInfoList: SkuInfoList[] = dataSourceCache.filter((item) =>
            selectedRowKeys.includes(item.id),
        );
        const hasZeroValue = skuInfoList.some((obj) => !obj.num || obj.num === 0);
        if (hasZeroValue) {
            message.warning('请填写数量');
            return;
        }
        submitPurchaseCart({
            skuInfoList: skuInfoList.map((item) => ({ etcNo: item.etcNo, num: item.num })),
        }).then((result) => {

            switch (result.code) {
                case 0:
                    message.success('提交成功');
                    break;
            }
        });
    };

    /**
     * 云端商品添加为门店商品事件
     * @param item
     */
    const handleAddToStore = (item: SuggestionItemTableEntity) => {
        console.log('handleAddToStore', item);
        setCreateModalProps((preModalProps) => ({
            ...preModalProps,
            visible: true,
            readOnly: false,
            cloudOuterParams: {
                skuId: item.skuId!,
                brandId: item?.brandId,
                oeNos: item?.oe ? item?.oe.join(',') : undefined,
                unitId: item?.unitId,
                categoryId: item?.categoryId,
                brandPartNos: item?.brandPartNoList ? item?.brandPartNoList.join(',') : undefined,
                itemName: item.skuName,
            },
            title: '商品添加',
        }));
    };

    /**
     * 关闭【新增商品】对话框
     */
    const hideCreateModal = () => {
        setCreateModalProps((preModalProps) => ({
            ...preModalProps,
            visible: false,
            recordId: 0,
            readOnly: false,
        }));
        setTimeout(() => {
            actionRef.current?.reload();
            tableActionRef.current?.reload();
        }, 1500);
    };

    return (
        <PageContainer>
            <ProCard>
                <ProDescriptions
                    actionRef={actionRef}
                    title={
                        <Space>
                            <span>{suggestionNo}</span>
                            <Tag color={StockUpStatusOptions[suggestionDetail?.suggestionStatus!]?.status}>
                                {StockUpStatusOptions[suggestionDetail?.suggestionStatus!]?.text}
                            </Tag>
                        </Space>
                    }
                    request={async () => {
                        if (!isEmpty(suggestionNo)) {
                            const data = await queryPurchaseSuggestionDetail(suggestionNo);
                            if (data) {
                                setSuggestionDetail(data);
                            }
                            return { data, success: true };
                        }
                        return Promise.resolve({
                            success: false,
                            data: {},
                        });
                    }}
                    column={3}
                >
                    <ProDescriptions.Item dataIndex="suggestionName" label="补货建议名称" />
                    <ProDescriptions.Item dataIndex="createTime" label="创建时间" />
                    <ProDescriptions.Item dataIndex="updateTime" label="更新时间" />
                </ProDescriptions>
            </ProCard>
            <ProCard
                className="mt-4"
                bodyStyle={{ padding: 0 }}
                actions={
                    <div className="flex p-6 justify-between ">
                        <div className="flex text-[16px] text-black/[0.85] font-semibold items-center ">
                            <div>
                                商品总数：<span>{defaultTo(totalAmount, 0)}</span>
                            </div>
                            <div className="flex items-center  pl-[40px]">
                                采购总金额：
                                <span className="text-[24px] font-medium text-[#F83431]">
                                    ￥{defaultTo(totalPrice, 0).toFixed(2)}
                                </span>
                            </div>
                        </div>
                        <AuthButton type="primary" key="submit" authority="stockUpAdd" onClick={handleSubmit}>
                            立即补货
                        </AuthButton>
                    </div>
                }
            >
                <EditableProTable<SuggestionItemTableEntity>
                    rowKey="id"
                    options={false}
                    search={false}
                    scroll={{ x: '1300' }}
                    actionRef={tableActionRef}
                    editableFormRef={tableFormRef}
                    recordCreatorProps={false}
                    pagination={{
                        showQuickJumper: true,
                        defaultPageSize: 10,
                        showSizeChanger: true,
                    }}
                    title={() => (
                        <Flex justify="space-between" align="flex-end">
                            <LeftTitle title="补货建议详情" />
                            <ProForm layout="inline" submitter={false} formRef={formRef}>
                                <ProFormSelect
                                    name="warehouseId"
                                    label="仓库"
                                    rules={[{ required: true }]}
                                    fieldProps={{
                                        fieldNames: { label: 'warehouseName', value: 'warehouseId' },
                                        allowClear: false,
                                    }}
                                    request={() => {
                                        return warehouseList({ state: YesNoStatus.YES }).then((s) => {
                                            if (s?.warehouseStoreRelationRoList) {
                                                let defaultItem;
                                                defaultItem = s?.warehouseStoreRelationRoList?.find(
                                                    (item) => item.isDefault,
                                                );
                                                if (!defaultItem) {
                                                    defaultItem = s?.warehouseStoreRelationRoList?.[0];
                                                }
                                                formRef.current?.setFieldValue('warehouseId', defaultItem.warehouseId);
                                            }
                                            return s.warehouseStoreRelationRoList ?? [];
                                        });
                                    }}
                                    width="sm"
                                />
                            </ProForm>
                        </Flex>
                    )}
                    params={{
                        suggestionNo,
                        warehouseId,
                    }}
                    request={async (params) => {
                        if (params?.suggestionNo && params?.warehouseId) {
                            const result = await queryPurchaseSuggestionListPage(params);
                            const dataList = result?.data.map((item) => ({
                                ...item,
                                num: item.suggestionNum,
                            }));
                            setDataSourceCache(dataList);
                            setEditableRowKeys(result?.data.map((s) => s.id!));
                            setSelectedRowKeys([]);
                            return { data: result?.data, success: true, total: result?.total };
                        } else {
                            return { data: [], success: true, total: 0 };
                        }
                    }}
                    rowSelection={{
                        selectedRowKeys: selectedRowKeys,
                        preserveSelectedRowKeys: true,
                        onChange: (selectedKeys: React.Key[]) => {
                            setSelectedRowKeys(selectedKeys);
                            updateTotal(selectedKeys);
                        },
                    }}
                    editable={{
                        type: 'multiple',
                        editableKeys,
                        onChange: setEditableRowKeys,
                    }}
                    tableAlertRender={false}
                    value={dataSourceCache}
                    columns={PostListTableColumns({
                        handleAgainItem,
                        handleUpdateItem,
                        handleViewPriceInfo,
                        handleAddToStore,
                    })}
                />
            </ProCard>
            <PriceInfoDrawer {...priceDrawer} onClose={() => setPriceDrawer({})} />
            <GoodsCreateDrawerForm {...createModalProps} onCancel={hideCreateModal} />
        </PageContainer>
    );
};

export default withKeepAlive(Stocking);
