export interface GeneratePurchaseOrderEntity {
    /**
     * 生成采购单明细数据
     */
    lineCmdList?: LineCmdList[];
    /**
     * 收货仓库id
     */
    receiveWarehouseId?: string;
    /**
     * 收货仓库Name
     */
    receiveWarehouseName?: string;
    /**
     * 下单门店id
     */
    storeId?: string;
    /**
     * 下单门店
     */
    storeName?: string;
    /**
     * 备货建议单号
     */
    suggestionNo?: string;
}

export interface LineCmdList {
    /**
     * 行id
     */
    id?: string;
    /**
     * 采购商品编码
     */
    itemSn?: string;
    /**
     * 采购数量
     */
    purchaseNum?: number;
    /**
     * 采购单价
     */
    purchasePrice?: number;
    /**
     * 供应商id
     */
    supplierId?: string;
    /**
     * 供应商name
     */
    supplierName?: string;
}



export interface GeneratePurchaseResultEntity {
    /**
     * 生成失败的采购单信息
     */
    failRo?: FailRo;
    /**
     * 生成成功的采购单信息
     */
    successRo?: SuccessRo;
}

/**
 * 生成失败的采购单信息
 */
export interface FailRo {
    /**
     * 失败信息
     */
    errMsg?: string;
}

/**
 * 生成成功的采购单信息
 */
export interface SuccessRo {
    /**
     * 生成成功的采购信息
     */
    purchaseInfoList?: PurchaseInfoList[];
    /**
     * 生成成功的采购单数量
     */
    successCount?: number;
}

export interface PurchaseInfoList {
    /**
     * 采购订单状态;draft-草稿、auditing-审核中、confirm已确认、to_arrival待收货、received已收货、complete已完成、close已关闭
     */
    orderStatus?: string;
    /**
     * 采购单号
     */
    purchaseNo?: string;
}
