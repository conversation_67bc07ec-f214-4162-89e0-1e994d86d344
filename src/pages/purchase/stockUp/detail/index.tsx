import {
  PageContainer
} from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { useEffect } from 'react';
import { useSearchParams } from 'umi';
import Replenish from './Replenish';
import { queryPurchaseSuggestionDetail } from './services';
import Stocking from './Stocking';
import { SuggestionType } from './types/detail.response.entity';

export default () => {
  const [searchParams] = useSearchParams();
  const suggestionNo = searchParams.get('suggestionNo');


  const { data: suggestionDetail, loading, run } = useRequest(() => queryPurchaseSuggestionDetail(suggestionNo!));


  useEffect(() => {
    if (suggestionNo) {
      run()
    }
  }, [suggestionNo]);

  return (
    <PageContainer loading={loading}>
      {
        suggestionDetail?.suggestionType === SuggestionType.Stocking && (
          <Stocking detail={suggestionDetail} />
        )
      }
      {
        suggestionDetail?.suggestionType === SuggestionType.Replenish && (
          <Replenish detail={suggestionDetail} />
        )
      }
    </PageContainer>
  );
};
