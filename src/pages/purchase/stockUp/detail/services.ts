import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import type { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { DetailRequestEntity } from './types/detail.request.entity';
import type {
  DetailResponseEntity,
  ReplenishListItemEntity,
  SuggestionItemTableEntity,
} from './types/detail.response.entity';
import { GeneratePurchaseOrderEntity, GeneratePurchaseResultEntity } from './types/GeneratePurchase.entity';

/**
 * 补货建议详情查询
 * @param params
 * @returns
 */
export const queryPurchaseSuggestionDetail = async (suggestionNo: string) => {
  return request<DetailResponseEntity>(
    `/ipmspurchase/purchase/PurchaseSuggestionFacade/queryPurchaseSuggestionDetail`,
    {
      data: { suggestionNo },
    },
  );
};
/**
 * 补货建议明细数据
 * @param params
 * @returns
 */
export const queryPurchaseSuggestionListPage = async (
  params: Partial<DetailRequestEntity> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<SuggestionItemTableEntity>>(
    `/ipmspurchase/purchase/PurchaseSuggestionFacade/queryPurchaseSuggestionListPage`,
    {
      data: params,
    },
  );
};


/**
 * 查询补货单列表
 */
export const queryPurchaseReplenishListPage = async (params: PageRequestParamsType) => {
  return request<PageResponseDataType<ReplenishListItemEntity>>(`/ipmspurchase/purchase/PurchaseReplenishFacade/queryPurchaseReplenishListPage`, {
    data: params,
  });
};

/**
 * 生成采购单
 */
export const generatePurchaseOrder = async (params: GeneratePurchaseOrderEntity): Promise<GeneratePurchaseResultEntity> => {
  return request(`/ipmspurchase/purchase/PurchaseReplenishFacade/generatePurchaseOrder`, {
    data: params,
  });
};