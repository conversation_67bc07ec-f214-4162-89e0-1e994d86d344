import FunProTable from '@/components/common/FunProTable';
import type { CommonModelForm } from '@/types/CommonModelForm';
import { PageContainer } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-table/lib';
import { history, useIntl } from '@umijs/max';
import { useRef, useState } from 'react';
import { PostListTableColumns } from './config/postListTableColumns';
import { addPurchaseReplenish, queryPostList } from './services';

import AuthButton from '@/components/common/AuthButton';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { DownOutlined } from '@ant-design/icons';
import { useSetState } from 'ahooks';
import { Dropdown, message, Space, Spin } from 'antd';
import { useActivate } from 'react-activation';
import { queryPurchaseSuggestionDetail } from '../detail/services';
import { SuggestionStatus } from '../detail/types/detail.response.entity';
import StockByInventoryModal from './components/StockByInventoryModal';
import StockBySalesModal from './components/StockBySalesModal';
import type { PostEntity } from './types/post.entity';

const pollingStockUpStatus = (suggestionNo: string, intl: any) => {
  // 每秒轮询补货状态，直到补货状态为有效或3s结束
  let currentTime = 0;
  return new Promise((resolve, reject) => {
    const timer = setInterval(() => {
      currentTime++;
      queryPurchaseSuggestionDetail(suggestionNo).then((detailRes) => {
        if (detailRes?.suggestionStatus === SuggestionStatus.EFFECTIVE) {
          history.push(`/purchase/stockUp/detail?suggestionNo=${suggestionNo}`);
          clearInterval(timer);
          resolve(true);
        }
        if (detailRes?.suggestionStatus === SuggestionStatus.GENERATING && currentTime === 3) {
          message.info(intl.formatMessage({ id: 'purchase.stockUp.list.message.generating' }));
          clearInterval(timer);
          resolve(false);
        }
      });
    }, 1000);
  });
};


const StockUpList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState(false);

  const [stockUpModalProps, setStockUpModalProps] = useState<
    CommonModelForm<string, PostEntity>
  >({
    visible: false,
    recordId: '0',
    readOnly: false,
    title: '',
  });

  const [visible, setVisible] = useSetState({
    inventory: false,
    sales: false
  })



  useActivate(() => {
    actionRef.current?.reload();
  });

  const handleAgainItem = async (suggestionNo: string) => {
    history.push(`/purchase/stockUp/detail?suggestionNo=${suggestionNo}`);
  };

  const menuProps = {
    items: [
      {
        label: intl.formatMessage({ id: 'purchase.stockUp.list.menu.stockByInventory' }),
        key: 'inventory'
      },
      {
        label: intl.formatMessage({ id: 'purchase.stockUp.list.menu.stockBySales' }),
        key: 'sales'
      }
    ],
    onClick: ({ key }: { key: string }) => {
      setVisible({
        [key]: true
      })
    },
  };

  const handleInventoryModalOk = (values: any) => {
    console.log('按库存补货表单数据:', values);
    // const hide = message.loading('正在生成补货建议...', 0);
    setLoading(true);
    addPurchaseReplenish({
      ruleType: 'BY_STOCK',
      stockCmd: values
    }).then((suggestionNo) => {
      if (suggestionNo) {
        pollingStockUpStatus(suggestionNo, intl).then((res) => {
          setLoading(false);
        });
      } else {
        setLoading(false);
      }
      setVisible({
        inventory: false
      });
    }).finally(() => {
      actionRef.current?.reload();
    });
  };

  const handleSalesModalOk = (values: any) => {
    console.log('按销售补货表单数据:', values);
    setLoading(true);

    addPurchaseReplenish({
      ruleType: 'BY_SALE',
      saleCmd: values
    }).then((suggestionNo) => {
      if (suggestionNo) {
        pollingStockUpStatus(suggestionNo!, intl).then((res) => {
          setLoading(false);
        });
      } else {
        setLoading(false);
      }
      setVisible({
        sales: false
      });
    }).finally(() => {
      actionRef.current?.reload();
    });
  };

  return (
    <PageContainer>
      <Spin spinning={loading} tip={intl.formatMessage({ id: 'purchase.stockUp.list.loading.generating' })} fullscreen />
      <FunProTable<PostEntity, any>
        rowKey="suggestionNo"
        search={{ labelWidth: 100, defaultCollapsed: false }}
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        requestPage={queryPostList}
        columns={PostListTableColumns({
          intl,
          handleAgainItem
        })}
        headerTitle={
          <Dropdown menu={menuProps}>
            <AuthButton type='primary' authority='stockUpAddPurchaseReplenish'>
              <Space>
                {intl.formatMessage({ id: 'purchase.stockUp.list.button.addStockUp' })}
                <DownOutlined />
              </Space>
            </AuthButton>
          </Dropdown>
        }
      />
      <StockByInventoryModal
        open={visible.inventory}
        onCancel={() => setVisible({ inventory: false })}
        onOk={handleInventoryModalOk}
      />

      <StockBySalesModal
        open={visible.sales}
        onCancel={() => setVisible({ sales: false })}
        onOk={handleSalesModalOk}
      />
    </PageContainer>
  );
};
export default withKeepAlive(StockUpList);
