import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import type { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { AddPurchaseReplenish, PostEntity } from './types/post.entity';

export const queryPostList = async (params: Partial<PostEntity> & PageRequestParamsType) => {
  return request<PageResponseDataType<PostEntity>>(
    `/ipmspurchase/purchase/PurchaseSuggestionFacade/queryPurchaseSuggestionPage`,
    {
      data: params,
    },
  );
};


export const addPurchaseReplenish = async (params: AddPurchaseReplenish) => {
  return request<PageResponseDataType<PostEntity>>(
    `/ipmspurchase/purchase/PurchaseReplenishFacade/addPurchaseReplenish`,
    {
      data: params,
    },
  );
};
