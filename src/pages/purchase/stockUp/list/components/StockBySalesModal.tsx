import { OrderStatus } from '@/pages/sales/order/list/types/OrderStatus';
import { queryStoreByAccount } from '@/pages/system/user/services';
import { ModalForm, ProFormCheckbox, ProFormDateRangePicker, ProFormInstance, ProFormSelect } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Alert } from 'antd';
import dayjs from 'dayjs';
import { FC, useRef } from 'react';

interface StockBySalesModalProps {
    open: boolean;
    onCancel: () => void;
    onOk: (values: any) => void;
}

const StockBySalesModal: FC<StockBySalesModalProps> = ({ open, onCancel, onOk }) => {
    const intl = useIntl();
    const formRef = useRef<ProFormInstance>();

    return (
        <ModalForm
            title={intl.formatMessage({ id: 'purchase.stockUp.sales.modal.title' })}
            open={open}
            width={520}
            layout="horizontal"
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 16 }}
            onOpenChange={(visible) => !visible && onCancel()}
            onFinish={async (values) => {
                const { timeRange, ...rest } = values;
                await onOk({
                    ...rest,
                    startTime: timeRange?.[0],
                    endTime: timeRange?.[1],
                });
                return true;
            }}
            modalProps={{
                destroyOnClose: true,
                maskClosable: false,
            }}
            initialValues={{
                statusIds: [OrderStatus.WAIT_TO_HANDLE, OrderStatus.WAIT_TO_OUTBOUND, OrderStatus.OUTBOUND_FINISH, OrderStatus.TRADE_SUCCESS],
                timeRange: [dayjs().subtract(6, 'day').startOf('day'), dayjs().endOf('day')]
            }}
            formRef={formRef}
        >
            <Alert
                className="mb-4"
                message={intl.formatMessage({ id: 'purchase.stockUp.sales.modal.alert' })}
                type="error"
            />

            <ProFormSelect
                name="storeIds"
                label={intl.formatMessage({ id: 'purchase.stockUp.sales.form.store' })}
                rules={[{ required: true }]}
                placeholder={intl.formatMessage({ id: 'purchase.stockUp.sales.form.store.placeholder' })}
                fieldProps={{
                    fieldNames: { label: 'name', value: 'id' },
                    mode: 'multiple'
                }}
                request={async () => {
                    const storeList = await queryStoreByAccount({ status: 1 });
                    // 如果只有一个门店，则默认选中
                    if (storeList?.length === 1) {
                        const form = formRef.current;
                        form?.setFieldValue('storeIds', [storeList[0].id]);
                    }
                    return storeList;
                }}
            />

            <ProFormDateRangePicker
                name="timeRange"
                label={intl.formatMessage({ id: 'purchase.stockUp.sales.form.orderTime' })}
                rules={[{ required: true }]}
                fieldProps={{
                    style: { width: '100%' },
                    disabledDate: (current) => current && current.isAfter(dayjs(), 'day'),
                    presets: [
                        {
                            label: intl.formatMessage({ id: 'purchase.stockUp.sales.form.preset.today' }),
                            value: [dayjs().startOf('day'), dayjs().endOf('day')],
                        },
                        {
                            label: intl.formatMessage({ id: 'purchase.stockUp.sales.form.preset.recent3Days' }),
                            value: [dayjs().subtract(2, 'day').startOf('day'), dayjs().endOf('day')],
                        },
                        {
                            label: intl.formatMessage({ id: 'purchase.stockUp.sales.form.preset.recent7Days' }),
                            value: [dayjs().subtract(6, 'day').startOf('day'), dayjs().endOf('day')],
                        },
                        {
                            label: intl.formatMessage({ id: 'purchase.stockUp.sales.form.preset.recent30Days' }),
                            value: [dayjs().subtract(29, 'day').startOf('day'), dayjs().endOf('day')],
                        },
                        {
                            label: intl.formatMessage({ id: 'purchase.stockUp.sales.form.preset.thisMonth' }),
                            value: [dayjs().startOf('month'), dayjs()],
                        },
                    ],
                }}
            />

            <ProFormCheckbox.Group
                name="statusIds"
                label={intl.formatMessage({ id: 'purchase.stockUp.sales.form.salesStatus' })}
                options={[
                    { label: intl.formatMessage({ id: 'purchase.stockUp.sales.status.draft' }), value: OrderStatus.WAIT_TO_HANDLE },
                    { label: intl.formatMessage({ id: 'purchase.stockUp.sales.status.waitToOutbound' }), value: OrderStatus.WAIT_TO_OUTBOUND },
                    { label: intl.formatMessage({ id: 'purchase.stockUp.sales.status.outboundFinish' }), value: OrderStatus.OUTBOUND_FINISH },
                    { label: intl.formatMessage({ id: 'purchase.stockUp.sales.status.tradeSuccess' }), value: OrderStatus.TRADE_SUCCESS },
                ]}
            />
        </ModalForm>
    );
};

export default StockBySalesModal;