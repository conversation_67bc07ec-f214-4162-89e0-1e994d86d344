import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { ModalForm, ProFormInstance, ProFormRadio, ProFormSelect } from '@ant-design/pro-components';
import { useIntl, useModel } from '@umijs/max';
import { Alert } from 'antd';
import { FC, useRef } from 'react';


interface StockByInventoryModalProps {
    open: boolean;
    onCancel: () => void;
    onOk: (values: any) => void;
}

const StockByInventoryModal: FC<StockByInventoryModalProps> = ({ open, onCancel, onOk }) => {
    const intl = useIntl();
    const formRef = useRef<ProFormInstance>();
    const { initialState } = useModel('@@initialState');

    return (
        <ModalForm
            title={intl.formatMessage({ id: 'purchase.stockUp.inventory.modal.title' })}
            open={open}
            width={520}
            layout="horizontal"
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 16 }}
            onOpenChange={(visible) => !visible && onCancel()}
            onFinish={onOk}
            modalProps={{
                destroyOnClose: true,
                maskClosable: false,
            }}
            initialValues={{
                ruleSubType: 'UPPER_UP'
            }}
            formRef={formRef}
        >
            <Alert
                className="mb-4"
                message={intl.formatMessage({ id: 'purchase.stockUp.inventory.modal.alert' })}
                type="error"
            />

            <ProFormSelect
                name="warehouseIds"
                label={intl.formatMessage({ id: 'purchase.stockUp.inventory.form.warehouse' })}
                rules={[{ required: true }]}
                placeholder={intl.formatMessage({ id: 'purchase.stockUp.inventory.form.warehouse.placeholder' })}
                fieldProps={{
                    mode: 'multiple',
                    fieldNames: { label: 'warehouseName', value: 'id' }
                }}
                dependencies={['storeId']}
                request={async (params) => {
                    const res = await warehouseList({
                        storeIdList: initialState?.currentUser?.storeIds ?? [],
                        state: YesNoStatus.YES
                    });
                    const _warehouseList = res?.warehouseSimpleRoList ?? [];
                    console.log(_warehouseList);
                    // 如果只有一个仓库，则默认选中
                    if (_warehouseList.length === 1) {
                        const form = formRef.current;
                        form?.setFieldValue('warehouseIds', [_warehouseList[0].id]);
                    }
                    return _warehouseList;
                }}
            />

            <ProFormRadio.Group
                name="ruleSubType"
                label={intl.formatMessage({ id: 'purchase.stockUp.inventory.form.rule' })}
                rules={[{ required: true }]}
                options={[
                    { label: intl.formatMessage({ id: 'purchase.stockUp.inventory.form.rule.upperUp' }), value: 'UPPER_UP' },
                    { label: intl.formatMessage({ id: 'purchase.stockUp.inventory.form.rule.lowUp' }), value: 'LOW_UP' }
                ]}
            />
        </ModalForm>
    );
};

export default StockByInventoryModal;