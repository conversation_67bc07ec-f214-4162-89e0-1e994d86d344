export interface PostEntity {
  suggestionNo: string;
}


export interface AddPurchaseReplenish {
    /**
     * 补货类型：按库存补货BY_STOCK按销售补货BY_SALE
     */
    ruleType?: string;
    saleCmd?:  SaleCmd;
    stockCmd?:  StockCmd;
}

export interface SaleCmd {
    /**
     * 下单结束时间
     */
    endTime?: string;
    /**
     * 下单开始时间
     */
    startTime?: string;
    /**
     * 销售状态ids
     */
    statusIds?: string[];
    /**
     * 销售门店id
     */
    storeIds?: string[];
}

export interface StockCmd {
    /**
     * 补货子类型:低于下限补到上限LOW_UP低于上限补到上限UPPER_UP
     */
    ruleSubType?: string;
    /**
     * 补货仓库id
     */
    warehouseIds?: string[];
}
