import type { PaginationRequest } from '@/types/PaginationRequest';

export interface QueryPostListRequest extends PaginationRequest {
  /**
   * 创建时间结束日期
   */
  createEndTime?: string;
  /**
   * 创建时间开始日期
   */
  createStartTime?: string;
  /**
   * 补货建议名称
   */
  suggestionName?: string;
  /**
   * 补货单号
   */
  suggestionNo?: string;
  /**
   * 补货建议状态0-生效1-失效
   */
  suggestionStatus?: string;
  /**
   * 更新时间结束日期
   */
  updateEndTime?: string;
  /**
   * 更新时间开始日期
   */
  updateStartTime?: string;
}