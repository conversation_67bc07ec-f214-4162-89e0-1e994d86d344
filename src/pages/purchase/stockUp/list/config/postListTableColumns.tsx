import type { ProColumns } from '@ant-design/pro-components';
import { Space } from 'antd';
import { StockUpStatusOptions } from '../types/StockUpStatus';
import type { PostEntity } from '../types/post.entity';

export interface PostListTableColumnsProps {
  intl: any;
  handleAgainItem: (suggestionNo: string) => void;
}

export const PostListTableColumns = (props: PostListTableColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'purchase.stockUp.list.columns.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.stockUp.list.columns.suggestionNo' }),
      dataIndex: 'suggestionNo',
      search: true,
      width: 100,
      order: 10,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.stockUp.list.columns.suggestionName' }),
      dataIndex: 'suggestionName',
      search: true,
      width: 200,
      order: 8,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.stockUp.list.columns.status' }),
      dataIndex: 'suggestionStatus',
      search: true,
      width: 80,
      valueEnum: StockUpStatusOptions,
      order: 6,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.stockUp.list.columns.sumQuantity' }),
      dataIndex: 'sumQuantity',
      search: false,
      width: 80,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.stockUp.list.columns.createTime' }),
      dataIndex: 'createTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            createStartTime: value[0],
            createEndTime: value[1],
          };
        },
      },
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.stockUp.list.columns.createTime' }),
      dataIndex: 'createTime',
      search: false,
      width: 140,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.stockUp.list.columns.updateTime' }),
      dataIndex: 'updateTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            updateStartTime: value[0],
            updateEndTime: value[1],
          };
        },
      },
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.stockUp.list.columns.updateTime' }),
      dataIndex: 'createTime',
      search: false,
      width: 140,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.stockUp.list.columns.operation' }),
      search: false,
      width: 100,
      fixed: 'right',
      render: (text, record: PostEntity) => (
        <Space>
          <a onClick={() => props.handleAgainItem(record.suggestionNo)}>{props.intl.formatMessage({ id: 'purchase.stockUp.list.button.view' })}</a>
        </Space>
      ),
    },
  ] as ProColumns<PostEntity>[];
