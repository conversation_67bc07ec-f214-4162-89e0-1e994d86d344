import ColumnRender from '@/components/ColumnRender';
import type { PurchaseCartEntity } from '@/pages/purchase/platform/types/purchase.cart.entity';
import { requiredProps } from '@/types/validateRules';
import type { ProColumns } from '@ant-design/pro-components';
import { Button, InputNumber } from 'antd';

export interface PurchaseCartTableColumnsProps {
  handleDelete: (ids: string[]) => void;
  handleUpdate: (id: string, num: number) => void;
}

export const purchaseCartTableColumns = (props: PurchaseCartTableColumnsProps) =>
  [
    {
      title: '序号',
      valueType: 'index',
      fixed: 'left',
      width: 40,
      editable: false,
    },
    {
      title: '商品名称',
      dataIndex: 'skuName',
      width: 120,
      editable: false,
    },
    {
      title: 'OE号',
      dataIndex: 'oe',
      width: 140,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: '品牌件号',
      dataIndex: 'brandPartNoList',
      width: 100,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: '品牌',
      dataIndex: 'brandName',
      width: 100,
      editable: false,
    },
    {
      title: '分类',
      dataIndex: 'categoryName',
      width: 100,
      editable: false,
    },
    {
      title: '适用车系',
      dataIndex: 'adaptSeries',
      width: 120,
      editable: false,
    },
    {
      title: '标准适用车型',
      dataIndex: 'adaptCarModel',
      width: 120,
      editable: false,
      ellipsis: true,
    },
    {
      title: '商品备注',
      dataIndex: 'skuRemark',
      width: 120,
      editable: false,
      ellipsis: true,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      width: 50,
      editable: false,
    },
    {
      title: '本地库存',
      dataIndex: 'localStock',
      width: 60,
      editable: false,
    },
    {
      title: '库存下限',
      dataIndex: 'minStock',
      width: 60,
      editable: false,
    },
    {
      title: '库存上限',
      dataIndex: 'maxStock',
      width: 60,
      editable: false,
    },
    {
      title: '一体系库存',
      dataIndex: 'hasInventory',
      width: 80,
      editable: false,
      renderText: (text) => (text === '0' ? '无货' : '有货'),
    },
    {
      title: '最小起订量',
      dataIndex: 'minOrderNum',
      width: 80,
      editable: false,
    },
    {
      title: '最小包装量',
      dataIndex: 'minPackNum',
      width: 80,
      editable: false,
    },
    {
      title: '采购价',
      dataIndex: 'price',
      valueType: 'money',
      width: 80,
      editable: false,
    },
    {
      title: '小计',
      dataIndex: 'amount',
      width: 80,
      editable: false,
      valueType: 'money',
    },
    {
      title: '采购数量',
      dataIndex: 'num',
      width: 100,
      fixed: 'right',
      valueType: 'digit',
      renderFormItem: (item, config) => {
        const step = config?.record?.minPackNum ?? 1;
        return (
          <InputNumber
            min={config.record?.minOrderNum ?? 1}
            max={2000}
            precision={0}
            step={step}
            parser={(value: any) => {
              const parsedValue = parseInt(value, 10);
              if (parsedValue % step !== 0) {
                return Math.round(parsedValue / step) * step;
              }
              return parsedValue;
            }}
            placeholder="请输入"
            onChange={(value) => {
              props.handleUpdate(config.record?.id!, Number(value));
            }}
          />
        );
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: '操作',
      width: 60,
      align: 'center',
      editable: false,
      fixed: 'right',
      render: (text, row) => (
        <Button type={'link'} onClick={() => props.handleDelete([row.id])}>
          删除
        </Button>
      ),
    },
  ] as ProColumns<PurchaseCartEntity, 'text'>[];
