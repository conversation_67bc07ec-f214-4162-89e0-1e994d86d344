export interface SubmitPurchaseCartRequest {
  /**
   * id
   */
  id?: string;
  /**
   * itemSn
   */
  itemSn?: string;
  /**
   * price
   */
  price?: number;
  /**
   * num
   */
  num?: number;
  /**
   * 下单人id
   */
  accountId?: string;
  /**
   * 门店系统账户名称
   */
  accountName?: string;
  /**
   * 门店系统手机号
   */
  accountPhone?: string;
  /**
   * 一体系账户id
   */
  etcAccountId?: string;
  /**
   * 一体系零售商id
   */
  etcMemberId?: string;
  /**
   * 一体系零售商id
   */
  memberId?: string;
  /**
   * sessionId
   */
  sessionId?: string;
  /**
   * 明细
   */
  skuInfoList?: SkuInfoList[];
}

export interface SkuInfoList {
  /**
   * etc号
   */
  etcNo?: string;
  /**
   * 数量
   */
  num?: number;
}
