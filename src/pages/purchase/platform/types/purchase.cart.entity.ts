export interface PurchaseCartEntity {
  /**
   * 标准适用车型
   */
  adaptCarModelList?: string[];
  /**
   * 适用车系
   */
  adaptSeries?: string;
  /**
   * 品牌
   */
  brandName?: string;
  /**
   * 品牌件号
   */
  brandPartNoList?: string[];
  /**
   * 分类
   */
  categoryName?: string;
  /**
   * etc号
   */
  etcNo: string;
  /**
   * 有无活动
   */
  hasActivity?: number;
  /**
   * 一体系库存
   */
  hasInventory?: string;
  /**
   * 购物车明细id
   */
  id: string;
  /**
   * 有无套装
   */
  isSuit?: number;
  /**
   * 本地库存
   */
  localStock?: number;
  /**
   * 库存上限
   */
  maxStock?: string;
  /**
   * 最小起订量
   */
  minOrderNum?: number;
  /**
   * 最小包装数
   */
  minPackNum?: number;
  /**
   * 库存下限
   */
  minStock?: string;
  /**
   * 采购数量
   */
  num?: number;
  /**
   * oe号
   */
  oe?: string[];
  /**
   * 采购单价
   */
  price?: number;
  /**
   * 商品名称
   */
  skuName?: string;
  /**
   * 商品备注
   */
  skuRemark?: string;
  /**
   * 单位
   */
  unit?: string;
}
