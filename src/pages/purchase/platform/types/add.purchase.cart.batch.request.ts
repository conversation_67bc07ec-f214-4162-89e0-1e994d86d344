export interface AddPurchaseCartBatchRequest {
  /**
    * 门店系统账户id
    */
  accountId?: string;
  /**
   * 门店系统账户名称
   */
  accountName?: string;
  /**
   * 门店系统手机号
   */
  accountPhone?: string;
  /**
   * 一体系账户id
   */
  etcAccountId?: string;
  /**
   * 一体系零售商id
   */
  etcMemberId?: string;
  /**
   * 采购购物明细
   */
  lineList?: LineList[];
  /**
   * 门店系统零售商id
   */
  memberId?: string;
  /**
   * sessionId
   */
  sessionId?: string;
}

export interface LineList {
  /**
   * etc号
   */
  etcNo?: string;
  /**
   * 数量
   */
  num?: number;
  /**
   * skuName
   */
  skuName?: string;
}

