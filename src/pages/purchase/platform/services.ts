import type { AddPurchaseCartRequest } from '@/pages/purchase/platform/types/add.purchase.cart.request';
import type { DeletePurchaseCartRequest } from '@/pages/purchase/platform/types/delete.purchase.cart.request';
import type { PurchaseCartEntity } from '@/pages/purchase/platform/types/purchase.cart.entity';
import type { SubmitPurchaseCartRequest } from '@/pages/purchase/platform/types/submit.purchase.cart.request';
import type { UpdatePurchaseCartRequest } from '@/pages/purchase/platform/types/update.purchase.cart.request';
import type { ResponseDataType } from '@/types/ResponseDataType';
import { request } from '@/utils/request';
import { AddPurchaseCartBatchRequest } from './types/add.purchase.cart.batch.request';

/**
 * 查询平台采购购物车列表
 */
export const queryPurchaseCartList = (params: any): Promise<PurchaseCartEntity[]> => {
  return request('/ipmspurchase/purchase/PurchaseCartFacade/queryPurchaseCartList', {
    data: params,
  });
};

/**
 * 加购到购物车
 */
export const addPurchaseCart = (params: AddPurchaseCartRequest): Promise<boolean> => {
  return request('/ipmspurchase/purchase/PurchaseCartFacade/addPurchaseCart', {
    data: params,
  });
};

/**
 * 批量加购到购物车
 */
export const addPurchaseCartBatch = (params: AddPurchaseCartBatchRequest): Promise<boolean> => {
  return request('/ipmspurchase/purchase/PurchaseCartFacade/addPurchaseCartBatch', {
    data: params,
  });
};

/**
 * 批量删除购物车明细
 */
export const deletePurchaseCart = (params: DeletePurchaseCartRequest): Promise<boolean> => {
  return request('/ipmspurchase/purchase/PurchaseCartFacade/deletePurchaseCart', {
    data: params,
  });
};

/**
 * 编辑购物车明细
 */
export const updatePurchaseCart = (params: UpdatePurchaseCartRequest): Promise<boolean> => {
  return request('/ipmspurchase/purchase/PurchaseCartFacade/updatePurchaseCart', {
    data: params,
  });
};

/**
 * 获取批量导入采购购物车模版
 */
export const getImportCartURL = (): Promise<string> => {
  return request('/ipmspurchase/purchase/PurchaseCartFacade/getImportCartURL', {
    data: {},
  });
};

/**
 * 提交采购购物车
 */
export const submitPurchaseCart = (
  params: SubmitPurchaseCartRequest,
): Promise<ResponseDataType<boolean>> => {
  return request('/ipmspurchase/purchase/PurchaseCartFacade/submitPurchaseCart', {
    data: params,
    origin: true,
  });
};

/**
 * 获取一体系购物车免登录URL接口
 */
export const queryEtcCartPage = (): Promise<string> => {
  return request('/ipmspurchase/purchase/PurchaseCommonFacade/queryEtcCartPage', {
    data: {},
  });
};
