import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { exportData } from '@/utils/exportData';
import { importData } from '@/utils/importData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer, ProFormInstance } from '@ant-design/pro-components';
import { ActionType } from '@ant-design/pro-table/lib';
import { Access, history, useAccess, useIntl } from '@umijs/max';
import { App, Button, Space, message } from 'antd';
import { useRef } from 'react';
import { useActivate } from 'react-activation';
import { closePurchasePost, resetPurchasePost } from '../detail/services';
import { PostListTableColumns } from './config/postListTableColumns';
import { copyOrderPost, queryPostList } from './services';
import { PurchasePostEntity } from './types/purchase.post.entity';

const PurchaseList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const { modal } = App.useApp();
  const formRef = useRef<ProFormInstance>();
  const access = useAccess();

  useActivate(() => {
    actionRef.current?.reload();
  });

  const handleAgainItem = async (orderNo: string) => {
    const result = await copyOrderPost({ orderNo });
    if (result) {
      switch (result.code) {
        case 0:
          message.success(intl.formatMessage({ id: 'purchase.list.message.submitSuccess' }));
          actionRef.current?.reload(true);
          break;
      }
    }
    return true;
  };
  const handleUpdateItem = async (orderId: string) => {
    history.push(`/purchase/external?purchaseOrderId=${orderId}`);
  };
  /**
   * 作废
   * @param orderNo
   */
  const handleClosePurchase = async (orderNo: string) => {
    const data = await closePurchasePost({ orderNo });
    actionRef.current?.reload(true);
    return true;
  };
  /**
   * 撤回
   * @param orderNo
   */
  const handleWithdraw = async (orderNo: string) => {
    const data = await resetPurchasePost({ orderNo });
    actionRef.current?.reload(true);
    return true;
  };

  return (
    <PageContainer>
      <FunProTable<PurchasePostEntity, any>
        rowKey="id"
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        formRef={formRef}
        headerTitle={
          <Space>
            <Access accessible={access.hasButtonPerms('addPurchase')}>
              <Button type="primary" key="primary" onClick={() => history.push('/purchase/external')}>
                {intl.formatMessage({ id: 'purchase.list.button.addPurchase' })}
              </Button>
            </Access>
            <AuthButton
              key="export"
              danger
              authority="exportPurchase"
              onClick={() => {
                exportData({
                  systemId: 'ETC_SAAS_SYS',
                  taskDesc: intl.formatMessage({ id: 'purchase.list.export.taskDesc' }),
                  moduleId: 'PURCHASE_ORDER_EXPORT',
                  params: formRef.current?.getFieldsValue(),
                });
              }}
            >
              {intl.formatMessage({ id: 'purchase.list.button.export' })}
            </AuthButton>
            <AuthButton
              key="import"
              danger
              authority="importPurchase"
              onClick={() => {
                importData({
                  systemId: 'ETC_SAAS_SYS',
                  taskDesc: intl.formatMessage({ id: 'purchase.list.import.taskDesc' }),
                  moduleId: 'BATCH_IMPORT_PURCHASE',
                  downloadFileName:
                    'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E9%87%87%E8%B4%AD%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx',
                });
              }}
            >
              {intl.formatMessage({ id: 'purchase.list.button.import' })}
            </AuthButton>
          </Space>
        }
        requestPage={queryPostList}
        columns={PostListTableColumns({
          handleAgainItem,
          handleUpdateItem,
          handleClosePurchase,
          handleWithdraw,
          formRef,
          intl,
        })}
      />
    </PageContainer>
  );
};
export default withKeepAlive(PurchaseList);
