import { OrderSourceStatus } from './OrderSourceStatus';

export interface PurchasePostEntity {
  /**
   * 订单id
   */
  id: string;
  /**
   * 订单号
   */
  orderNo?: string;
  /**
   * 数据来源
   */
  orderSource?: OrderSourceStatus;
  /**
   * 采购状态
   */
  orderStatus?: string;
  /**
   * 付款状态
   */
  payStatus?: string;
  /**
   * 付款方式
   */
  payType?: string;
  /**
   * 采购人
   */
  purchaseUser?: string;
  /**
   * 收货仓库
   */
  receiveWarehouseName?: string;
  /**
   * 发起门店
   */
  storeName?: string;
  /**
   * 采购总金额
   */
  sumAmount?: number;
  /**
   * 采购数量
   */
  sumQuantity?: number;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 供应商name
   */
  supplierName?: string;

  sourceNo?: string;
}
