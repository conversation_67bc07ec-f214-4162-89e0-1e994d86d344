import { FormattedMessage } from "@umijs/max";

export enum OrderStatus {
  DRAFT = 'draft',
  AUDITING = 'auditing',
  CONFIRM = 'confirm',
  TO_ARRIVAL = 'to_arrival',
  RECEIVED = 'received',
  COMPLETE = 'complete',
  CLOSE = 'close',
  REJECT = 'reject',
}

export enum OrderStatusName {
  DRAFT = 'purchase.orderStatus.draft',
  AUDITING = 'purchase.orderStatus.auditing',
  CONFIRM = 'purchase.orderStatus.confirm',
  TO_ARRIVAL = 'purchase.orderStatus.toArrival',
  RECEIVED = 'purchase.orderStatus.received',
  COMPLETE = 'purchase.orderStatus.complete',
  CLOSE = 'purchase.orderStatus.close',
  REJECT = 'purchase.orderStatus.reject',
}

export const orderStatusOptions = {
  [OrderStatus.DRAFT]: { text: <FormattedMessage id={OrderStatusName.DRAFT} />, status: '' },
  [OrderStatus.AUDITING]: { text: <FormattedMessage id={OrderStatusName.AUDITING} />, status: '' },
  [OrderStatus.CONFIRM]: { text: <FormattedMessage id={OrderStatusName.CONFIRM} />, status: '' },
  [OrderStatus.TO_ARRIVAL]: { text: <FormattedMessage id={OrderStatusName.TO_ARRIVAL} />, status: '' },
  [OrderStatus.RECEIVED]: { text: <FormattedMessage id={OrderStatusName.RECEIVED} />, status: '' },
  [OrderStatus.COMPLETE]: { text: <FormattedMessage id={OrderStatusName.COMPLETE} />, status: '' },
  [OrderStatus.CLOSE]: { text: <FormattedMessage id={OrderStatusName.CLOSE} />, status: '' },
  [OrderStatus.REJECT]: { text: <FormattedMessage id={OrderStatusName.REJECT} />, status: '' },
};

export const orderColorOptions = {
  [OrderStatus.DRAFT]: { status: 'error' },
  [OrderStatus.AUDITING]: { status: 'error' },
  [OrderStatus.CONFIRM]: { status: 'error' },
  [OrderStatus.TO_ARRIVAL]: { status: 'error' },
  [OrderStatus.RECEIVED]: { status: 'error' },
  [OrderStatus.COMPLETE]: { status: 'success' },
  [OrderStatus.CLOSE]: { status: 'default' },
  [OrderStatus.REJECT]: { status: 'error' },
};
