import { FormattedMessage } from "@umijs/max";

export enum PayStatus {
  UN_BALANCE = '0',
  PART_BALANCE = '1',
}

export enum PayStatusName {
  UN_BALANCE = 'purchase.list.payStatus.unBalance',
  PART_BALANCE = 'purchase.list.payStatus.partBalance',
}
export const payStatusOptions = {
  [PayStatus.UN_BALANCE]: { text: <FormattedMessage id={PayStatusName.UN_BALANCE} />, status: 'error' },
  [PayStatus.PART_BALANCE]: { text: <FormattedMessage id={PayStatusName.PART_BALANCE} />, status: 'success' },
};
