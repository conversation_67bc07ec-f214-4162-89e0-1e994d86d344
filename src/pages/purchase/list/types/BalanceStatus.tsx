import { FormattedMessage } from "@umijs/max";

export enum BalanceStatus {
  UN_BALANCE = '0',
  PART_BALANCE = '1',
  ALL_BALANCE = '2',
}

export enum BalanceStatusName {
  UN_BALANCE = 'purchase.balanceStatus.unBalance',
  PART_BALANCE = 'purchase.balanceStatus.partBalance',
  ALL_BALANCE = 'purchase.balanceStatus.allBalance',
}
export const balanceStatusOptions = {
  [BalanceStatus.UN_BALANCE]: { text: <FormattedMessage id={BalanceStatusName.UN_BALANCE} />, status: 'error' },
  [BalanceStatus.PART_BALANCE]: { text: <FormattedMessage id={BalanceStatusName.PART_BALANCE} />, status: 'success' },
  [BalanceStatus.ALL_BALANCE]: { text: <FormattedMessage id={BalanceStatusName.ALL_BALANCE} />, status: 'success' },
};
