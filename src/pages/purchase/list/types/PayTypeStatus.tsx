import { FormattedMessage } from "@umijs/max";

export enum PayTypeStatus {
  CASH = '1',
  HANGING_ACCOUNTS = '2',
}

export enum PayTypeStatusName {
  CASH = 'purchase.payType.cash',
  HANGING_ACCOUNTS = 'purchase.payType.hangingAccounts',
}
export const payTypeStatusOptions = {
  [PayTypeStatus.CASH]: { text: <FormattedMessage id={PayTypeStatusName.CASH} />, status: '' },
  // [PayTypeStatus.CASH]: { text: PayTypeStatusName.CASH, status: '' },
  [PayTypeStatus.HANGING_ACCOUNTS]: { text: <FormattedMessage id={PayTypeStatusName.HANGING_ACCOUNTS} />, status: '' },
  // [PayTypeStatus.HANGING_ACCOUNTS]: { text: PayTypeStatusName.HANGING_ACCOUNTS, status: '' },
};
