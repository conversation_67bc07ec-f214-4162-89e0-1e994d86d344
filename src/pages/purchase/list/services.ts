import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { ResponseDataType } from '@/types/ResponseDataType';
import { request } from '@/utils/request';
import { PurchasePostEntity } from './types/purchase.post.entity';

/**
 * 列表查询
 * @param params
 * @returns
 */
export const queryPostList = async (
  params: Partial<PurchasePostEntity> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<PurchasePostEntity>>(
    `/ipmspurchase/purchase/PurchaseOrderFacade/queryPurchaseOrderPage`,
    {
      data: params,
    },
  );
};
/**
 * 再来一单
 */

export const copyOrderPost = async (params: { orderNo: string }) => {
  return request<ResponseDataType<boolean>>(
    `/ipmspurchase/purchase/PurchaseOrderFacade/copyOrder`,
    {
      origin: true,
      data: params,
    },
  );
};
