import type { ProColumns } from '@ant-design/pro-components';
import type { PostEntity } from '../types/post.entity';

export interface PostListTableColumnsProps { }

export const PostListTableColumns = () =>
  [
    {
      title: '序号',
      valueType: 'index',
      width: 40,
    },
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      search: false,
      width: 140,
    },
    {
      title: '品牌',
      dataIndex: 'contactPerson2',
      key: 'contactPerson2',
      search: false,
      width: 100,
    },
    {
      title: '类目',
      dataIndex: 'contactPerson2',
      key: 'contactPerson2',
      search: false,
      width: 100,
    },
    {
      title: 'OE',
      dataIndex: 'createTime',
      key: 'createTime',
      search: false,
      width: 160,
    },
    {
      title: '品牌件号',
      dataIndex: 'contactPerson1',
      key: 'contactPerson1',
      search: false,
      width: 100,
    },
    {
      title: '使用车型',
      dataIndex: 'contactPerson3',
      key: 'contactPerson3',
      search: false,
      width: 100,
    },
    {
      title: '本地库存',
      dataIndex: 'contactPerson3',
      key: 'contactPerson3',
      search: false,
      width: 60,
    },
    {
      title: '在途库存',
      dataIndex: 'contactPerson3',
      key: 'contactPerson3',
      search: false,
      width: 100,
    },
    {
      title: '库存下限',
      dataIndex: 'contactPerson3',
      key: 'contactPerson3',
      search: false,
      width: 100,
    },
    {
      title: '库存上限',
      dataIndex: 'contactPerson3',
      key: 'contactPerson3',
      search: false,
      width: 100,
    },
    {
      title: '补货数量',
      dataIndex: 'contactPerson3',
      key: 'contactPerson3',
      search: false,
      width: 100,
    },
  ] as ProColumns<PostEntity>[];
