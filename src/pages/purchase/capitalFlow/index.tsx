import LeftTitle from '@/components/LeftTitle';
import FunProTable from '@/components/common/FunProTable';
import { ProCard } from '@ant-design/pro-components';
import { useIntl } from 'umi';
import { CapitalEntity } from './types/CapitalEntity';

export interface CapitalFlowProps {
  capitalData?: any;
}

const CapitalFlow = (props: CapitalFlowProps) => {
  const intl = useIntl();

  return props.capitalData?.length > 0 ? (
    <ProCard bodyStyle={{ padding: 0 }} className="mt-4">
      <FunProTable<CapitalEntity, any>
        headerTitle={<LeftTitle title={intl.formatMessage({ id: 'purchase.capitalFlow.title' })} />}
        search={false}
        pagination={false}
        className="mt-4"
        options={false}
        scroll={{ x: true }}
        dataSource={props.capitalData}
        columns={[
          {
            title: intl.formatMessage({ id: 'purchase.capitalFlow.columns.settlementTime' }),
            dataIndex: 'confirmTime',
          },
          {
            title: intl.formatMessage({ id: 'purchase.capitalFlow.columns.settlementAccount' }),
            dataIndex: 'desc',
            search: false,
          },
          {
            title: intl.formatMessage({ id: 'purchase.capitalFlow.columns.settlementAmount' }),
            dataIndex: 'amount',
            search: false,
          },
        ]}
      />
    </ProCard>
  ) : (
    <></>
  );
};

export default CapitalFlow;
