export interface CapitalEntity {
  /**
   * 账户id
   */
  accountId?: number;
  /**
   * 账户名称
   */
  accoutName?: string;
  /**
   * 金额，单位：分
   */
  amount?: number;
  /**
   * 金额，单位：元
   */
  amountYuan?: number;
  /**
   * 业务单号
   */
  bizNo?: string;
  /**
   * 业务时间
   */
  bizTime?: string;
  /**
   * 业务类型
   */
  bizType?: number;
  /**
   * 买家id
   */
  buyerId?: string;
  /**
   * 买家名称
   */
  buyerName?: string;
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * 台账类型：1：收入，2：支出
   */
  ledgerType?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * 零售商名称
   */
  memberName?: string;
  /**
   * 卖家id
   */
  sellerId?: string;
  /**
   * 卖家名称
   */
  sellerName?: string;
  /**
   * 资金流水号
   */
  serialNumber?: string;
  /**
   * 0：无效，1：有效
   */
  status?: number;
  /**
   * 门店id
   */
  storeId?: string;
  /**
   * 门店名称
   */
  storeName?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
}
