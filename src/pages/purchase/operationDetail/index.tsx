import LeftTitle from '@/components/LeftTitle';
import FunProTable from '@/components/common/FunProTable';
import { LogList } from '@/pages/purchase/detail/types/purchase.post.entity';
import { ProCard } from '@ant-design/pro-components';
import { useIntl } from 'umi';
import { OperationEntity } from './type/operation.entity';

export interface OperationDetailProps {
  logList?: LogList[];
}

const OperationDetail = (props: OperationDetailProps) => {
  const intl = useIntl();

  return (
    <ProCard bodyStyle={{ padding: 0 }} className="mt-4">
      <FunProTable<OperationEntity, any>
        headerTitle={<LeftTitle title={intl.formatMessage({ id: 'purchase.operationDetail.title' })} />}
        search={false}
        pagination={false}
        className="mt-4"
        options={false}
        scroll={{ x: true }}
        dataSource={props.logList ?? []}
        columns={[
          {
            title: intl.formatMessage({ id: 'purchase.operationDetail.columns.operationNode' }),
            dataIndex: 'operationTypeDesc',
            search: false,
          },
          {
            title: intl.formatMessage({ id: 'purchase.operationDetail.columns.operationTime' }),
            dataIndex: 'operationTime',
          },
          {
            title: intl.formatMessage({ id: 'purchase.operationDetail.columns.operator' }),
            dataIndex: 'operator',
            search: false,
          },
        ]}
      />
    </ProCard>
  );
};

export default OperationDetail;
