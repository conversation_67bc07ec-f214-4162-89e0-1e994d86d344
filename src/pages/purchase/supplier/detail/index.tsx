import SubTitle from '@/components/common/SubTitle';
import {
  PageContainer,
  ProCard,
  ProDescriptions,
  ProDescriptionsActionType,
} from '@ant-design/pro-components';
import { Space, Tag } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { useRef, useState } from 'react';
import { useIntl, useSearchParams } from 'umi';
import { postStatusOptions } from '../list/types/PostStatus';
import { YesNoStatus } from '../operation/types/YesNo';
import { AddPostEntity } from '../operation/types/add.post.entity';
import { queryFullById } from '../services';
export default () => {
  const intl = useIntl();
  const [form] = useForm();
  const actionRef = useRef<ProDescriptionsActionType>();
  let [searchParams, setSearchParams] = useSearchParams();
  let [record, setRecord] = useState<AddPostEntity>({});

  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  return (
    <PageContainer>
      <ProCard>
        <ProDescriptions
          actionRef={actionRef}
          title={
            <Space>
              <span>{record?.supplierInfo?.supplierName}</span>
              <Tag color={postStatusOptions[record?.supplierInfo?.supplierStatus!]?.status}>
                {postStatusOptions[record?.supplierInfo?.supplierStatus!]?.text}
              </Tag>
            </Space>
          }
          params={{ id: searchParams.get('id') ?? '' }}
          request={async (param) => {
            if (param?.id) {
              const data = await queryFullById({ id: param.id });
              setRecord(data);
              return { data, success: true };
            }
            return [];
          }}
          column={3}
        >
          <ProDescriptions.Item dataIndex={['supplierInfo', 'supplierCode']} label={intl.formatMessage({ id: 'purchase.supplier.detail.basic.supplierCode' })} />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'purchase.supplier.detail.basic.shortName' })} dataIndex={['supplierInfo', 'shortName']} />
          <ProDescriptions.Item label={intl.formatMessage({ id: 'purchase.supplier.detail.basic.remark' })} dataIndex={['supplierInfo', 'remark']} />
        </ProDescriptions>
      </ProCard>
      <ProCard className="mt-4" title={<SubTitle text={intl.formatMessage({ id: 'purchase.supplier.detail.contact.title' })} />} wrap>
        {record?.supplierConcatList?.map((s, index) => {
          return (
            <ProCard
              layout="center"
              bordered
              className={index != 0 ? 'mt-4' : ''}
              title={
                <div className="font-normal text-black/[0.85]">
                  <Space>
                    {s.concatPerson}
                    {s.post && (
                      <Tag color="red" className="ml-2">
                        {s.post}
                      </Tag>
                    )}
                    {s.isDefault == YesNoStatus.NO && <Tag color="blue">{intl.formatMessage({ id: 'purchase.supplier.detail.contact.defaultContact' })}</Tag>}
                  </Space>
                </div>
              }
            >
              <ProDescriptions column={4} dataSource={s}>
                <ProDescriptions.Item dataIndex="concatPhone" label={intl.formatMessage({ id: 'purchase.supplier.detail.contact.contactPhone' })} />
                <ProDescriptions.Item label={intl.formatMessage({ id: 'purchase.supplier.detail.contact.qq' })} dataIndex="qq" />
                <ProDescriptions.Item label={intl.formatMessage({ id: 'purchase.supplier.detail.contact.wechat' })} dataIndex="wx" />
                <ProDescriptions.Item label={intl.formatMessage({ id: 'purchase.supplier.detail.contact.email' })} dataIndex="email" />
              </ProDescriptions>
            </ProCard>
          );
        })}
      </ProCard>
      <ProCard className="mt-4" title={<SubTitle text={intl.formatMessage({ id: 'purchase.supplier.detail.address.title' })} />} wrap>
        {record?.supplierAddressList?.map((s, index) => {
          return (
            <ProCard
              layout="center"
              bordered
              className={index != 0 ? 'mt-4' : ''}
              title={
                <div className="font-normal text-black/[0.85]">
                  {intl.formatMessage({ id: 'purchase.supplier.detail.address.addressNumber' }, { number: index + 1 })}
                  {s.isDefault == YesNoStatus.NO && (
                    <Tag color="blue" className="ml-2">
                      {intl.formatMessage({ id: 'purchase.supplier.detail.address.defaultAddress' })}
                    </Tag>
                  )}
                </div>
              }
            >
              <ProDescriptions column={4} dataSource={s}>
                <ProDescriptions.Item label={intl.formatMessage({ id: 'purchase.supplier.detail.address.region' })}>
                  {s.province + '' + s.city + '' + s.area}
                </ProDescriptions.Item>
                <ProDescriptions.Item label={intl.formatMessage({ id: 'purchase.supplier.detail.address.detailAddress' })} dataIndex="detailAddress" />
                <ProDescriptions.Item dataIndex="concatPerson" label={intl.formatMessage({ id: 'purchase.supplier.detail.address.contactPerson' })} />
                <ProDescriptions.Item label={intl.formatMessage({ id: 'purchase.supplier.detail.address.contactPhone' })} dataIndex="concatPhone" />
              </ProDescriptions>
            </ProCard>
          );
        })}
      </ProCard>
      <ProCard className="mt-4" title={<SubTitle text={intl.formatMessage({ id: 'purchase.supplier.detail.settlement.title' })} />} wrap>
        <ProDescriptions column={4} dataSource={record?.supplierInfo}>
          <ProDescriptions.Item dataIndex="initAccount" label={intl.formatMessage({ id: 'purchase.supplier.detail.settlement.initAccount' })} />
        </ProDescriptions>
      </ProCard>
    </PageContainer>
  );
};
