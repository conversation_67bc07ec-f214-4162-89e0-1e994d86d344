import { FormattedMessage } from "@umijs/max";

export enum PostStatusName {
  ENABLE = 'purchase.supplierStatus.enable',
  NOT_ENABLE = 'purchase.supplierStatus.disable',
}

export enum PostStatus {
  ENABLE = '1',
  NOT_ENABLE = '0',
}

export const postStatusOptions = {
  [PostStatus.ENABLE]: { text: <FormattedMessage id={PostStatusName.ENABLE} />, status: 'success' },
  [PostStatus.NOT_ENABLE]: { text: <FormattedMessage id={PostStatusName.NOT_ENABLE} />, status: 'error' },
};

export const statusAttribute: Record<string, string> = {
  '0': PostStatusName.ENABLE,
  '1': PostStatusName.NOT_ENABLE,
};

export const setStatusValue: Record<string, string> = {
  '0': '1',
  '1': '0',
};
