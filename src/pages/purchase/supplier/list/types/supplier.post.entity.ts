import { YesNoStatus } from '../../operation/types/YesNo';

export interface SupplierPostEntity {
  /**
   * 供应商地址信息
   */
  supplierAddressList?: SupplierAddressList[];
  /**
   * 供应商联系人信息
   */
  supplierConcatList?: SupplierConcatList[];
  /**
   * 供应商基本信息
   */
  supplierInfo?: SupplierInfo;
}

export interface SupplierAddressList {
  /**
   * 区
   */
  area?: string;
  /**
   * 区code
   */
  areaCode?: string;
  /**
   * 市
   */
  city?: string;
  /**
   * 市code
   */
  cityCode?: string;
  /**
   * 联系人
   */
  concatPerson?: string;
  /**
   * 联系方式
   */
  concatPhone?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 详细地址
   */
  detailAddress?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 是否默认;0-默认地址1-非默认地址
   */
  isDefault?: string;
  /**
   * 删除标记;0正常1已删除
   */
  isDelete?: string;
  /**
   * 省
   */
  province?: string;
  /**
   * 省code
   */
  provinceCode?: string;
  /**
   * 供应商id
   */
  supplierId?: number;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}

export interface SupplierConcatList {
  /**
   * 联系人
   */
  concatPerson?: string;
  /**
   * 联系方式
   */
  concatPhone?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 是否默认联系人
   */
  isDefault?: string;
  /**
   * 删除标记;0正常1已删除
   */
  isDelete?: string;
  /**
   * 职务
   */
  post?: string;
  /**
   * QQ
   */
  qq?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 性别;0-男1-女
   */
  sex?: string;
  /**
   * 供应商id
   */
  supplierId?: number;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 微信
   */
  wx?: string;
}

/**
 * 供应商基本信息
 */
export interface SupplierInfo {
  /**
   * 是否可删除0-是1-否
   */
  canDelete?: string;
  /**
     * 期初应付账款
     */
  initAccount?: number;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * id
   */
  id: string;
  /**
   * 删除标记;0正常1已删除
   */
  isDelete?: string;
  /**
   * 门店系统侧零售商ID
   */
  memberId?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 供应商简称
   */
  shortName?: string;
  /**
   * 供应商code
   */
  supplierCode?: string;
  /**
   * 供应商name
   */
  supplierName?: string;
  /**
   * 供应商状态;0-禁用1-启用
   */
  supplierStatus?: string;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 是否可编辑 0-否 1-是
   */
  canEdit?: YesNoStatus;
}

export interface SupplierList {
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 供应商name
   */
  supplierName?: string;
}
