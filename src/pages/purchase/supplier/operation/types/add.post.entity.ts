import { PostStatus } from '../../list/types/PostStatus';
import { YesNoStatus } from './YesNo';

export interface AddPostEntity {
  /**
   * 供应商地址信息
   */
  supplierAddressList?: SupplierAddressList[];
  /**
   * 供应商联系人信息
   */
  supplierConcatList?: SupplierConcatList[];
  /**
   * 供应商基本信息
   */
  supplierInfo?: SupplierInfo;
}

export interface SupplierAddressList {
  /**
   * 区
   */
  area?: string;
  /**
   * 区code
   */
  areaCode?: string;
  /**
   * 市
   */
  city?: string;
  /**
   * 市code
   */
  cityCode?: string;
  /**
   * 详细地址
   */
  detailAddress?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 是否默认;0-默认地址1-非默认地址
   */
  isDefault?: string;
  /**
   * 省
   */
  province?: string;
  /**
   * 省code
   */
  provinceCode?: string;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 操作人(创建人、更新人)
   */
  updatePerson?: string;

  /**
   * 省市区code列表，兼容前端传值方式
   */
  areaCodeList?: string[];
}

export interface SupplierConcatList {
  /**
   * 联系人
   */
  concatPerson?: string;
  /**
   * 联系方式
   */
  concatPhone?: string;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 是否默认联系人
   */
  isDefault?: YesNoStatus;
  /**
   * 职务
   */
  post?: string;
  /**
   * QQ
   */
  qq?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 性别;0-男1-女
   */
  sex?: string;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 操作人(创建人、更新人)
   */
  updatePerson?: string;
  /**
   * 微信
   */
  wx?: string;
}

export interface SupplierInfo {
  /**
   * id
   */
  id?: string;
  /**
   * 门店系统侧零售商ID
   */
  memberId?: string;

  /**
     * 期初应付账款
     */
  initAccount?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 供应商简称
   */
  shortName?: string;
  /**
   * 供应商code
   */
  supplierCode?: string;
  /**
   * 供应商name
   */
  supplierName?: string;
  /**
   * 操作人(创建人、更新人)
   */
  updatePerson?: string;

  supplierStatus?: PostStatus;
}
