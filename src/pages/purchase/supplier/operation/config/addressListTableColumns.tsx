import { queryDistrictAreaTree } from '@/pages/system/store/services';
import { requiredProps } from '@/types/validateRules';
import type { ProColumns } from '@ant-design/pro-components';
import { ProFormRadio } from '@ant-design/pro-components';
import { Popconfirm, Radio } from 'antd';
import { YesNoStatus } from '../types/YesNo';
import type { SupplierAddressList } from '../types/add.post.entity';

export interface AddressListTableColumnsProps {
  intl: any;
  setIsChecked: (id: string) => void;
  handleDeleteItemAddress: (id: string, isDefault: string) => void;
}

export const AddressListTableColumns = (props: AddressListTableColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      dataIndex: 'index',
      readonly: true,
      width: 40,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.address.columns.defaultAddress' }),
      dataIndex: 'isDefault',
      key: 'isDefault',
      search: false,
      width: 80,
      render: (_, row, index) => (
        <ProFormRadio
          fieldProps={{
            width: '80',
            checked: row.isDefault == YesNoStatus.NO,
          }}
        />
      ),
      renderFormItem: ({ index, originProps }, config) => (
        <Radio
          checked={config.record?.isDefault == YesNoStatus.NO}
          onChange={() => props.setIsChecked(config?.record?.id ?? '')}
        />
      ),
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.address.columns.region' }),
      dataIndex: 'areaCodeList',
      key: 'areaCodeList',
      search: true,
      width: 200,
      valueType: 'cascader',
      fieldProps: {
        fieldNames: { label: 'areaName', value: 'areaId' },
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
      request: () => {
        return queryDistrictAreaTree();
      },
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.address.columns.detailAddress' }),
      dataIndex: 'detailAddress',
      key: 'detailAddress',
      width: 200,
      search: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.address.columns.contactPerson' }),
      dataIndex: 'concatPerson',
      key: 'concatPerson',
      search: false,
      width: 100,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.address.columns.contactPhone' }),
      dataIndex: 'concatPhone',
      key: 'concatPhone',
      search: false,
      width: 120,
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 100,
      fixed: 'right',
      valueType: 'option',
      render: (text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            console.log(action);
            action?.startEditable?.(record.id ?? '');
          }}
        >
          {props.intl.formatMessage({ id: 'purchase.supplier.operation.address.button.edit' })}
        </a>,
        <Popconfirm
          key="delete"
          title={props.intl.formatMessage({ id: 'purchase.supplier.operation.address.confirm.delete' })}
          onConfirm={() => props.handleDeleteItemAddress(record.id ?? '', record.isDefault ?? '')}
        >
          <a>{props.intl.formatMessage({ id: 'purchase.supplier.operation.address.button.delete' })}</a>
        </Popconfirm>,
      ],
    },
  ] as ProColumns<SupplierAddressList>[];
