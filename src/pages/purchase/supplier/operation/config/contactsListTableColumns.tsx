import { requiredProps } from '@/types/validateRules';
import { ProFormRadio, type ProColumns } from '@ant-design/pro-components';
import { Popconfirm, Radio } from 'antd';
import { YesNoStatus } from '../types/YesNo';
import type { SupplierConcatList } from '../types/add.post.entity';

export interface ContactsListTableColumnsProps {
  intl: any;
  setIsChecked: (index: string) => void;
  handleDeleteItemContacts: (id: string, isDefault: string) => void;
}

export const ContactsListTableColumns = (props: ContactsListTableColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      dataIndex: 'index',
      readonly: true,
      width: 40,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.contact.columns.defaultContact' }),
      dataIndex: 'isDefault',
      key: 'isDefault',
      search: false,
      width: 80,
      render: (_, row, index) => (
        <ProFormRadio
          width={80}
          fieldProps={{
            checked: row.isDefault == YesNoStatus.NO,
          }}
        />
      ),
      renderFormItem: ({ index, originProps }, config) => (
        <Radio
          checked={config.record?.isDefault == YesNoStatus.NO}
          onChange={() => props.setIsChecked(config?.record?.id ?? '')}
        />
      ),
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.contact.columns.contactPerson' }),
      dataIndex: 'concatPerson',
      key: 'concatPerson',
      search: true,
      width: 100,
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.contact.columns.contactPhone' }),
      dataIndex: 'concatPhone',
      key: 'concatPhone',
      search: true,
      width: 140,
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.contact.columns.post' }),
      dataIndex: 'post',
      key: 'post',
      search: false,
      width: 100,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.contact.columns.qq' }),
      dataIndex: 'qq',
      key: 'qq',
      width: 120,
      search: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.contact.columns.wechat' }),
      dataIndex: 'wx',
      key: 'wx',
      width: 120,
      search: false,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.contact.columns.email' }),
      dataIndex: 'email',
      key: 'email',
      search: false,
      width: 120,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.supplier.operation.contact.columns.remark' }),
      dataIndex: 'remark',
      key: 'remark',
      search: false,
      width: 120,
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 100,
      fixed: 'right',
      valueType: 'option',
      render: (text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id ?? '');
          }}
        >
          {props.intl.formatMessage({ id: 'purchase.supplier.operation.contact.button.edit' })}
        </a>,
        <Popconfirm
          key="delete"
          title={props.intl.formatMessage({ id: 'purchase.supplier.operation.contact.confirm.delete' })}
          onConfirm={() => props.handleDeleteItemContacts(record.id ?? '', record.isDefault ?? '')}
        >
          <a>{props.intl.formatMessage({ id: 'purchase.supplier.operation.contact.button.delete' })}</a>
        </Popconfirm>,
      ],
    },
  ] as ProColumns<SupplierConcatList>[];
