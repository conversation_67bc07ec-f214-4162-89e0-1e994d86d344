import SubTitle from '@/components/common/SubTitle';
import type { CommonModelForm } from '@/types/CommonModelForm';
import { REG_ONLY_ALPHA_AND_DIGIT_RULE } from '@/utils/RuleUtils';
import type {
  ProDescriptionsActionType,
  ProFormInstance
} from '@ant-design/pro-components';
import {
  DrawerForm,
  EditableProTable,
  PageContainer,
  ProCard,
  ProFormMoney,
  ProFormText,
} from '@ant-design/pro-components';
import { useAsyncEffect } from 'ahooks';
import { Col, ConfigProvider, Row } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { useRef, useState } from 'react';
import { useIntl } from 'umi';
import { queryFullById } from '../services';
import { AddressListTableColumns } from './config/addressListTableColumns';
import { ContactsListTableColumns } from './config/contactsListTableColumns';
import { YesNoStatus } from './types/YesNo';
import type { AddPostEntity, SupplierAddressList, SupplierConcatList } from './types/add.post.entity';
export default (props: CommonModelForm<string, any>) => {
  const intl = useIntl();
  const [form] = useForm<any>();
  const formRef = useRef<ProFormInstance<any>>();
  const actionRef = useRef<ProDescriptionsActionType>();
  const [concatEditableKeys, setConcatEditableKeys] = useState<React.Key[]>(() => []);
  const [addressEditableKeys, setAddressEditableKeys] = useState<React.Key[]>(() => []);

  useAsyncEffect(async () => {
    if (props.recordId == '0') {
      form.resetFields();
    } else {
      setConcatEditableKeys([]);
      setAddressEditableKeys([]);
      const data = await queryFullById({ id: props.recordId ?? '' });
      form.setFieldsValue(data);
    }
  }, [props.visible]);
  //删除联系人
  const handleDeleteItemContacts = async (id: string, isDefault: string) => {
    const supplierConcatListDataSource = form.getFieldValue(
      'supplierConcatList',
    ) as SupplierConcatList[];
    let newDataSource;
    newDataSource = supplierConcatListDataSource.filter((item) => item.id !== id);
    if (isDefault == YesNoStatus.NO) {
      if (newDataSource.length > 0) {
        console.log(newDataSource);
        newDataSource = newDataSource.map((s, index) => ({
          ...s,
          isDefault: index == 0 ? YesNoStatus.NO : YesNoStatus.YES,
        }));
      }
    }
    form.setFieldsValue({
      supplierConcatList: newDataSource,
    });
  };
  //删除联系人
  const handleDeleteItemAddress = async (id: string, isDefault: string) => {
    const supplierAddressListDataSource = form.getFieldValue(
      'supplierAddressList',
    ) as SupplierConcatList[];
    let newDataSource;
    newDataSource = supplierAddressListDataSource.filter((item) => item.id !== id);
    if (isDefault == YesNoStatus.NO) {
      //如果删除的是默认地址
      if (newDataSource.length > 0) {
        newDataSource = newDataSource.map((s, index) => ({
          ...s,
          isDefault: index == 0 ? YesNoStatus.NO : YesNoStatus.YES,
        }));
      }
    }
    form.setFieldsValue({
      supplierAddressList: newDataSource,
    });
  };
  //设置地址默认值
  const setIsCheckedAddress = async (id: string) => {
    const supplierAddressListDataSource = form.getFieldValue(
      'supplierAddressList',
    ) as SupplierAddressList[];
    const newDataSource = supplierAddressListDataSource.map((s) => ({
      ...s,
      isDefault: s.id == id ? YesNoStatus.NO : YesNoStatus.YES,
    }));
    form.setFieldsValue({
      supplierAddressList: newDataSource,
    });
  };
  //设置联系人默认
  const setIsChecked = async (id: string) => {
    const supplierConcatListDataSource = form.getFieldValue(
      'supplierConcatList',
    ) as SupplierConcatList[];
    const newDataSource = supplierConcatListDataSource.map((s) => ({
      ...s,
      isDefault: s.id == id ? YesNoStatus.NO : YesNoStatus.YES,
    }));
    form.setFieldsValue({
      supplierConcatList: newDataSource,
    });
  };

  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  const rules = [{ required: !props.readOnly }];

  return (
    <PageContainer>
      <DrawerForm<AddPostEntity>
        form={form}
        onFinish={props.onOk}
        drawerProps={{
          maskClosable: false,
          onClose: props.onCancel,
        }}
        title={props.title}
        open={props.visible}
        width={1200}
        layout="inline"
      >
        <ProCard title={<SubTitle text={intl.formatMessage({ id: 'purchase.supplier.operation.basic.title' })} />}>
          <ProFormText name={['supplierInfo', 'id']} disabled={props.readOnly} hidden={true} />
          <Row>
            <Col span={8}>
              <ProFormText
                name={['supplierInfo', 'supplierName']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.basic.supplierName' })}
                rules={rules}
                placeholder={intl.formatMessage({ id: 'purchase.supplier.operation.basic.supplierName.placeholder' })}
                fieldProps={{ maxLength: 50 }}
              />
            </Col>
            <Col span={8}>
              <ProFormText
                rules={[REG_ONLY_ALPHA_AND_DIGIT_RULE]}
                name={['supplierInfo', 'supplierCode']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.basic.supplierCode' })}
                placeholder={intl.formatMessage({ id: 'purchase.supplier.operation.basic.supplierCode.placeholder' })}
                fieldProps={{ maxLength: 50 }}
              />
            </Col>
            <Col span={8}>
              <ProFormText
                name={['supplierInfo', 'shortName']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.basic.shortName' })}
                placeholder={intl.formatMessage({ id: 'purchase.supplier.operation.basic.shortName.placeholder' })}
                fieldProps={{ maxLength: 20 }}
              />
            </Col>
          </Row>
          <Row className="pt-4 pl-[12px]">
            <Col span={24}>
              <ProFormText
                name={['supplierInfo', 'remark']}
                label={intl.formatMessage({ id: 'purchase.supplier.operation.basic.remark' })}
                placeholder={intl.formatMessage({ id: 'purchase.supplier.operation.basic.remark.placeholder' })}
                fieldProps={{ count: { max: 100, show: true }, maxLength: 100 }}
                rules={[{ max: 100 }]}
              />
            </Col>
          </Row>
        </ProCard>
        <ConfigProvider
          theme={{
            components: {
              InputNumber: {
                controlWidth: 130,
              },
            },
          }}
        >
          <EditableProTable<SupplierConcatList>
            rowKey="id"
            search={false}
            columns={ContactsListTableColumns({ intl, setIsChecked, handleDeleteItemContacts })}
            headerTitle={<SubTitle text={intl.formatMessage({ id: 'purchase.supplier.operation.contact.title' })} />}
            recordCreatorProps={{
              record: (index, dataSource) => ({
                id: 'new_' + Math.random() * 1000000 + '',
                isDefault: dataSource?.length == 0 ? YesNoStatus.NO : YesNoStatus.YES,
              }),
              creatorButtonText: intl.formatMessage({ id: 'purchase.supplier.operation.contact.add' }),
            }}
            name="supplierConcatList"
            editable={{
              type: 'multiple',
              editableKeys: concatEditableKeys,
              onChange: setConcatEditableKeys,
              actionRender: (row, config, defaultDom) => [defaultDom.delete],
            }}
          />
        </ConfigProvider>
        <EditableProTable<SupplierAddressList>
          rowKey="id"
          search={false}
          columns={AddressListTableColumns({
            intl,
            setIsChecked: setIsCheckedAddress,
            handleDeleteItemAddress,
          })}
          headerTitle={<SubTitle text={intl.formatMessage({ id: 'purchase.supplier.operation.address.title' })} />}
          name="supplierAddressList"
          recordCreatorProps={{
            record: (index, dataSource) => ({
              id: 'new_' + Math.random() * 1000000,
              isDefault: dataSource?.length == 0 ? YesNoStatus.NO : YesNoStatus.YES,
            }),
            creatorButtonText: intl.formatMessage({ id: 'purchase.supplier.operation.address.add' }),
          }}
          editable={{
            type: 'multiple',
            editableKeys: addressEditableKeys,
            onChange: setAddressEditableKeys,
            actionRender: (row, config, defaultDom) => [defaultDom.delete],
          }}
        />
        <ProCard title={<SubTitle text={intl.formatMessage({ id: 'purchase.supplier.operation.settlement.title' })} />}>
          <ProFormMoney
            name={['supplierInfo', 'initAccount']}
            label={intl.formatMessage({ id: 'purchase.supplier.operation.settlement.initAccount' })}
            placeholder={intl.formatMessage({ id: 'purchase.supplier.operation.settlement.initAccount.placeholder' })}
            width={'lg'}
            addonAfter={intl.formatMessage({ id: 'purchase.supplier.operation.settlement.currency' })}
          />
        </ProCard>
      </DrawerForm>
    </PageContainer>
  );
};
