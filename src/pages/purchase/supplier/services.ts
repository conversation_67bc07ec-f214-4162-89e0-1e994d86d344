import { PageResponseDataType } from '@/types/PageResponseDataType';
import { ResponseDataType } from '@/types/ResponseDataType';
import { request } from '@/utils/request';
import { QueryPostListRequest } from './list/types/query.post.list.request';
import { SupplierList, SupplierPostEntity } from './list/types/supplier.post.entity';
import { YesNoStatus } from './operation/types/YesNo';
import { AddPostEntity } from './operation/types/add.post.entity';

/**
 * 供应商列表查询
 * @param params
 * @returns
 */
export const queryPostList = async (params: Partial<QueryPostListRequest>) => {
  return request<PageResponseDataType<SupplierPostEntity>>(
    `/ipmspurchase/vendor/SupplierFacade/queryPage`,
    {
      data: params,
    },
  );
};
/**
 * 根据供应商ID查询明细
 * @param params
 * @returns
 */
export const queryFullById = async (params: { id: string }) => {
  return request<AddPostEntity>(`/ipmspurchase/vendor/SupplierFacade/getFullById`, {
    data: params,
  });
};
/**
 * 根据id 禁用启用
 * @param params
 * @returns
 */
export const updateStatusById = async (params: { id: string; supplierStatus: string }) => {
  return request<boolean>(`/ipmspurchase/vendor/SupplierFacade/updateStatus`, {
    data: params,
  });
};
/**
 * 查询供应商 下来框
 * @param params
 * @returns
 */
export const querySupplierList = async ({
  supplierStatus = YesNoStatus.YES,
}: {
  supplierStatus?: YesNoStatus;
}) => {
  return request<SupplierList[]>(`/ipmspurchase/vendor/SupplierFacade/querySupplierList`, {
    data: { supplierStatus },
  });
};
/**
 * 新增或者编辑 供应商
 * @param params
 * @returns
 */
export const createOrUpdateFullInfoPost = async (params: Partial<AddPostEntity>) => {
  return request<ResponseDataType<boolean>>(
    `/ipmspurchase/vendor/SupplierFacade/createOrUpdateFullInfo`,
    {
      origin: true,
      data: params,
    },
  );
};
