import { request } from '@/utils/request';
import type { AddItemExternalRequest } from './types/add.item.external.request';
import type { AddItemExternalResult } from './types/add.item.external.result';
import { AddPurchaseLineBatchRequest } from './types/add.purchase.line.batch.request';
import type { CreateExternalRequest } from './types/create.external.request';
import type { UpdateExternalRequest } from './types/update.external.request';

/**
 * 新建
 * @param params
 * @returns
 */
export const createExternalPurchasePost = async (params: Partial<CreateExternalRequest>) => {
  return request<AddItemExternalResult>(
    '/ipmspurchase/purchase/ExternalPurchaseFacade/saveExternalPurchase',
    {
      data: params,
    },
  );
};
/**
 * 编辑
 * @param params
 * @returns
 */
export const updateExternalPurchasePost = async (params: Partial<UpdateExternalRequest>) => {
  return request<boolean>('/ipmspurchase/purchase/ExternalPurchaseFacade/updateExternalPurchase', {
    data: params,
  });
};

export const addPurchaseLinePost = async (params: Partial<AddItemExternalRequest>) => {
  return request<AddItemExternalResult>(
    '/ipmspurchase/purchase/PurchaseOrderLineFacade/addPurchaseLine',
    {
      data: params,
    },
  );
};

/**
 * 批量加入采购单
 * @param params
 * @returns
 */
export const addPurchaseLineBatchPost = (params: AddPurchaseLineBatchRequest): Promise<boolean> => {
  return request('/ipmspurchase/purchase/PurchaseOrderLineFacade/addPurchaseLineBatch', {
    data: params,
  });
};

/**
 * 编辑行数据
 * @param params
 * @returns
 */
export const updatePurchaseLinePost = async (params: Partial<AddItemExternalRequest>) => {
  return request<boolean>('/ipmspurchase/purchase/PurchaseOrderLineFacade/updatePurchaseLine', {
    data: params,
  });
};
/**
 * 删除采购行明细
 * @param params
 * @returns
 */
export const deletePurchaseLinePost = async (params: { ids: string[]; orderNo?: string }) => {
  return request<boolean>('/ipmspurchase/purchase/PurchaseOrderLineFacade/deletePurchaseLine', {
    data: params,
  });
};
/**
 * 提交采购订单信息
 * @param params
 * @returns
 */
export const submitExternalPurchasePost = async (params: { orderNo?: string }) => {
  return request<boolean>('/ipmspurchase/purchase/ExternalPurchaseFacade/submitExternalPurchase', {
    data: params,
  });
};
/**
 * 确认支付
 * @param params
 * @returns
 */
export const confirmPayExternalPurchasePost = async (params: { orderNo?: string }) => {
  return request<boolean>('/ipmspurchase/purchase/ExternalPurchaseFacade/confirmPay', {
    data: params,
  });
};

/**
 * 直接入库
 * @param params
 * @returns
 */
export const inStockExternalPurchasePost = async (params: { orderNo?: string }) => {
  return request<boolean>('/ipmspurchase/purchase/ExternalPurchaseFacade/inStock', {
    data: params,
  });
};
