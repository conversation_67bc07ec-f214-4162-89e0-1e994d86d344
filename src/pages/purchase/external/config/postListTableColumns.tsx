import ColumnRender from '@/components/ColumnRender';
import type { StocksInfoDrawerProps } from '@/components/StocksInfoDrawer';
import { requiredProps } from '@/types/validateRules';
import { MAX_AMOUNT, MAX_COUNT } from '@/utils/Constants';
import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { isEmpty } from 'lodash';
import type { LinePostEntity } from '../../detail/types/line.post.entity';
import type { AddItemExternalRequest } from '../types/add.item.external.request';

export interface PostListTableColumnsProps {
  handleUpdateItem: (item: AddItemExternalRequest) => void;
  /**
   * 查看库存
   */
  handleViewStocksInfo: (data: StocksInfoDrawerProps) => void;
}

export const PostListTableColumns = (props: PostListTableColumnsProps): ProColumns<LinePostEntity>[] => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.index' }),
      valueType: 'index',
      dataIndex: 'index',
      fixed: 'left',
      readonly: true,
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.itemName' }),
      dataIndex: 'skuName',
      readonly: true,
      width: 120,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.itemCode' }),
      dataIndex: 'itemSn',
      key: 'itemSn',
      search: false,
      readonly: true,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.oeNo' }),
      dataIndex: 'oe',
      width: 140,
      readonly: true,
      renderText: (text, entity) => {
        return ColumnRender.ArrayColumnRender(entity.oeList as string[]);
      },
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.brandPartNo' }),
      dataIndex: 'brandPart',
      width: 100,
      readonly: true,
      renderText: (text, entity) => {
        return ColumnRender.ArrayColumnRender(entity.brandPartNoList as string[]);
      },
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.brand' }),
      dataIndex: 'brandName',
      width: 100,
      readonly: true,
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.category' }),
      dataIndex: 'categoryName',
      width: 100,
      readonly: true,
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.originRegion' }),
      dataIndex: 'originRegionName',
      width: 100,
      readonly: true,
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.specification' }),
      dataIndex: 'spec',
      width: 60,
      readonly: true,
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.vehicleRemark' }),
      dataIndex: 'adaptModel',
      width: 120,
      readonly: true,
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.itemRemark' }),
      dataIndex: 'skuRemark',
      width: 120,
      readonly: true,
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.unit' }),
      dataIndex: 'unit',
      readonly: true,
      width: 50,
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.localInventory' }),
      dataIndex: 'inventoryNum',
      width: 60,
      readonly: true,
      renderText: (text, record: LinePostEntity) => (
        <a
          className="cursor-pointer"
          onClick={() => {
            props.handleViewStocksInfo({
              itemIdList: [record.itemId!],
            });
          }}
        >
          {record.inventoryNum}
        </a>
      ),
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.inventoryLowerLimit' }),
      dataIndex: 'lowerLimit',
      width: 60,
      readonly: true,
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.inventoryUpperLimit' }),
      dataIndex: 'upperLimit',
      width: 60,
      readonly: true,
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.location' }),
      dataIndex: 'locationCode',
      width: 100,
      readonly: true,
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.subtotal' }),
      dataIndex: 'amount',
      width: 80,
      readonly: true,
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.purchasePrice' }),
      dataIndex: 'price',
      fixed: 'right',
      readonly: false,
      width: 100,
      valueType: 'money',
      formItemProps: () => {
        return {
          rules: [requiredProps, { type: 'number', min: 0.01, message: intl.formatMessage({ id: 'purchase.external.validation.minAmount' }) }],
        };
      },
      fieldProps(_, config) {
        return {
          min: 0.01,
          precision: 2,
          max: MAX_AMOUNT,
          onPressEnter: (e) => {
            if (!isEmpty(e)) {
              props.handleUpdateItem({
                id: config.entity?.id!,
                itemSn: config.entity?.itemSn,
                num: config.entity?.num,
                price: Number(e),
              });
            }
          },
          onChange: (e: any) => {
            if (!isEmpty(e) || e > 0) {
              props.handleUpdateItem({
                id: config.entity?.id!,
                itemSn: config.entity?.itemSn,
                num: config.entity?.num,
                price: Number(e),
              });
            }
          },
        };
      },
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.purchaseQuantity' }),
      dataIndex: 'num',
      fixed: 'right',
      width: 100,
      valueType: 'digit',
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
      fieldProps(_, config) {
        return {
          min: 1,
          precision: 0,
          max: MAX_COUNT,
          onPressEnter: (e) => {
            if (!isEmpty(e)) {
              props.handleUpdateItem({
                id: config.entity?.id!,
                itemSn: config.entity?.itemSn,
                price: config.entity?.price,
                num: Number(e),
              });
            }
          },
          onChange: (e: any) => {
            if (!isEmpty(e) || e > 0) {
              props.handleUpdateItem({
                id: config.entity?.id!,
                itemSn: config.entity?.itemSn,
                price: config.entity?.price,
                num: Number(e),
              });
            }
          },
        };
      },
    },
    {
      title: intl.formatMessage({ id: 'purchase.external.columns.operation' }),
      valueType: 'option',
      fixed: 'right',
      width: 60,
    },
  ] as ProColumns<LinePostEntity>[];
};
