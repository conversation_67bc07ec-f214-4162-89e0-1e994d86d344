export interface AddPurchaseLineBatchRequest {
    /**
    * 门店系统账户id
    */
    accountId?: string;
    /**
     * 门店系统账户名称
     */
    accountName?: string;
    /**
     * 门店系统手机号
     */
    accountPhone?: string;
    /**
     * 一体系账户id
     */
    etcAccountId?: string;
    /**
     * 一体系零售商id
     */
    etcMemberId?: string;
    /**
     * 采购明细数据
     */
    lineList?: LineList[];
    /**
     * 门店系统零售商id
     */
    memberId?: string;
    /**
     * 采购单号
     */
    orderNo?: string;
    /**
     * sessionId
     */
    sessionId?: string;
}

export interface LineList {
    /**
     * 采购商品编码
     */
    itemSn?: string;
    /**
     * 采购数量
     */
    num?: number;
    /**
     * 采购单价
     */
    price?: number;
}
