import { ResponseDataType } from '@/types/ResponseDataType';
import { request } from '@/utils/request';
import { PostEntity } from './types/post.entity';
import { QueryPostListRequest } from './types/query.post.list.request';
import { QueryPostSessionRequest } from './types/query.post.session.request';
import { QueryPostTokenRequest } from './types/query.post.token.request';

/**
 * 登录
 * @param params
 * @returns
 */
export const loginPost = async (params: Partial<QueryPostListRequest>) => {
  return request<ResponseDataType<PostEntity>>(`/ipmspassport/LoginFacade/login`, {
    origin: true,
    data: params,
  });
};
/**
 * 修改密码
 * @param params
 * @returns
 */
export const modifyPasswordPost = async (params: Partial<QueryPostListRequest>) => {
  return request<ResponseDataType<true>>(`/ipmspassport/LoginFacade/modifyPassword`, {
    origin: true,
    data: params,
  });
};

/**
 * 根据提供的session信息进行用户注销。
 * @param params
 * @returns
 */
export const logoutPost = async (params: Partial<QueryPostSessionRequest>) => {
  return request<ResponseDataType<PostEntity>>(`/ipmspassport/LoginFacade/logout`, {
    origin: true,
    data: params,
  });
};
/**
 * 发送验证码
 * @param params
 * @returns
 */
export const sendAuthcodePost = async (params: Partial<QueryPostListRequest>) => {
  return request<ResponseDataType<boolean>>(`/ipmspassport/LoginFacade/sendAuthcode`, {
    origin: true,
    data: { ...params, channel: 0 },
  });
};

/**
 * 查询用户信息
 * @param params
 * @returns
 */
export const queryUserInfoPost = async (params: Partial<QueryPostSessionRequest>) => {
  return request<ResponseDataType<PostEntity>>(`/ipmspassport/LoginFacade/getSession`, {
    origin: true,
    data: params,
  });
};

/**
 * 根据token 自动登录
 * @param params
 * @returns
 */
export const autoLoginPost = async (params: Partial<QueryPostTokenRequest>) => {
  return request<ResponseDataType<PostEntity>>(`/ipmspassport/LoginFacade/autoLogin`, {
    origin: true,
    data: params,
  });
};
/**
 * 获取etc url
 * @param params
 * @returns url
 */
export const etcDirectUrlPost = async (params: Partial<QueryPostListRequest>) => {
  return request<string>(`/ipmspassport/LoginFacade/getEtcOssDirectUrl`, {
    data: params,
  });
};
