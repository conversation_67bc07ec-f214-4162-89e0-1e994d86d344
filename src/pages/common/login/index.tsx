import { setSessionCookie } from '@/access';
import Footer from '@/components/Footer';
import {
  getInitSystemInfo,
} from '@/services/systerm';
import { md5 } from '@/utils/md5';
import { LockOutlined, TabletOutlined } from '@ant-design/icons';
import {
  LoginForm,
  ProForm,
  ProFormCaptcha,
  ProFormText,
} from '@ant-design/pro-components';
import { useIntl, useModel, useSearchParams } from '@umijs/max';
import { useAsyncEffect, useBoolean } from 'ahooks';
import { Button, ConfigProvider, Flex, message } from 'antd';
import { useForm } from 'antd/es/form/Form';
import React, { useState } from 'react';
import { flushSync } from 'react-dom';
import SetPassword from './components/setPassword';
import { loginPost, modifyPasswordPost, sendAuthcodePost } from './services';
import type { QueryPostListRequest } from './types/query.post.list.request';

const Login: React.FC = () => {
  const intl = useIntl();
  const [userLoginState, setUserLoginState] = useState({});
  const { initialState, setInitialState } = useModel('@@initialState');
  const [flag, { toggle: setFlag, setTrue, setFalse }] = useBoolean(true); //flag :true 登录 false:设置密码
  const [state, { toggle, setTrue: setType }] = useBoolean(true); //登录方式
  const [form] = useForm();
  const [passTitle, setPassTitle] = useState(intl.formatMessage({ id: 'login.setNewPassword' }));
  const [searchParams, setSearchParams] = useSearchParams();

  useAsyncEffect(async () => {
    if (searchParams?.get('first')) {
      if (searchParams?.get('first') == 'YES') {
        setFalse();
        setPassTitle(intl.formatMessage({ id: 'login.setInitialPassword' }));
      }
    }
  }, [searchParams]);

  const fetchUserInfo = async () => {
    const {
      currentUser,
      currentUserInfo,
      systemMenu: systemMenu,
      buttonItem,
    } = await getInitSystemInfo();
    if (currentUser?.accountId) {
      flushSync(() => {
        setInitialState((s) => ({
          ...s,
          currentUser,
          currentUserInfo,
          systemMenu,
          buttonItem,
        }));
      });
    }
  };

  const handleSubmit = async (values: QueryPostListRequest) => {
    try {
      // 登录 手机号登录phone  用户名name
      const result = await loginPost({ ...values, type: state ? '0' : '1' });
      if (result && result.code == 0 && result.data) {
        const {
          data: { sSessionId, accountId },
        } = result;
        if (sSessionId) {
          setSessionCookie(sSessionId);
        }
        await fetchUserInfo();
        const urlParams = new URL(window.location.href).searchParams;
        // history.push(urlParams.get('redirect') || '/');
        window.location.href = urlParams.get('redirect') || '/';
        return;
      } else if (result.code == 100001) {
        //首次登录修改密码
        setFalse();
        setPassTitle(intl.formatMessage({ id: 'login.setInitialPassword' }));
        form.setFieldValue('phone', values.phone);
      }
      // 如果失败去设置用户错误信息
      setUserLoginState(result);
    } catch (error) {
      console.log(error);

      message.error(intl.formatMessage({ id: 'login.loginFailed' }));
    }
  };
  /**
   * 重置密码
   * @param values
   */
  const handleModify = async (values: QueryPostListRequest) => {
    const { code, data, msg } = await modifyPasswordPost(values);
    if (code == 0 && data) {
      //修改成功
      message.success(intl.formatMessage({ id: 'login.resetSuccess' }));
      //自动登录 --密码
      setType();
      handleSubmit({ ...values, password: values.newPassword });
      setTrue();
      form.resetFields(); //重置表单
    } else {
      message.error(msg);
    }
  };

  const onCancel = async () => {
    setTrue();
    form.resetFields(); //重置表单
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#F49C1F',
        },
      }}
    >
      <Flex
        className="h-screen bg-contain"
        vertical
        justify="center"
        style={{ backgroundImage: 'url(/bg.png)' }}
      >
        <Flex
          justify="center"
          align="center"
          style={{
            backgroundImage: 'url(/left.png) ',
            backgroundSize: '280px 100%',
            paddingLeft: 280,
            margin: '0 auto',
          }}
          className="bg-no-repeat"
        >
          <div className="overflow-hidden rounded-r-lg bg-white w-[450px]">
            {flag ? (
              <LoginForm
                title={<span className="text-[20px]">{intl.formatMessage({ id: 'login.title' })}</span>}
                initialValues={{
                  autoLogin: true,
                }}
                onFinish={async (values) => {
                  await handleSubmit(values);
                }}
                actions={
                  <Flex vertical>
                    <div>
                      <span className="cursor-pointer float-left" onClick={toggle}>
                        {state ? intl.formatMessage({ id: 'login.captchaLogin' }) : intl.formatMessage({ id: 'login.passwordLogin' })}
                      </span>
                      <span
                        className="cursor-pointer float-right"
                        onClick={() => {
                          setFlag();
                          setPassTitle(intl.formatMessage({ id: 'login.setNewPassword' }));
                        }}
                      >
                        {intl.formatMessage({ id: 'login.forgotPassword' })}
                      </span>
                    </div>
                  </Flex>
                }
              >
                <div className="mt-10">
                  <ProFormText
                    name="phone"
                    fieldProps={{
                      size: 'large',
                      prefix: <TabletOutlined />,
                      maxLength: 11,
                    }}
                    placeholder={intl.formatMessage({ id: 'login.phoneNumber' })}
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'login.phoneRequired' }),
                      },
                    ]}
                  />
                  {state ? (
                    <ProFormText.Password
                      name="password"
                      fieldProps={{
                        size: 'large',
                        prefix: <LockOutlined />,
                      }}
                      placeholder={intl.formatMessage({ id: 'login.password' })}
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'login.passwordRequired' }),
                        },
                      ]}
                      transform={(value) => md5(value)}
                    />
                  ) : (
                    <ProFormCaptcha
                      key="authcode"
                      fieldProps={{
                        size: 'large',
                        prefix: <LockOutlined className={'prefixIcon'} />,
                      }}
                      captchaProps={{
                        size: 'large',
                      }}
                      placeholder={intl.formatMessage({ id: 'login.captchaPlaceholder' })}
                      captchaTextRender={(timing, count) => {
                        if (timing) {
                          return `${count} ${'s'}`;
                        }
                        return intl.formatMessage({ id: 'login.sendCaptcha' });
                      }}
                      name="authcode"
                      phoneName="phone"
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'login.captchaRequired' }),
                        },
                      ]}
                      onGetCaptcha={async (phone) => {
                        const { code } = await sendAuthcodePost({ phone, type: 0 });
                        if (code != 0) {
                          throw new Error(intl.formatMessage({ id: 'login.captchaError' }));
                        } else {
                          message.success(intl.formatMessage({ id: 'login.captchaSent' }));
                        }
                      }}
                    />
                  )}
                </div>
              </LoginForm>
            ) : (
              <ProForm
                key="setProForm"
                submitter={{
                  render: (props, doms) => {
                    return [
                      <div key="buttons" className="flex justify-between">
                        <Button
                          type="primary"
                          className="w-[150px] h-[40px]"
                          key="ok"
                          onClick={() => props.form?.submit?.()}
                        >
                          {intl.formatMessage({ id: 'common.button.confirm' })}
                        </Button>
                        <Button
                          key="cancel"
                          className="w-[150px] h-[40px]"
                          onClick={() => onCancel()}
                        >
                          {intl.formatMessage({ id: 'common.button.cancel' })}
                        </Button>
                      </div>,
                    ];
                  },
                }}
                onFinish={async (values) => {
                  await handleModify(values);
                }}
                form={form}
                className="px-[50px] pb-[40px]"
              >
                <SetPassword title={passTitle} />
              </ProForm>
            )}
          </div>
        </Flex>
        <Footer />
      </Flex>
    </ConfigProvider>
  );
};

export default Login;
