import { md5 } from '@/utils/md5';
import { LockOutlined, TabletOutlined } from '@ant-design/icons';
import { ProFormCaptcha, ProFormText } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { ConfigProvider, message } from 'antd';
import { sendAuthcodePost } from '../services';
const SetPassword: React.FC<{ title: string }> = (props: { title: string }) => {
  const intl = useIntl();
  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#F49C1F',
        },
        components: {
          Form: {
            itemMarginBottom: 16,
          },
        },
      }}
    >
      <span className="text-[20px] font-semibold flex justify-center pt-[16px]">{props.title}</span>
      <div className="mt-4">
        <ProFormText
          name="phone"
          key="setPhone"
          fieldProps={{
            size: 'large',
            prefix: <TabletOutlined />,
          }}
          placeholder={intl.formatMessage({ id: 'login.enterPhoneNumber' })}
          rules={[
            {
              required: true,
              message: intl.formatMessage({ id: 'login.phoneNumberRequired' }),
            },
          ]}
        />
        <ProFormCaptcha
          key="setAuthCode"
          fieldProps={{
            size: 'large',
            prefix: <LockOutlined className={'prefixIcon'} />,
          }}
          captchaProps={{
            size: 'large',
          }}
          placeholder={intl.formatMessage({ id: 'login.captchaPlaceholder' })}
          captchaTextRender={(timing, count) => {
            if (timing) {
              return `${count} ${'s'}`;
            }
            return intl.formatMessage({ id: 'login.sendCaptcha' });
          }}
          name="authCode"
          rules={[
            {
              required: true,
              message: intl.formatMessage({ id: 'login.captchaRequired' }),
            },
          ]}
          phoneName="phone"
          onGetCaptcha={async (phone) => {
            const { code } = await sendAuthcodePost({ phone, type: 1 });
            if (code != 0) {
              throw new Error(intl.formatMessage({ id: 'login.captchaError' }));
            } else {
              message.success(intl.formatMessage({ id: 'login.captchaSent' }));
            }
          }}
        />
        <ProFormText.Password
          name="newPassword"
          key="newPassword"
          fieldProps={{
            size: 'large',
            prefix: <LockOutlined />,
            autoComplete: 'new-password',
            maxLength: 12,
          }}
          placeholder={intl.formatMessage({ id: 'login.enterPassword' })}
          extra={intl.formatMessage({ id: 'login.passwordRule' })}
          rules={[
            {
              required: true,
              message: intl.formatMessage({ id: 'login.passwordRequired' }),
            },
            {
              pattern:
                /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d|.*[\W_]).{8,12}$|^(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,12}$|^(?=.*[a-z])(?=.*\d)(?=.*[\W_]).{8,12}$/,
              message: intl.formatMessage({ id: 'login.passwordRuleError' }),
            },
          ]}
          transform={(value) => md5(value)}
        />
        <ProFormText.Password
          name="confirmPassword"
          key="confirmPassword"
          fieldProps={{
            size: 'large',
            prefix: <LockOutlined />,
            autoComplete: 'new-password',
            maxLength: 12,
          }}
          dependencies={['newPassword']}
          placeholder={intl.formatMessage({ id: 'login.confirmPassword' })}
          rules={[
            {
              required: true,
              message: intl.formatMessage({ id: 'login.confirmPasswordRequired' }),
            },
            {
              pattern:
                /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d|.*[\W_]).{8,12}$|^(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,12}$|^(?=.*[a-z])(?=.*\d)(?=.*[\W_]).{8,12}$/,
              message: intl.formatMessage({ id: 'login.passwordRuleError' }),
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error(intl.formatMessage({ id: 'login.passwordMismatch' })));
              },
            }),
          ]}
          transform={(value) => md5(value)}
        />
      </div>
    </ConfigProvider>
  );
};

export default SetPassword;
