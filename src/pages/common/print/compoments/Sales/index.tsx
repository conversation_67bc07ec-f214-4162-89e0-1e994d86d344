/**
 * 销售单打印模板
 * @constructor
 */
import { useEffect, useState } from 'react';
import { PrintConfig } from '@/pages/common/print';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { useSearchParams } from '@@/exports';
import QRCode from 'qrcode';
import { getOrderByOrderNoForDbReturnSelected } from '@/pages/sales/order/edit/services';
import { Spin } from 'antd';
import { OrderListItemEntity } from '@/pages/sales/order/list/types/order.list.item.entity';
import dayjs from 'dayjs';
import { SalesFields } from '@/pages/system/config/components/Print/types/SalesFields';
import { queryPostDetail } from '@/pages/system/store/services';
import type { PostEntity } from '@/pages/system/store/list/types/post.entity';

export interface SalesPrintProps {
  currentPrintConfig?: PrintConfig[PrintType.salesOrder];
}

const Sales = (props: SalesPrintProps) => {
  const { currentPrintConfig } = props;
  const [loading, setLoading] = useState(false);
  const [orderDetail, setOrderDetail] = useState<OrderListItemEntity>();
  const [storeDetail, setStoreDetail] = useState<PostEntity>();
  const [qrCodeUrl, setQrCodeUrl] = useState<string>();

  console.log('currentPrintConfig', currentPrintConfig);

  const [searchParams] = useSearchParams();
  const orderNo = searchParams.get('orderNo');

  useEffect(() => {
    if (orderNo) {
      setLoading(true);
      getOrderByOrderNoForDbReturnSelected(orderNo)
        .then((result) => {
          if (result) {
            setOrderDetail(result);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [orderNo]);

  useEffect(() => {
    if (orderDetail?.orders?.storeId) {
      queryPostDetail({ id: orderDetail?.orders?.storeId }).then((result) => {
        if (result) {
          setStoreDetail(result);
        }
      });
      QRCode.toDataURL(orderDetail?.orders?.orderNo!, { margin: 1 }).then((result) => {
        setQrCodeUrl(result);
      });
    }
  }, [orderDetail]);

  const getSaleNum = () => {
    let num = 0;
    orderDetail?.orderGoodsROList?.forEach((item) => {
      num += item?.saleNum ?? 0;
    });
    return num;
  };

  if (!orderDetail) return null;

  return (
    <Spin spinning={loading}>
      <div className="print-table">
        <table>
          <tr>
            <td width={100}>
              {currentPrintConfig?.printQrCode && <img width={80} height={80} src={qrCodeUrl} />}
            </td>
            <td className="title">{orderDetail.orders?.storeName}销售单</td>
            <td width={100}></td>
          </tr>
        </table>
        <table className="no-border-top no-border-bottom">
          <tr>
            <td colSpan={2}>购方单位: {orderDetail.orders?.cstName}</td>
            <td style={{ width: '40%' }}>单&emsp;&emsp;号: {orderDetail.orders?.orderNo}</td>
          </tr>
          <tr>
            <td colSpan={2}>
              地&emsp;&emsp;址:{' '}
              {`${orderDetail?.orderFixedAddressList?.[0]?.consigneeProvinceName ?? ''}${
                orderDetail?.orderFixedAddressList?.[0]?.consigneeCityName ?? ''
              }${orderDetail?.orderFixedAddressList?.[0]?.consigneePrefectureName ?? ''}${
                orderDetail?.orderFixedAddressList?.[0]?.consigneeDetail ?? ''
              }`}
            </td>
            <td>打印日期: {dayjs().format('YYYY-MM-DD HH:mm:ss')}</td>
          </tr>
          <tr>
            <td>联&ensp;系&ensp;人: {orderDetail.orderFixedAddressList?.[0]?.consigneeName}</td>
            <td>联系方式: {orderDetail.orderFixedAddressList?.[0]?.consigneePhone}</td>
            <td>制单日期: {orderDetail?.orders?.createTime}</td>
          </tr>
        </table>
        <table className="has-td-border" style={{ textAlign: 'center' }}>
          <thead>
            <tr>
              <td>序号</td>
              <td>商品编码</td>
              <td>商品名称</td>
              {currentPrintConfig?.fields?.includes(SalesFields.OeNo) && <td>OE</td>}
              {currentPrintConfig?.fields?.includes(SalesFields.BrandPartNo) && <td>品牌件号</td>}
              {currentPrintConfig?.fields?.includes(SalesFields.BrandName) && <td>品牌</td>}
              {currentPrintConfig?.fields?.includes(SalesFields.OriginRegionName) && <td>产地</td>}
              {currentPrintConfig?.fields?.includes(SalesFields.Spec) && <td>规格</td>}
              {currentPrintConfig?.fields?.includes(SalesFields.AdaptModel) && <td>车型</td>}
              <td>数量</td>
              <td>单位</td>
              <td>单价(元)</td>
              <td>合计(元)</td>
              <td>库位</td>
            </tr>
          </thead>
          <tbody>
            {orderDetail.orderGoodsROList?.map((item, index: number) => (
              <tr key={item.itemSn}>
                <td>{index + 1}</td>
                <td>{item.itemSn}</td>
                <td>{item.itemName}</td>
                {currentPrintConfig?.fields?.includes(SalesFields.OeNo) && <td>{item.oeNo}</td>}
                {currentPrintConfig?.fields?.includes(SalesFields.BrandPartNo) && (
                  <td>{item.brandPartNo}</td>
                )}
                {currentPrintConfig?.fields?.includes(SalesFields.BrandName) && (
                  <td>{item.brandName}</td>
                )}
                {currentPrintConfig?.fields?.includes(SalesFields.OriginRegionName) && (
                  <td>{item.originRegionName}</td>
                )}
                {currentPrintConfig?.fields?.includes(SalesFields.Spec) && <td>{item.spec}</td>}
                {currentPrintConfig?.fields?.includes(SalesFields.AdaptModel) && (
                  <td>{item.adaptModel}</td>
                )}
                <td>{item.saleNum}</td>
                <td>{item.unitName}</td>
                <td>{item.unitPriceYuan?.toFixed(2)}</td>
                <td>{item.actualSellingTotalAmountYuan?.toFixed(2)}</td>
                <td>{item.locationCode}</td>
              </tr>
            ))}
          </tbody>
        </table>
        <table className="no-border-top has-td-border">
          <tr>
            <td className="no-border-top" style={{ width: '33%' }}>
              结算方式: {orderDetail?.orderPayDetailList?.[0]?.payKindName}
            </td>
            <td className="no-border-top" style={{ width: '33%' }}>
              {orderDetail?.orderPrice?.totalDiscountAmountYuan
                ? `优惠内容: ${orderDetail?.orderPrice?.totalDiscountAmountYuan?.toFixed(2)}`
                : ''}
            </td>
            <td className="no-border-top" style={{ width: '33%' }}>
              商品总计(元): {orderDetail?.orderPrice?.totalGoodsPriceAmountYuan?.toFixed(2)}
            </td>
          </tr>
          <tr>
            <td>商品数量: {getSaleNum()}</td>
            <td>运&emsp;&emsp;费: {orderDetail?.orderPrice?.deliveryAmountYuan?.toFixed(2)}</td>
            <td>
              总&emsp;&emsp;计(元):{' '}
              {orderDetail?.orderPrice?.shouldTotalOrderAmountYuan?.toFixed(2)}
            </td>
          </tr>
        </table>
        <table className="no-border-top">
          <tr>
            <td style={{ width: '33%' }}>制单人: {orderDetail?.orders?.salesman}</td>
            <td style={{ width: '33%' }}>
              发货仓库: {orderDetail?.orderFixedDistributionList?.[0]?.warehouseName}
            </td>
            <td style={{ width: '33%' }}>电话: {storeDetail?.contactPhone}</td>
          </tr>
          <tr>
            <td colSpan={2}>
              地&emsp;址: {storeDetail?.provinceName}
              {storeDetail?.cityName}
              {storeDetail?.districtName}
              {storeDetail?.detailAddress}
            </td>
            <td>
              客户签字:
              <span
                style={{
                  borderBottom: '1px solid #000000',
                  display: 'inline-block',
                  width: '80px',
                }}
              >
                &ensp;
              </span>
            </td>
          </tr>
          {orderDetail?.orderNoteList?.[0]?.noteDetail && (
            <tr>
              <td colSpan={3}>备注: {orderDetail?.orderNoteList?.[0]?.noteDetail}</td>
            </tr>
          )}
        </table>
        {currentPrintConfig?.remark && (
          <table className="no-border-top">{currentPrintConfig?.remark}</table>
        )}
      </div>
    </Spin>
  );
};

export default Sales;
