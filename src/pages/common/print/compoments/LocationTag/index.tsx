import { useSearchParams } from '@@/exports';
import React, { useState } from 'react';
import { Spin } from 'antd';
import { generateQRCodeDataURLs } from '@/pages/common/print/compoments/GoodTag';
import { useAsyncEffect } from 'ahooks';

export interface PrintLocationItems {
  code: string;
  remark?: string;
  warehouseName?: string;
}

export interface LocationTagPrintContent {
  content: React.ReactNode;
}

export interface LocationTagProps {
  setPrintLocationList: (goodList: LocationTagPrintContent[]) => void;
}

const LocationTag = (props: LocationTagProps) => {
  const [searchParams] = useSearchParams();
  const locationsStr = searchParams.get('locations');
  const [loading, setLoading] = useState(false);
  const [printContent, setPrintContent] = useState<LocationTagPrintContent[]>([]);
  const locations = JSON.parse(locationsStr ?? '[]') as PrintLocationItems[];

  useAsyncEffect(async () => {
    const content = [] as LocationTagPrintContent[];
    const qrCodes = await generateQRCodeDataURLs(locations.map((item) => item.code));
    locations.forEach((item, index) => {
      content.push({
        content: (
          <div className="print-table" style={{ padding: '10px' }}>
            <table style={{ border: 'none' }}>
              <tbody>
                <tr>
                  <td style={{ verticalAlign: 'top' }} width={100}>
                    <img src={qrCodes?.[index]} style={{ width: '100px', height: '100px' }} />
                  </td>
                  <td style={{ verticalAlign: 'top' }}>
                    <div style={{ lineHeight: '20px', fontSize: '14px', paddingLeft: '10px' }}>
                      <div style={{ fontSize: '16px', marginBottom: '5px', fontWeight: 'bold' }}>
                        {item.code}
                      </div>
                      <div>备注：{item.remark}</div>
                      <div>仓库：{item.warehouseName}</div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        ),
      });
    });
    setPrintContent(content);
    props.setPrintLocationList(content);
  }, [locations.length]);

  if (printContent?.length === 0) {
    return null;
  }

  return (
    <Spin spinning={loading}>
      <div className="flex gap-[20px] flex-wrap">
        {printContent.map((item) => (
          <div className="flex flex-col">
            <div className="border border-dashed border-gray-500 p-[10px]">{item.content}</div>
          </div>
        ))}
      </div>
    </Spin>
  );
};

export default LocationTag;
