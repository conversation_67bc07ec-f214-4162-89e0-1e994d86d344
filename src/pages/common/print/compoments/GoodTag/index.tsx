import { useSearchParams } from '@@/exports';
import React, { useEffect, useState } from 'react';
import QRCode from 'qrcode';
import { PrintItems } from '@/components/GoodsPrintDrawer';
import { queryStoreGoodsPage } from '@/components/GoodsSearch/services';
import { Spin } from 'antd';

export interface GoodTagPrintContent {
  content: React.ReactNode;
  number: number;
}

export interface GoodTagProps {
  setPrintGoodList: (goodList: GoodTagPrintContent[]) => void;
}

/**
 * 批量生成二维码
 * @param dataList
 */
export const generateQRCodeDataURLs = async (dataList: string[]) => {
  const promises = dataList.map((data) => QRCode.toDataURL(data, { margin: 0 }));
  try {
    const dataURLs = await Promise.all(promises);
    return dataURLs;
  } catch (error) {
    console.error('Error generating QR codes:', error);
  }
};

const GoodTag = (props: GoodTagProps) => {
  const [searchParams] = useSearchParams();
  const goodsStr = searchParams.get('goods');
  const [loading, setLoading] = useState(false);
  const [printContent, setPrintContent] = useState<GoodTagPrintContent[]>([]);
  const goods = JSON.parse(goodsStr ?? '[]') as PrintItems[];

  useEffect(() => {
    if (goods?.length) {
      setLoading(true);
      queryStoreGoodsPage({
        pageNo: 1,
        pageSize: 999,
        itemSnList: goods.map((item) => item.itemSn),
      })
        .then(async (result) => {
          if (result) {
            const content = [] as GoodTagPrintContent[];
            const qrCodes = await generateQRCodeDataURLs(result.data?.map((item) => item.itemId));
            result.data?.forEach((item, index) => {
              const number = goods.find((n) => n.itemSn === item.itemSn)?.number ?? 0;
              content.push({
                content: (
                  <div className="print-table" style={{ padding: '10px' }}>
                    <table style={{ border: 'none' }}>
                      <tbody>
                        <tr>
                          <td width={100}>
                            <img
                              src={qrCodes?.[index]}
                              style={{ width: '100px', height: '100px' }}
                            />
                          </td>
                          <td style={{ verticalAlign: 'top' }}>
                            <div style={{ lineHeight: '20px', fontSize: '14px' }}>
                              <div
                                style={{
                                  fontSize: '16px',
                                  marginBottom: '5px',
                                  fontWeight: 'bold',
                                }}
                              >
                                {item.itemName}
                              </div>
                              <div>商品编码：{item.itemSn}</div>
                              <div>品牌：{item.brandName}</div>
                              <div>车型：{item.adaptSeries}</div>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                ),
                number,
              });
            });
            setPrintContent(content);
            props.setPrintGoodList(content);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [goods.length]);

  if (printContent?.length === 0) {
    return null;
  }

  return (
    <Spin spinning={loading}>
      <div className="flex gap-[20px] flex-wrap">
        {printContent.map((item) => (
          <div className="flex flex-col">
            <div className="border border-dashed border-gray-500 p-[10px]">{item.content}</div>
            <div className="text-center mt-[10px]">
              打印份数:<span className="text-red-500 font-bold mx-[10px]">{item.number}</span>份
            </div>
          </div>
        ))}
      </div>
    </Spin>
  );
};

export default GoodTag;
