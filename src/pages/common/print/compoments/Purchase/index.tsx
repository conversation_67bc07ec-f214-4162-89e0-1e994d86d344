/**
 * 采购单打印模板
 * @constructor
 */
import { useEffect, useState } from 'react';
import { PrintConfig } from '@/pages/common/print';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { useSearchParams } from '@@/exports';
import QRCode from 'qrcode';
import { Spin } from 'antd';
import dayjs from 'dayjs';
import {
  queryPurchaseLinePagePost,
  queryPurchaseOrderDetailPost,
} from '@/pages/purchase/detail/services';
import { PurchasePostEntity } from '@/pages/purchase/detail/types/purchase.post.entity';
import { LinePostEntity } from '@/pages/purchase/detail/types/line.post.entity';
// @ts-ignore
import nzhcn from 'nzh/cn';

export interface PurchasePrintProps {
  currentPrintConfig?: PrintConfig[PrintType.purchaseOrder];
}

const Purchase = (props: PurchasePrintProps) => {
  const { currentPrintConfig } = props;
  const [loading, setLoading] = useState(false);
  const [orderDetail, setOrderDetail] = useState<PurchasePostEntity>();
  const [goodsList, setGoodsList] = useState<LinePostEntity[]>([]);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>();

  console.log('currentPrintConfig', currentPrintConfig);

  const [searchParams] = useSearchParams();
  const purchaseId = searchParams.get('purchaseId');

  useEffect(() => {
    if (purchaseId) {
      setLoading(true);
      queryPurchaseOrderDetailPost({ id: purchaseId })
        .then((result) => {
          if (result) {
            setOrderDetail(result);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [purchaseId]);

  useEffect(() => {
    if (orderDetail) {
      queryPurchaseLinePagePost({ orderNo: orderDetail.orderNo, pageNo: 1, pageSize: 9999 }).then(
        (result) => {
          setGoodsList(result.data ?? []);
        },
      );
      QRCode.toDataURL(orderDetail?.orderNo!, { margin: 1 }).then((result) => {
        setQrCodeUrl(result);
      });
    }
  }, [orderDetail]);

  if (!orderDetail) return null;

  return (
    <Spin spinning={loading}>
      <div className="print-table">
        <table>
          <tr>
            <td width={100}>
              {currentPrintConfig?.printQrCode && <img width={80} height={80} src={qrCodeUrl} />}
            </td>
            <td className="title">{orderDetail?.storeName}采购单</td>
            <td width={100}></td>
          </tr>
        </table>
        <table className="no-border-top no-border-bottom">
          <tr>
            <td>供应商: {orderDetail?.supplierName}</td>
            <td>单&emsp;&emsp;号: {orderDetail?.orderNo}</td>
            <td>制单人: {orderDetail?.purchaseUser}</td>
          </tr>
          <tr>
            <td>制单日期: {orderDetail?.orderTime}</td>
            <td>打印日期: {dayjs().format('YYYY-MM-DD HH:mm:ss')}</td>
            <td>收货仓库: {orderDetail?.receiveWarehouseName}</td>
          </tr>
          {orderDetail?.remark && (
            <tr>
              <td colSpan={3}>备注: {orderDetail?.remark}</td>
            </tr>
          )}
        </table>
        <table className="has-td-border" style={{ textAlign: 'center' }}>
          <thead>
            <tr>
              <td>序号</td>
              <td>商品编码</td>
              <td>商品名称</td>
              <td>OE</td>
              <td>品牌件号</td>
              <td>品牌</td>
              <td>数量</td>
              <td>单位</td>
              <td>单价(元)</td>
              <td>合计(元)</td>
              <td>库位</td>
            </tr>
          </thead>
          <tbody>
            {goodsList?.map((item, index: number) => (
              <tr key={item.itemSn}>
                <td>{index + 1}</td>
                <td>{item.itemSn}</td>
                <td>{item.skuName}</td>
                <td>{item.oe}</td>
                <td>{item.brandPartNo}</td>
                <td>{item.brandName}</td>
                <td>{item.num}</td>
                <td>{item.unit}</td>
                <td>{item.price?.toFixed(2)}</td>
                <td>{item.amount?.toFixed(2)}</td>
                <td>{item.locationCode}</td>
              </tr>
            ))}
          </tbody>
        </table>
        <table className="no-border-top has-td-border">
          <tr>
            <td className="no-border-top">结算方式: {orderDetail?.paySubTypeList?.[0].desc}</td>
            <td className="no-border-top">商品数量: {orderDetail?.sumQuantity}</td>
            <td className="no-border-top">
              总计(元): {nzhcn.toMoney(orderDetail?.sumAmount, { outSymbol: false })}{' '}
              {orderDetail?.sumAmount?.toFixed(2)}
            </td>
          </tr>
        </table>
        {currentPrintConfig?.remark && (
          <table className="no-border-top">{currentPrintConfig?.remark}</table>
        )}
      </div>
    </Spin>
  );
};

export default Purchase;
