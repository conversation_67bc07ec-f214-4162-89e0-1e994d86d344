/**
 * 出库单打印模板
 * @constructor
 */
import { useEffect, useState } from 'react';
import { PrintConfig } from '@/pages/common/print';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { useSearchParams } from '@@/exports';
import QRCode from 'qrcode';
import { Spin } from 'antd';
import dayjs from 'dayjs';
import { queryOutPutDetailPost } from '@/pages/stocks/output/services';
import { OutPutDetailPostEntity } from '@/pages/stocks/output/detail/types/output.detail.post.entity';

export interface OutStockPrintProps {
  currentPrintConfig?: PrintConfig[PrintType.outStockOrder];
}

const OutStock = (props: OutStockPrintProps) => {
  const { currentPrintConfig } = props;
  const [loading, setLoading] = useState(false);
  const [orderDetail, setOrderDetail] = useState<OutPutDetailPostEntity>();
  const [qrCodeUrl, setQrCodeUrl] = useState<string>();

  console.log('currentPrintConfig', currentPrintConfig);

  const [searchParams] = useSearchParams();
  const stockOutId = searchParams.get('stockOutId');

  useEffect(() => {
    if (stockOutId) {
      setLoading(true);
      queryOutPutDetailPost({ stockOutId })
        .then((result) => {
          if (result) {
            setOrderDetail(result);
            QRCode.toDataURL(result.stockOutRo?.bizBillNo!, { margin: 1 }).then((result) => {
              setQrCodeUrl(result);
            });
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [stockOutId]);

  if (!orderDetail) return null;

  return (
    <Spin spinning={loading}>
      <div className="print-table">
        <table>
          <tr>
            <td width={100}>
              {currentPrintConfig?.printQrCode && <img width={80} height={80} src={qrCodeUrl} />}
            </td>
            <td className="title">{orderDetail.stockOutRo?.warehouseName}出库单</td>
            <td width={100}></td>
          </tr>
        </table>
        <table className="no-border-top no-border-bottom">
          <tr>
            <td>收&ensp;货&ensp;方: {orderDetail.stockOutRo?.customer}</td>
            <td>业务单号: {orderDetail.stockOutRo?.origBillNo}</td>
            <td>单&emsp;&emsp;号: {orderDetail.stockOutRo?.bizBillNo}</td>
          </tr>
          <tr>
            <td>业务类型: {orderDetail.stockOutRo?.billTypeDesc}</td>
            <td>地&emsp;&emsp;址: {orderDetail.stockOutRo?.deliveryAddress}</td>
            <td>通知出库: {orderDetail.stockOutRo?.createTime}</td>
          </tr>
        </table>
        <table className="has-td-border" style={{ textAlign: 'center' }}>
          <thead>
            <tr>
              <td>序号</td>
              <td>商品编码</td>
              <td>商品名称</td>
              <td>OE</td>
              <td>品牌件号</td>
              <td>品牌</td>
              <td>数量</td>
              <td>单位</td>
              <td>库位</td>
            </tr>
          </thead>
          <tbody>
            {orderDetail.stockOutDetailRoList?.map((item, index: number) => (
              <tr key={item.itemSn}>
                <td>{index + 1}</td>
                <td>{item.itemSn}</td>
                <td>{item.itemName}</td>
                <td>{item.oeNo}</td>
                <td>{item.brandPartNo}</td>
                <td>{item.brandName}</td>
                <td>{item.realAmount}</td>
                <td>{item.unitName}</td>
                <td>{item.code}</td>
              </tr>
            ))}
          </tbody>
        </table>
        <table className="no-border-top">
          <tr>
            <td style={{ width: '30%' }}>打印日期: {dayjs().format('YYYY-MM-DD HH:mm:ss')}</td>
            <td style={{ width: '20%' }}>出库仓库: {orderDetail.stockOutRo?.warehouseName}</td>
            <td style={{ width: '20%' }}>
              捡&ensp;货&ensp;人:{' '}
              <span
                style={{
                  borderBottom: '1px solid #000000',
                  display: 'inline-block',
                  width: '80px',
                }}
              >
                &ensp;
              </span>
            </td>
            <td style={{ width: '20%' }}>
              验货人:{' '}
              <span
                style={{
                  borderBottom: '1px solid #000000',
                  display: 'inline-block',
                  width: '80px',
                }}
              >
                &ensp;
              </span>
            </td>
          </tr>
          <tr>
            <td style={{ width: '20%' }}>
              配送方式: {orderDetail?.stockOutRo?.distributionModeDesc}
            </td>
            <td style={{ width: '20%' }}>
              物流公司: {orderDetail?.stockOutRo?.logisticsCompanyName}
            </td>
            <td colSpan={2} style={{ width: '20%' }}>
              物流单号: {orderDetail?.stockOutRo?.logisticsNo}
            </td>
          </tr>
        </table>
        {currentPrintConfig?.remark && (
          <table className="no-border-top">{currentPrintConfig?.remark}</table>
        )}
      </div>
    </Spin>
  );
};

export default OutStock;
