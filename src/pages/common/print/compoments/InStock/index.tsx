/**
 * 入库单打印模板
 * @constructor
 */
import { useEffect, useState } from 'react';
import { PrintConfig } from '@/pages/common/print';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { useSearchParams } from '@@/exports';
import QRCode from 'qrcode';
import { Spin } from 'antd';
import dayjs from 'dayjs';
import { queryInPutDetailPost } from '@/pages/stocks/input/services';
import { InPutDetailPostEntity } from '@/pages/stocks/input/detail/types/input.detail.post.entity';

export interface InStockPrintProps {
  currentPrintConfig?: PrintConfig[PrintType.inStockOrder];
}

const InStock = (props: InStockPrintProps) => {
  const { currentPrintConfig } = props;
  const [loading, setLoading] = useState(false);
  const [orderDetail, setOrderDetail] = useState<InPutDetailPostEntity>();
  const [qrCodeUrl, setQrCodeUrl] = useState<string>();

  console.log('currentPrintConfig', currentPrintConfig);

  const [searchParams] = useSearchParams();
  const stockInId = searchParams.get('stockInId');

  useEffect(() => {
    if (stockInId) {
      setLoading(true);
      queryInPutDetailPost({ stockInId })
        .then((result) => {
          if (result) {
            setOrderDetail(result);
            QRCode.toDataURL(result.stockInRo?.bizBillNo!, { margin: 1 }).then((result) => {
              setQrCodeUrl(result);
            });
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [stockInId]);

  if (!orderDetail) return null;

  return (
    <Spin spinning={loading}>
      <div className="print-table">
        <table>
          <tr>
            <td width={100}>
              <img width={80} height={80} src={qrCodeUrl} />
            </td>
            <td className="title">{orderDetail.stockInRo?.warehouseName}入库单</td>
            <td width={100}></td>
          </tr>
        </table>
        <table className="no-border-top no-border-bottom">
          <tr>
            <td>发&ensp;货&ensp;方: {orderDetail.stockInRo?.promoter}</td>
            <td>业务单号: {orderDetail.stockInRo?.origBillNo}</td>
            <td>单&emsp;&emsp;号: {orderDetail.stockInRo?.bizBillNo}</td>
          </tr>
          <tr>
            <td>业务类型: {orderDetail.stockInRo?.billTypeDesc}</td>
            <td>通知入库: {orderDetail.stockInRo?.createTime}</td>
            <td>打印日期: {dayjs().format('YYYY-MM-DD HH:mm:ss')}</td>
          </tr>
        </table>
        <table className="has-td-border" style={{ textAlign: 'center' }}>
          <thead>
            <tr>
              <td>序号</td>
              <td>商品编码</td>
              <td>商品名称</td>
              <td>OE</td>
              <td>品牌件号</td>
              <td>品牌</td>
              <td>数量</td>
              <td>单位</td>
              <td>库位</td>
            </tr>
          </thead>
          <tbody>
            {orderDetail.stockInDetailRoList?.map((item, index: number) => (
              <tr key={item.itemSn}>
                <td>{index + 1}</td>
                <td>{item.itemSn}</td>
                <td>{item.itemName}</td>
                <td>{item.oeNo}</td>
                <td>{item.brandPartNo}</td>
                <td>{item.brandName}</td>
                <td>{item.realAmount}</td>
                <td>{item.unitName}</td>
                <td>{item.code}</td>
              </tr>
            ))}
          </tbody>
        </table>
        <table className="no-border-top">
          <tr>
            <td style={{ width: '33%' }}>入库仓库: {orderDetail.stockInRo?.warehouseName}</td>
            <td style={{ width: '33%' }}>
              验&ensp;货&ensp;人:{' '}
              <span
                style={{
                  borderBottom: '1px solid #000000',
                  display: 'inline-block',
                  width: '80px',
                }}
              >
                &ensp;
              </span>
            </td>
            <td style={{ width: '33%' }}>
              入库人:{' '}
              <span
                style={{
                  borderBottom: '1px solid #000000',
                  display: 'inline-block',
                  width: '80px',
                }}
              >
                &ensp;
              </span>
            </td>
          </tr>
        </table>
        {currentPrintConfig?.remark && (
          <table className="no-border-top">{currentPrintConfig?.remark}</table>
        )}
      </div>
    </Spin>
  );
};

export default InStock;
