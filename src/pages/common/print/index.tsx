/**
 * 远程打印功能
 * 适用于PC/小程序/PDA
 * 1、在移动端以IFrame的形式内嵌此页面，URL传递打印参数
 * 2、此页面接收到打印参数后在前端拼接打印内容
 * 3、点击打印按钮调用远程机器的C-Lodop插件进行打印，打印机安装在远程机器上
 * 4、纸张大小 A4: 210mm*280mm，二等分: 210mm*140mm, 三等分：210mm*93mm
 * 5、本地开发时需要适用IP访问此页面才会正确加载打印的CSS
 */
import { useEffect, useRef, useState } from 'react';
import { Button, message, Modal, Select, Spin } from 'antd';
import { useTitle } from 'ahooks';
// @ts-ignore
import ReactDOMServer from 'react-dom/server.browser';
import Sales from './compoments/Sales';
import useStyle from '@/hooks/useStyle';
import { useSearchParams } from '@@/exports';
import { PrintType, PrintTypeName } from '@/pages/system/config/components/Print/types/PrintType';
import { querySysPropertyList } from '@/services/systerm';
import { ConfigType } from '@/pages/system/config/components/types/ConfigType';
import { PaperType, PaperTypeSize } from '@/pages/system/config/components/Print/types/PaperType';
import { SalesFields } from '@/pages/system/config/components/Print/types/SalesFields';
import SalesReturn from '@/pages/common/print/compoments/SalesReturn';
import OutStock from '@/pages/common/print/compoments/OutStock';
import InStock from '@/pages/common/print/compoments/InStock';
import Purchase from '@/pages/common/print/compoments/Purchase';
import PurchaseReturn from '@/pages/common/print/compoments/PurchaseReturn';
import CheckStock from '@/pages/common/print/compoments/CheckStock';
import GoodTag, { GoodTagPrintContent } from '@/pages/common/print/compoments/GoodTag';
import LocationTag, { LocationTagPrintContent } from '@/pages/common/print/compoments/LocationTag';
import { getPrinterTypeLocalStorageName } from '@/pages/system/config/components/Print/types/PrinterType';

export interface PrintConfig {
  printIP: string;
  [PrintType.salesOrder]: {
    paperType: PaperType;
    fields: SalesFields;
    printQrCode: boolean;
    remark?: string;
  };
  [PrintType.salesReturnOrder]: {
    paperType: PaperType;
    printQrCode: boolean;
    remark?: string;
  };
  [PrintType.inStockOrder]: {
    paperType: PaperType;
    printQrCode: boolean;
    remark?: string;
  };
  [PrintType.outStockOrder]: {
    paperType: PaperType;
    printQrCode: boolean;
    remark?: string;
  };
  [PrintType.checkOrder]: {
    paperType: PaperType;
    printQrCode: boolean;
    remark?: string;
  };
  [PrintType.purchaseOrder]: {
    paperType: PaperType;
    printQrCode: boolean;
    remark?: string;
  };
  [PrintType.purchaseReturnOrder]: {
    paperType: PaperType;
    printQrCode: boolean;
    remark?: string;
  };
  [PrintType.goodTag]: {
    paperType: PaperType;
  };
  [PrintType.locationTag]: {
    paperType: PaperType;
  };
}

const Print = () => {
  const LODOP = useRef<any>();
  const [scriptLoaded, setScriptLoaded] = useState(false);
  const [loading, setLoading] = useState(false);
  const [printing, setPrinting] = useState<boolean>(false);
  const [printConfig, setPrintConfig] = useState<PrintConfig>();
  const [printList, setPrintList] = useState<string[]>([]);
  const [currentPrintName, setCurrentPrintName] = useState<string>();

  const [printGoodList, setPrintGoodList] = useState<GoodTagPrintContent[]>([]);
  const [printLocationList, setPrintLocationList] = useState<LocationTagPrintContent[]>([]);

  const [searchParams] = useSearchParams();
  const printType = searchParams.get('printType') as PrintType;

  // 当前的打印机配置
  const currentPrintConfig: any = printConfig?.[printType];

  useTitle('打印');
  useStyle('/print.css');

  console.log('printConfig', printConfig);

  useEffect(() => {
    if (scriptLoaded) {
      initLODOP();
      getPrintList();
    }
  }, [scriptLoaded]);

  /**
   * 查询打印配置
   */
  const queryConfig = () => {
    setLoading(true);
    querySysPropertyList({ type: ConfigType.Print, propDimensions: 'ACCOUNT' })
      .then((result) => {
        if (result?.[0]?.value) {
          const data = JSON.parse(result?.[0]?.value);
          setPrintConfig(data);
        } else {
          alert('请到系统设置配置打印机信息');
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  /**
   * 初始化打印机
   */
  const initLODOP = () => {
    // 获取打印程序实例
    LODOP.current = getCLodop();
    // 注册
    LODOP.current?.SET_LICENSES('', 'DF8C11FA96795AF737BCD5E27B28AD96321', '', '');
    // 检查是否已经连上打印程序
    if (!LODOP) {
      alert('CLodop 加载失败，请确保已正确安装并启动 CLodop 控件！');
    }
  };

  /**
   * 获取打印机名称列表
   */
  const getPrintList = () => {
    const printList = [];
    const printNumber = LODOP.current.GET_PRINTER_COUNT();
    for (let i = 0; i < printNumber; i++) {
      printList.push(LODOP.current.GET_PRINTER_NAME(i));
    }
    console.log('打印机列表', printList);
    setPrintList(printList);

    /**
     * 设置默认打印机
     */
    const defaultPrinterName = localStorage.getItem(getPrinterTypeLocalStorageName(printType));
    if (printList.length === 1) {
      setCurrentPrintName(printList[0]);
    } else if (defaultPrinterName && printList.includes(defaultPrinterName)) {
      setCurrentPrintName(defaultPrinterName);
    }
  };

  useEffect(() => {
    let script = document.createElement('script');
    script.src = 'http://localhost:8000/CLodopFuncs.js';
    // script.src = 'http://**************:8000/CLodopFuncs.js';
    // script.src = 'http://**************:8000/CLodopFuncs.js';
    script.async = true;
    script.onload = () => {
      initLODOP();
      getPrintList();
    };
    script.onerror = () => {
      Modal.info({
        title: '错误提示',
        width: 740,
        content: (
          <div>
            <div>未检测到本机的CLODOP打印插件，可能原因：</div>
            <div>
              1、插件未启动，请到本机程序列表手动启动CLODOP，启动后插件图标会显示在电脑右下角
            </div>
            <div>
              2、本机电脑未安装CLODOP，请
              <a href="https://www.lodop.net/download.html" target="_blank">
                点击此处
              </a>
              下载安装，版本为：Windows64版/Web打印服务C-Lodop
            </div>
          </div>
        ),
      });
    };
    document.body.appendChild(script);
    return () => {
      document.body.removeChild(script);
    };
  }, []);

  useEffect(() => {
    queryConfig();
  }, []);

  /**
   * 打印任务
   */
  const handlePrint = async () => {
    if (!currentPrintName) {
      message.warning('请先选择打印机');
      return;
    }
    // 获得当前打印配置
    let currentPrintConfig = printConfig?.[printType];
    console.log('currentPrintConfig', currentPrintConfig);
    // 准备打印任务
    setPrinting(true);
    // 初始化打印任务
    LODOP.current.PRINT_INIT(PrintTypeName[PrintType[printType]]);
    // 指定打印机
    LODOP.current.SET_PRINTER_INDEX(currentPrintName);
    // 获取纸张设置
    // @ts-ignore
    const pageSizeConfig = PaperTypeSize[PaperType[currentPrintConfig?.paperType]];
    // 打印内容
    switch (printType) {
      case PrintType.goodTag:
        printGoodList.forEach(function (item) {
          // 初始化一个新任务
          LODOP.current.NewPage();
          // 指定纸张
          LODOP.current.SET_PRINT_PAGESIZE(1, 0, 0, '');
          // 设置打印 HTML 内容
          LODOP.current.ADD_PRINT_HTM(
            0,
            0,
            '100%',
            '100%',
            `<link href="/print.css" rel="stylesheet" type="text/css"><body>${ReactDOMServer.renderToStaticMarkup(
              item.content,
            )}</body>`,
          );
          // 设置打印份数
          LODOP.current.SET_PRINT_COPIES(item.number);
          print();
        });
        break;
      case PrintType.locationTag:
        printLocationList.forEach(function (item) {
          // 初始化一个新任务
          LODOP.current.NewPage();
          // 指定纸张
          LODOP.current.SET_PRINT_PAGESIZE(1, 0, 0, '');
          // 设置打印 HTML 内容
          LODOP.current.ADD_PRINT_HTM(
            0,
            0,
            '100%',
            '100%',
            `<link href="/print.css" rel="stylesheet" type="text/css"><body>${ReactDOMServer.renderToStaticMarkup(
              item.content,
            )}</body>`,
          );
        });
        print();
        break;
      default:
        // 指定纸张
        LODOP.current.SET_PRINT_PAGESIZE(1, pageSizeConfig[0], pageSizeConfig[1], '');
        LODOP.current.ADD_PRINT_HTM(
          0,
          0,
          '100%',
          pageSizeConfig[1],
          `<link href="/print.css" rel="stylesheet" type="text/css"><body>${
            window.document.getElementById('print')?.innerHTML
          }</body>`,
        );
        print();
    }
  };

  const print = () => {
    // 预览或者打印
    // LODOP.current.PREVIEW();
    LODOP.current.PRINT();
    setTimeout(() => {
      setPrinting(false);
    }, 3000);
  };

  return (
    <Spin spinning={loading}>
      <div className="h-screen">
        <div className="w-[230mm] mx-auto bg-white mb-[20px]">
          <div className="flex p-[20px] items-center">
            <div className="mr-[10px]">选择打印机</div>
            <div>
              <Select
                style={{ width: '300px' }}
                value={currentPrintName}
                placeholder="请选择"
                options={printList.map((item) => ({ label: item, value: item }))}
                onChange={(value) => {
                  setCurrentPrintName(value);
                  localStorage.setItem(getPrinterTypeLocalStorageName(printType), value);
                }}
              />
            </div>
          </div>
        </div>

        <div className="w-[230mm] mx-auto bg-white" id="print">
          <div style={{ padding: '20px' }}>
            {printType === PrintType.salesOrder && (
              <Sales currentPrintConfig={currentPrintConfig} />
            )}
            {printType === PrintType.salesReturnOrder && (
              <SalesReturn currentPrintConfig={currentPrintConfig} />
            )}
            {printType === PrintType.outStockOrder && (
              <OutStock currentPrintConfig={currentPrintConfig} />
            )}
            {printType === PrintType.inStockOrder && (
              <InStock currentPrintConfig={currentPrintConfig} />
            )}
            {printType === PrintType.purchaseOrder && (
              <Purchase currentPrintConfig={currentPrintConfig} />
            )}
            {printType === PrintType.purchaseReturnOrder && (
              <PurchaseReturn currentPrintConfig={currentPrintConfig} />
            )}
            {printType === PrintType.checkOrder && (
              <CheckStock currentPrintConfig={currentPrintConfig} />
            )}
            {printType === PrintType.goodTag && <GoodTag setPrintGoodList={setPrintGoodList} />}
            {printType === PrintType.locationTag && (
              <LocationTag setPrintLocationList={setPrintLocationList} />
            )}
          </div>
        </div>

        <div className="fixed bottom-12 left-2 right-2 text-center">
          <Button type={'primary'} size={'large'} onClick={handlePrint} loading={printing}>
            打印
          </Button>
        </div>
      </div>
    </Spin>
  );
};

export default Print;
