// src/App.jsx (或者 H5 的某个具体页面)
import { message } from 'antd';
import { useEffect, useState } from 'react';
import PageRenderer from './components/PageRenderer';

function App() {
    const [pageContent, setPageContent] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchPage = async () => {
            try {
                const savedState = localStorage.getItem('craftjs-editor-state');
                if (savedState) {
                    const json = JSON.parse(savedState);
                    console.log('json', json)
                    const parsedData = JSON.parse(json);
                    setPageContent(parsedData);
                    // setPageContent(data);
                    message.success('页面布局加载成功');
                } else {
                    message.warning('没有找到保存的布局');
                }


                // 假设 pageId 是从 URL 或其他地方获取的
                // const pageId = 'your-page-id'; // 替换为实际的页面 ID
                // const response = await fetch(`/api/pages/${pageId}`); // 调用后端 API
                // if (!response.ok) {
                //     throw new Error(`HTTP error! status: ${response.status}`);
                // }
                // const data = await response.json(); // Craft.js 序列化的 JSON 对象

                // Craft.js editor.query.serialize() 的结果本身就是一个对象
                // 不需要再 JSON.parse(data)，除非后端返回的是字符串化的 JSON
                // 如果后端返回的是 JSON 字符串，则需要：


            } catch (e) {
                console.error("Failed to load page content:", e);
                setError(e.message);
            } finally {
                setLoading(false);
            }
        };

        fetchPage();
    }, []); // 空依赖数组，仅在组件挂载时运行

    if (loading) return <p>Loading page...</p>;
    if (error) return <p>Error loading page: {error}</p>;
    if (!pageContent) return <p>No page content found.</p>;

    return (
        <div className="h5-page-container">
            <h1>My Awesome Activity Page</h1>
            <PageRenderer pageData={pageContent} />
        </div>
    );
}

export default App;