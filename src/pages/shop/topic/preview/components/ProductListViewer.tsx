import { queryStoreGoodsPage } from '@/components/GoodsSearch/services';
import { ShoppingCartOutlined } from '@ant-design/icons';
import { Alert, Button, Card, Col, Empty, Row, Spin } from 'antd';
import { useEffect, useState } from 'react';

export type ProductItem = {
    id: string;
    name: string;
    price: string; // 当前价格
    image: string;
    link: string; // 商品链接
    stockStatus: string; // 库存状态
    description?: string;
};



// 模拟API调用
// 注意：实际项目中，您需要替换为真实的后端API接口。
// 后端API应能根据商品ID列表返回包含名称、图片、当前价格、库存状态、链接等信息的商品详情。
const fetchProductDetailsByIds = (ids: string[]): Promise<ProductItem[]> => {
    return queryStoreGoodsPage({
        "pageSize": 10, "pageNo": 1, "isFetchAllInventory": true
    }).then((res) => res.data)
};

export type ProductListProps = {
    title?: string; // 新增：列表标题
    productIds: string[];
    layout?: 'one-column' | 'two-columns'; // 修改：布局方式，一行一列或一行两列
    background: string;
    padding: string[];
    border: string;
    borderRadius: string;
    shadow: 'none' | 'sm' | 'md' | 'lg' | 'xl';
    showAddToCartButton: boolean;
    buttonText: string;
    titleColor: string;
    priceColor: string;
    buttonColor: string;
    buttonTextColor: string;
};

export const ProductListViewer = ({
    title, // 新增
    productIds = [],
    layout = 'two-columns', // 修改
    background = '#ffffff',
    padding = ['10', '10', '10', '10'],
    border = '1px solid #e5e7eb',
    borderRadius = '8px',
    shadow = 'md',
    showAddToCartButton = true,
    buttonText = '加入购物车',
    titleColor = '#333333',
    priceColor = '#ff4d4f',
    buttonColor = '#ff4d4f',
    buttonTextColor = '#ffffff',
}: ProductListProps) => {

    const [displayProducts, setDisplayProducts] = useState<ProductItem[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (productIds && productIds.length > 0) {
            setLoading(true);
            setError(null);
            fetchProductDetailsByIds(productIds)
                .then(data => {
                    setDisplayProducts(data);
                })
                .catch(err => {
                    console.error('Error fetching product details:', err);
                    setError('获取商品信息失败，请稍后重试。');
                    setDisplayProducts([]); // 清空商品，避免显示旧数据
                })
                .finally(() => {
                    setLoading(false);
                });
        } else {
            setDisplayProducts([]); // 如果没有IDs，清空列表
        }
    }, [productIds]); // 依赖 productIds

    const paddingStyle = `${padding[0]}px ${padding[1]}px ${padding[2]}px ${padding[3]}px`;
    const shadowClass = {
        'none': '',
        'sm': 'shadow-sm',
        'md': 'shadow-md',
        'lg': 'shadow-lg',
        'xl': 'shadow-xl',
    }[shadow];

    return (
        <div
            style={{
                background,
                padding: paddingStyle,
                border,
                borderRadius,
            }}
            className={`product-list ${shadowClass} min-h-[100px]`}
            data-cy="product-list-widget"
        >
            {title && <h2 className="text-xl font-semibold mb-4" style={{ color: titleColor }}>{title}</h2>}
            {loading && (
                <div className="flex justify-center items-center h-full py-8">
                    <Spin tip="加载商品中..." />
                </div>
            )}
            {!loading && error && (
                <div className="flex justify-center items-center h-full py-8">
                    <Alert message={error} type="error" showIcon />
                </div>
            )}
            {!loading && !error && displayProducts.length === 0 && (
                <Empty
                    description="暂无商品或未选择商品"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    className="py-8"
                />
            )}
            {!loading && !error && displayProducts.length > 0 && (
                <Row gutter={[16, 16]}>
                    {displayProducts.map((product) => (
                        <Col
                            key={product.id}
                            span={layout === 'one-column' ? 24 : 12} // 修改：根据 layout 决定 span
                        >
                            <a href={product.link || '#'} target="_blank" rel="noopener noreferrer" className="block h-full">
                                <Card
                                    hoverable
                                    cover={
                                        <div className="h-48 overflow-hidden flex items-center justify-center bg-gray-100">
                                            <img
                                                alt={product.name}
                                                src={product.image}
                                                className="object-cover w-full h-full"
                                            />
                                        </div>
                                    }
                                    className="h-full flex flex-col"
                                    bodyStyle={{ flexGrow: 1 }}
                                >
                                    <div className="mt-2 flex flex-col justify-between flex-grow">
                                        <div>
                                            <h3
                                                className="text-base font-medium truncate mb-1"
                                                style={{ color: titleColor }}
                                                title={product.itemName}
                                            >
                                                {product.name}
                                            </h3>
                                            {product.description && (
                                                <p className="text-xs text-gray-500 my-1 line-clamp-2 h-8">
                                                    {product.brandName} {product.categoryName}
                                                </p>
                                            )}
                                            <p className="text-sm text-gray-600 mt-1">
                                                库存: {product.inventoryNum}
                                            </p>
                                        </div>
                                        <div className="flex justify-between items-center mt-2">
                                            <span
                                                className="text-lg font-bold"
                                                style={{ color: priceColor }}
                                            >
                                                {product.suggestPrice}
                                            </span>
                                            {showAddToCartButton && (
                                                <Button
                                                    size="small"
                                                    icon={<ShoppingCartOutlined />}
                                                    style={{
                                                        backgroundColor: buttonColor,
                                                        borderColor: buttonColor,
                                                        color: buttonTextColor
                                                    }}
                                                >
                                                    {buttonText}
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                </Card>
                            </a>
                        </Col>
                    ))}
                </Row>
            )}
        </div>
    );
};

export default ProductListViewer;