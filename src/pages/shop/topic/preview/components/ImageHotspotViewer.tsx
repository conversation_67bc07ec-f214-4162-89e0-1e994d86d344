// src/components/ImageHotspotViewer.jsx

// HotspotData 接口定义 (与 CMS 中的一致)
// interface HotspotData { id: string; x: number; y: number; width: number; height: number; link?: string; tooltip?: string; productId?: string; }

const ImageHotspotViewer = ({ imageUrl, altText, hotspots, ...otherProps }) => {
    if (!imageUrl) {
        return <div style={{ padding: '20px', border: '1px dashed #ccc', textAlign: 'center' }}>Image not available.</div>;
    }

    return (
        <div
            style={{
                position: 'relative',
                display: 'inline-block', // Important for correct positioning of hotspots
                lineHeight: 0, // Prevents extra space below img if it's inline-block
            }}
            {...otherProps} // 允许传递其他 Craft.js 可能保存的 style props 等
        >
            <img
                src={imageUrl}
                alt={altText || 'Hotspot Image'}
                style={{ display: 'block', maxWidth: '100%', height: 'auto' }}
            />
            {hotspots && hotspots.map((hotspot) => (
                <a // 或者用 div，然后用 onClick 处理
                    key={hotspot.id}
                    href={hotspot.link || undefined} // 如果没有链接，href 为 undefined 不会渲染为可点击
                    target={hotspot.link && hotspot.link.startsWith('http') ? '_blank' : undefined}
                    rel={hotspot.link && hotspot.link.startsWith('http') ? 'noopener noreferrer' : undefined}
                    className="h5-hotspot-area" // 给 H5 上的热区特定样式
                    style={{
                        position: 'absolute',
                        left: `${hotspot.x * 100}%`,
                        top: `${hotspot.y * 100}%`,
                        width: `${hotspot.width * 100}%`,
                        height: `${hotspot.height * 100}%`,
                        // H5 展示时的样式，可以与编辑器中的预览不同
                        // border: '2px solid rgba(0,123,255,0.7)', // 示例：给热区一个可见边框
                        // background: 'rgba(0,123,255,0.1)',     // 示例：半透明背景
                        cursor: (hotspot.link || hotspot.productId) ? 'pointer' : 'default',
                        boxSizing: 'border-box',
                    }}
                    title={hotspot.tooltip}
                    onClick={(e) => {
                        if (hotspot.productId) {
                            e.preventDefault(); // 阻止默认的链接跳转（如果有链接的话）
                            // 在 H5 中处理 productId 的逻辑，例如打开商品详情弹窗
                            console.log('H5: Clicked product hotspot:', hotspot.productId);
                            // showProductModal(hotspot.productId);
                        }
                        // 如果是纯链接，<a> 标签会自动处理跳转
                    }}
                >
                    {/* 你可以在热区内显示一些标记或内容，如果需要的话 */}
                </a>
            ))}
        </div>
    );
};

export default ImageHotspotViewer;