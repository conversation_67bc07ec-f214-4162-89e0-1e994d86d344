// src/components/PageRenderer.jsx
import ImageHotspotViewer from './ImageHotspotViewer';
import ProductListViewer from './ProductListViewer';
// 导入其他你可能在 CMS 中使用的组件的 H5 版本
// import TextBlockViewer from './TextBlockViewer';
// import ButtonViewer from './ButtonViewer';

// 组件名称映射 (CMS 中的组件名 -> H5 Viewer 组件)
// Key 必须与 Craft.js 组件的 `craft.displayName` 或类/函数名完全一致
const COMPONENT_MAP = {
    ImageHotspot: ImageHotspotViewer,
    ProductList: ProductListViewer,
    // Text: (props) => <div dangerouslySetInnerHTML={{ __html: props.text }} {...props.domProps} />, // 简单的文本组件示例
    // Container: (props) => <div {...props.domProps}>{props.children}</div>, // Craft.js 内置容器
    // ... 其他组件
};

// Craft.js 的节点数据通常包含 'ROOT' 节点
const RenderNode = ({ nodeData, allNodes }) => {
    if (!nodeData) return null;

    // 获取组件类型：如果是用户自定义组件，通常在 nodeData.type.resolvedName
    // 如果是内置HTML元素，可能在 nodeData.type (字符串)
    // Craft.js 0.2.x+ 使用 displayName
    let componentType = nodeData.displayName;

    if (typeof nodeData.type === 'string') { // 处理内置 HTML 元素或简单类型
        componentType = nodeData.type;
    } else if (nodeData.type && nodeData.type.resolvedName) { // 自定义组件
        componentType = nodeData.type.resolvedName;
    }


    const ComponentToRender = COMPONENT_MAP[componentType];

    if (!ComponentToRender) {
        // 如果组件未在 MAP 中注册，可以提供一个 fallback 或者直接忽略
        console.warn(`No H5 component found for type: ${componentType}`);
        if (nodeData.nodes && nodeData.nodes.length > 0) { // 尝试渲染子节点
            return <>{nodeData.nodes.map(childId => <RenderNode key={childId} nodeData={allNodes[childId]} allNodes={allNodes} />)}</>;
        }
        return null;
    }

    // 准备 props
    const props = { ...nodeData.props };
    // 如果组件可以有子节点
    if (nodeData.nodes && nodeData.nodes.length > 0) {
        props.children = nodeData.nodes.map(childId => (
            <RenderNode key={childId} nodeData={allNodes[childId]} allNodes={allNodes} />
        ));
    }

    // 对于 Craft.js 内置的如 div, span 等，它们可能没有 props，但有 domProps
    if (nodeData.dom) { // Craft.js 0.1.x (较旧)
        // props = { ...props, ...nodeData.dom.props };
    }
    // 对于 Craft.js 0.2.x+ 样式等通常直接在 props 或通过自定义 props 控制

    // 处理自定义组件的 props，如 ImageHotspot 的 imageUrl, hotspots
    // Craft.js 序列化时，props 已经包含了这些信息

    return <ComponentToRender {...props} />;
};

const PageRenderer = ({ pageData }) => {
    if (!pageData || typeof pageData !== 'object' || !pageData.ROOT) {
        // pageData 应该是从 Craft.js editor.query.serialize() 得到的对象
        return <div>Invalid page data structure.</div>;
    }

    // pageData 是一个以节点 ID 为键的对象
    // 渲染从 'ROOT' 节点开始
    return <RenderNode nodeData={pageData.ROOT} allNodes={pageData} />;
};

export default PageRenderer;