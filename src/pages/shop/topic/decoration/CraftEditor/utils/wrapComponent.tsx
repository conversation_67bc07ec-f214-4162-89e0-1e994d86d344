import { DeleteOutlined } from '@ant-design/icons';
import { useEditor, useNode, UserComponent } from '@craftjs/core';
import { useEffect, useRef } from 'react';

interface DeleteButtonProps {
    nodeId: string;
    isVisible: boolean;
}
const DeleteButton: React.FC<DeleteButtonProps> = ({ nodeId, isVisible }) => {
    const { actions, query } = useEditor();
    const buttonRef = useRef<HTMLDivElement>(null);

    // 使用原生事件监听器来确保事件能够触发
    useEffect(() => {
        const button = buttonRef.current;
        if (!button || !isVisible) return;

        const handleNativeClick = (e: Event) => {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();

            console.log('=== NATIVE DELETE CLICK ===');
            console.log('Deleting node:', nodeId);

            try {
                // 检查节点是否可删除
                const node = query.node(nodeId);
                if (node.isDeletable()) {
                    actions.delete(nodeId);
                    console.log('Node deleted successfully via native event');
                } else {
                    console.warn('Node is not deletable');
                }
            } catch (error) {
                console.error('Failed to delete node via native event:', error);
            }
        };

        const handleNativeMouseDown = (e: Event) => {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();

            console.log('=== NATIVE DELETE MOUSEDOWN ===');
            console.log('MouseDown on delete button for node:', nodeId);

            // 立即执行删除
            try {
                const node = query.node(nodeId);
                if (node.isDeletable()) {
                    actions.delete(nodeId);
                    console.log('Node deleted successfully via mousedown');
                }
            } catch (error) {
                console.error('Failed to delete node via mousedown:', error);
            }
        };

        // 添加多个事件监听器以确保至少一个能工作
        button.addEventListener('click', handleNativeClick, { capture: true });
        button.addEventListener('mousedown', handleNativeMouseDown, { capture: true });

        return () => {
            button.removeEventListener('click', handleNativeClick, { capture: true });
            button.removeEventListener('mousedown', handleNativeMouseDown, { capture: true });
        };
    }, [nodeId, isVisible, actions, query]);

    if (!isVisible) return null;

    return (
        <div
            ref={buttonRef}
            className="absolute -top-2 -right-2 bg-primary text-white rounded-full w-6 h-6 flex items-center justify-center shadow-md transition-colors cursor-pointer"
            style={{
                pointerEvents: 'auto',
                userSelect: 'none',
                zIndex: 999,
                position: 'absolute',
            }}
            title="删除组件"
        >
            <DeleteOutlined
                style={{
                    fontSize: '12px',
                    pointerEvents: 'none'
                }}
            />
        </div>
    );
};



export function wrapWithSelectionWrapper<T>(Component: UserComponent<T>): UserComponent<T> {

    const WrappedComponent: UserComponent<T> = (props) => {
        const { id } = useNode();
        const { query, selectedNodeId } = useEditor((state) => {
            const [currentNodeId] = state.events.selected;

            return {
                selectedNodeId: currentNodeId,
            };
        });

        const isSelected = selectedNodeId === id;
        const isDeletable = isSelected && query.node(id).isDeletable();

        return (
            <div className={`relative ${isSelected ? 'ring-1 ring-[#0099D3]' : ''}`}>
                <Component {...props} />

                <DeleteButton
                    nodeId={id}
                    isVisible={isDeletable && isSelected}
                />
            </div>
        );
    };

    WrappedComponent.craft = {
        ...Component.craft,
    };

    return WrappedComponent;
}