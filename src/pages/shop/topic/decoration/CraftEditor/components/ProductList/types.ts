type ProductSource = 'specified' | 'conditional';
type ListStyle = 'one-per-row' | 'two-per-row' | 'horizontal-scroll';
type DisplayCountType = 'all' | 'count';

export interface ProductGroup {
    id: string; // Unique ID for the tab/group (e.g., uuid)
    title: string; // Title for the tab
    // Product selection for this group can be by IDs or conditions
    productSource: ProductSource;
    specifiedProductIds?: string[]; // Array of product IDs for this group
    conditions?: ProductListConditions; // Conditions for this group
}

export interface ProductListConditions {
    brandIdList?: string[];
    categoryIdList?: string[];
    tagIds?: string[];
    onlyCampaignProducts?: boolean;
    displayCountType?: DisplayCountType;
    displayCount?: number;
}

export interface ProductListProps {
    listStyle?: ListStyle;
    productType?: 'single' | 'grouped'; // 'single' means one list, 'grouped' means tabbed groups

    // For 'single' type
    productSource?: ProductSource;
    specifiedProductIds?: string[];
    conditions?: ProductListConditions;

    // For 'grouped' type
    groups?: ProductGroup[];
    activeGroupKey?: string; // To store which tab is active in editor/preview
}