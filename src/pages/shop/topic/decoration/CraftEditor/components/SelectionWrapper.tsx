import { DeleteOutlined } from '@ant-design/icons';
import { useEditor, useNode } from '@craftjs/core';
import React from 'react';

/**
 * A wrapper component that adds a delete button to any component when it's selected
 */
export const SelectionWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { id } = useNode();
  const { actions, query, selectedNodeId } = useEditor((state, query) => {
    const [currentNodeId] = state.events.selected;
    return {
      selectedNodeId: currentNodeId,
      isHovered: state.events.hovered.includes(id),
    };
  });

  // Check if this node is selected and if it's deletable
  const isSelected = selectedNodeId === id;
  const isDeletable = isSelected && query.node(id).isDeletable();

  // Handle delete action
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    actions.delete(id);
  };

  return (
    <div className="relative group">
      {children}

      {/* Delete button - only shown when component is selected */}
      {isDeletable && isSelected && (
        <button
          className="absolute -top-3 -right-3 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center shadow-md hover:bg-red-600 transition-colors z-50"
          onClick={handleDelete}
        >
          <DeleteOutlined style={{ fontSize: '12px' }} />
        </button>
      )}
    </div>
  );
};

// Add craft configuration
SelectionWrapper.craft = {
  // isSelectionWrapper: true,
};
