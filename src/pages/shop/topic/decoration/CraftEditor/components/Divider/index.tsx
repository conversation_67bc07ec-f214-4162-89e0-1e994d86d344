import { useEditor, useNode } from '@craftjs/core';
import { Divider as AntdDivider } from 'antd'; // 导入Ant Design的Divider，并重命名以避免冲突
import React from 'react';
import DividerSettings from './DividerSettings';

export interface DividerProps {
    spacing?: number; // 上下间距，单位像素
}

const Divider: React.FC<DividerProps> = ({ spacing = 5 }) => {
    const {
        connectors: { connect, drag },
        selected,
        actions: { setProp },
    } = useNode((node) => ({
        selected: node.events.selected,
    }));
    const { enabled } = useEditor((state) => ({
        enabled: state.options.enabled,
    }));

    // 计算上下各一半的padding，确保总间距是spacing
    const verticalPadding = spacing / 2;

    return (
        <div
            ref={(ref) => connect(drag(ref as HTMLElement))}
            style={{
                paddingTop: `${verticalPadding}px`,
                paddingBottom: `${verticalPadding}px`,
                minHeight: `${spacing + 20}px`, // 确保即使间距很小，组件也有足够的高度被选中和拖拽 (20px 为AntdDivider本身高度的估算)
            }}
        >
            {/* AntdDivider 默认有内边距，所以我们用 !my-0 覆盖掉，让父容器的padding来控制总间距 */}
            <AntdDivider className="!my-0 border-gray-300" />
        </div>
    );
};

// 定义默认属性
Divider.craft = {
    props: {
        spacing: 5,
    } as DividerProps,
    displayName: 'topic.decoration.icon.divider',
    related: {
        settings: DividerSettings
    },
};

export default Divider;