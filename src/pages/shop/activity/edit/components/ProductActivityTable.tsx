import SelectProductDrawer from '@/pages/shop/topic/decoration/CraftEditor/components/LinkSettings/SelectProductDrawer';
import { ActivityItemEntity } from '@/pages/shop/types/ActivityDataType';
import { ProFormItem } from '@ant-design/pro-components';
import { EditableProTable, ProColumns } from '@ant-design/pro-table';
import { Button, FormInstance, Space, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react'; // 导入 useEffect
import { useIntl } from 'react-intl';

interface ProductActivityTableProps {
    onDataChange?: (data: ActivityItemEntity[]) => void;
    columns: ProColumns<ActivityItemEntity>[];
    form: FormInstance;
}

const ProductActivityTable: React.FC<ProductActivityTableProps> = ({ onDataChange, form, columns }) => {
    const intl = useIntl();
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    // 关键：初始时将所有行的ID都设为可编辑状态
    const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
    const [isModalVisible, setIsModalVisible] = useState(false);


    // 使用 useEffect 在 form value 变化时更新 editableKeys
    useEffect(() => {
        // 确保所有 form value 中的行都处于编辑状态
        const currentItems = form.getFieldValue('activityItems') ?? [];
        const allIds = currentItems.map(item => item.itemId);
        setEditableKeys(allIds);
    }, [form, form.getFieldValue('activityItems')]); // 依赖 form 和 form value


    const actionRef = useRef<any>();


    // 添加商品
    const handleAddProduct = (ids, selectedProducts) => {
        const currentItems = form.getFieldValue('activityItems') ?? [];
        const updatedItems = [...currentItems, ...selectedProducts];
        form.setFieldsValue({ activityItems: updatedItems });
        setIsModalVisible(false);
    };

    // 删除选中商品
    const handleDeleteSelected = () => {
        if (selectedRowKeys.length === 0) {
            message.warning(intl.formatMessage({ id: 'common.message.needSelectOne' }));
            return;
        }

        const currentItems = form.getFieldValue('activityItems') ?? [];
        const newItems = currentItems.filter((item) => !selectedRowKeys.includes(item.itemId));
        form.setFieldsValue({ activityItems: newItems });
        setSelectedRowKeys([]); // 清空选中项
    };

    return (
        <div className="p-4 bg-white rounded shadow-sm">
            <Space className="mb-4">
                <Button type="primary" onClick={() => setIsModalVisible(true)}>
                    {intl.formatMessage({ id: 'shop.common.button.addProduct' })}
                </Button>
                <Button className='button-outline' onClick={handleDeleteSelected}>
                    {intl.formatMessage({ id: 'common.button.delete' })}
                </Button>
                <Button className='button-outline'>{intl.formatMessage({ id: 'common.button.batchImport' })}</Button>
            </Space>
            <ProFormItem
                name="activityItems"
                rules={[
                    {
                        required: true,
                        message: intl.formatMessage({ id: 'shop.activity.giftInfo.giftItemsRequired' }),
                        validator: (_, value) =>
                            value && value.length > 0 ? Promise.resolve() : Promise.reject(),
                    },
                ]}
            >
                <EditableProTable<ActivityItemEntity>
                    rowKey="itemId"
                    columns={columns}
                    value={form.getFieldValue('activityItems') ?? []}
                    onChange={(newData) => {
                        console.log('onChange', newData);
                        form.setFieldsValue({ activityItems: newData });
                        // onDataChange?.(newData); // Call the external onDataChange prop
                    }}
                    recordCreatorProps={false}
                    editable={{
                        type: 'multiple', // 允许多行同时编辑
                        editableKeys: editableKeys, // 始终将所有行的ID都包含在 editableKeys 中
                        onSave: async (rowKey, row, originRow) => {
                            console.log('保存行:', rowKey, row);
                            // 如果你希望保存后数据立即反映在UI上，且数据并非通过API重载，可以手动更新 dataSource
                            // const newDataSource = dataSource.map(item => item.id === rowKey ? row : item);
                            // handleDataSourceChange(newDataSource); // 已经通过 onChange 自动更新了
                        },
                        // 隐藏默认的操作列（保存/取消按钮），因为所有列都默认可编辑
                        actionRender: () => [],
                    }}
                    search={false}
                    options={false}
                    pagination={false}
                    rowSelection={{
                        columnWidth: 48,
                        selectedRowKeys,
                        onChange: (keys) => setSelectedRowKeys(keys),
                    }}
                    scroll={{ x: 'max-content', y: 500 }}
                    virtual={true}
                    actionRef={actionRef}
                    headerTitle={null}
                    tableAlertRender={() => false}
                    size="small"
                />
            </ProFormItem>

            <SelectProductDrawer visible={isModalVisible} onClose={() => {
                setIsModalVisible(false);
            }} onOk={handleAddProduct} />
        </div >
    );
};

export default ProductActivityTable;