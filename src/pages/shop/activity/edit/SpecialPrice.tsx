import { <PERSON><PERSON>ontainer, ProForm } from "@ant-design/pro-components";
import { useIntl, useSearchParams } from "@umijs/max";
import { useForm } from "antd/es/form/Form";
import dayjs from "dayjs";
import { useEffect } from "react";
import { SubmitArea } from ".";
import { ActivityFormValues, ActivityItemEntity } from "../../types/ActivityDataType";
import { ActivityArea, ActivityType } from "../../types/ActivityEnum";
import { CouponFormValues } from "../../types/CouponDataType";
import ActivityBasicForm from "./components/ActivityBasicForm";
import ProductActivityTable from "./components/ProductActivityTable";
import { specialActivityColumns } from "./config/productActivityColumn";
import { queryActivityById } from "./service";




const SpecialActivityForm: React.FC = () => {
    const intl = useIntl();
    const [searchParams] = useSearchParams();
    const activityId = searchParams.get('id');
    const type = searchParams.get('type'); // view | edit | copy
    const readonly = type === 'view';

    const [form] = useForm<ActivityFormValues>();
    const [activityItemsForm] = useForm<ActivityItemEntity>();


    useEffect(() => {
        if (activityId) {
            queryActivityById({ activityId }).then(res => {
                form.setFieldsValue({
                    ...res,
                    periodTime: [res.startTime ? dayjs(res.startTime) : undefined, res.endTime ? dayjs(res.endTime) : undefined],
                    activityImageList: res.activityImage ? [{ uid: '1', name: res.activityImage, status: 'done', url: res.activityImage }] : [],
                });
                activityItemsForm.setFieldValue('activityItems', res.activityItems);
            });
        }
    }, [activityId]);


    const onSubmit = async () => {
        const tableValidate = await activityItemsForm.validateFields();
        console.log('onSubmit', form.getFieldsValue());
        console.log('tableFormRef', tableValidate);
        const basicValues = form.getFieldsValue();
        const { periodTime = [], activityImageList = [] } = basicValues;
        const productData = activityItemsForm.getFieldValue('activityItems') ?? [];
        const params = {
            activityType: ActivityType.SPECIAL_PRICE,
            activityName: basicValues.activityName,
            startTime: periodTime[0] ? periodTime[0].format('YYYY-MM-DD HH:mm:ss') : undefined,
            endTime: periodTime[1] ? periodTime[1].format('YYYY-MM-DD HH:mm:ss') : undefined,
            activityDesc: basicValues.activityDesc,
            activityArea: basicValues.activityArea,
            areaId: basicValues.areaId,
            userCoupon: basicValues.userCoupon ? 1 : 0,
            onCredit: basicValues.onCredit ? 1 : 0,
            orderNoStock: basicValues.orderNoStock ? 1 : 0,
            remark: basicValues.remark,
            activityItems: productData.map(item => ({
                activityId,
                itemId: item.itemId,
                itemName: item.itemName,
                categoryId: item.categoryId,
                categoryName: item.categoryName,
                brandId: item.brandId,
                brandName: item.brandName,
                promotionPrice: item.promotionPrice,
                minBuyNum: item.minBuyNum,
                maxBuyNum: item.maxBuyNum,
                totalMaxBuyNum: item.totalMaxBuyNum,
                showOrder: item.showOrder
            })),
            activityImage: activityImageList?.file?.response?.data?.[0]
        }
        console.log('params', params);
    }


    return <PageContainer>
        <ProForm<CouponFormValues>
            className="flex flex-col h-[calc(100vh-130px)]"
            form={form}
            initialValues={{
                activityArea: ActivityArea.NO_LIMIT,
            }}
            readonly={readonly}
            onFinish={onSubmit}
            submitter={{
                render: (props, doms) => {
                    return (
                        <SubmitArea {...props} readonly={readonly} />
                    );
                },
            }}
        >
            <div className="flex flex-1 bg-white rounded-lg overflow-auto">
                {/* 左侧：基础信息 */}
                <div className="w-[328px] p-4 shadow-sm">
                    <h3 className="text-lg font-semibold text-gray-800 module-title">{intl.formatMessage({ id: 'shop.activity.title.basicInfo' })}</h3>
                    <ActivityBasicForm form={form} />
                </div>

                {/* 右侧：适用商品 */}
                <div className="flex-1 min-w-0 p-4 shadow-sm">
                    <h3 className="module-title">{intl.formatMessage({ id: 'shop.activity.title.applicableProducts' })}</h3>
                    <ProductActivityTable
                        form={activityItemsForm}
                        columns={specialActivityColumns}
                    />
                </div>
            </div>
        </ProForm>
    </PageContainer>
}

export default SpecialActivityForm;