import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { queryFinReceivablePage } from '@/pages/finance/collection/services';
import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { exportData } from '@/utils/exportData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl, useLocation } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Checkbox, Flex } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import DetailDrawer from './components/DetailDrawer';
import { getTableColumns } from './config/TableColumns';
import { type FinReceivableDetailModalType } from './types/FinReceivableDetailModalType';
import { type FinReceivableEntity } from './types/FinReceivableEntity.entity';

const FinReceivable = () => {
  const intl = useIntl();
  const [pageParams, setPageParams] = useState<any>();
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [totalReceivableAmount, setTotalReceivableAmount] = useState<string>('0');
  const [receivableFlag, setReceivableFlag] = useState(false);
  const location = useLocation();

  useActivate(() => {
    actionRef.current?.reload();
  });

  useEffect(() => {
    if (location?.state) {
      const params = {};
      const receivableFlag = location?.state?.receivableFlag;
      setReceivableFlag(receivableFlag == '1');
      params.receivableFlag = receivableFlag;
      const storeIdList = location?.state?.storeIdList;
      if (storeIdList) {
        params.storeIdList = storeIdList;
        formRef.current?.setFieldValue('storeIdList', storeIdList);
      }
      setPageParams((pre) => ({ ...pre, ...params }));
    }
  }, [location?.state]);

  useAsyncEffect(async () => {
    actionRef.current?.reload(true);
  }, [receivableFlag]);

  // 明细
  const [detailDrawerProps, setDetailDrawerProps] = useState<FinReceivableDetailModalType>({
    visible: false,
  });

  /**
   * 打开【明细】对话框
   */
  const openDetailDrawer = (record: FinReceivableEntity) => {
    setDetailDrawerProps((preModalProps) => ({
      ...preModalProps,
      title: intl.formatMessage({ id: 'finance.collection.detail' }),
      visible: true,
      record: record,
    }));
  };
  const operatorColumn: ProColumns<FinReceivableEntity> = {
    title: intl.formatMessage({ id: 'common.column.operation' }),
    valueType: 'option',
    fixed: 'right',
    width: 80,
    render: (_text, record) => [
      <AuthButton
        isHref
        authority="collectionDetail"
        key="crete"
        onClick={() => {
          openDetailDrawer(record);
        }}
      >
        {intl.formatMessage({ id: 'finance.collection.detailTitle' })}
      </AuthButton>,
    ],
  };
  /**
   * 关闭【明细】对话框
   */
  const closeDetailDrawer = () => {
    setDetailDrawerProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      record: undefined,
    }));
  };

  /**
   * 分页查询数据
   */
  const finReceivableGroupQueryPage = async (
    params: Partial<FinReceivableEntity> & PageRequestParamsType,
  ) => {
    const queryResult = await queryFinReceivablePage({
      ...params,
      receivableFlag: receivableFlag ? 1 : 0,
    });
    setTotalReceivableAmount(queryResult?.data?.[0].totalReceivableAmountYuan || '0');
    return { ...queryResult, data: queryResult?.data?.[0].finReceivableRoList || [] };
  };

  return (
    <PageContainer>
      <FunProTable<FinReceivableEntity, any>
        onReset={() => {
          setPageParams({ pageSize: 10, pageNo: 1 });
          setReceivableFlag(false);
        }}
        params={pageParams}
        scroll={{ x: 'max-content' }}
        request={finReceivableGroupQueryPage}
        options={{ setting: false, density: false, reload: false }}
        actionRef={actionRef}
        formRef={formRef}
        columns={[...getTableColumns(intl), operatorColumn]}
        title={() => (
          <Flex justify="space-between" align="flex-end">
            <AuthButton
              authority="exportCollection"
              danger
              onClick={() => {
                exportData({
                  systemId: 'ETC_SAAS_SYS',
                  taskDesc: intl.formatMessage({ id: 'finance.collection.exportDescription' }),
                  moduleId: 'RECEIVABLE_GROUP_EXPORT',
                  params: {
                    ...formRef.current?.getFieldsValue(),
                    receivableFlag: receivableFlag ? 1 : 0,
                    storeIdList: formRef.current?.getFieldValue('storeIdList')
                      ? [formRef.current?.getFieldValue('storeIdList')]
                      : [],
                  },
                });
              }}
            >
              {intl.formatMessage({ id: 'common.button.export' })}
            </AuthButton>
            <Checkbox
              className="mb-0"
              checked={receivableFlag}
              onChange={(e) => setReceivableFlag(e.target.checked)}
            >
              {intl.formatMessage({ id: 'finance.collection.onlyShowReceivable' })}
            </Checkbox>
          </Flex>
        )}
      />
      <ProCard bordered>
        <span className="flex flex-row items-center">
          <span className="text-[16px] font-semibold text-[#000000D9]">{intl.formatMessage({ id: 'finance.collection.totalReceivableAmount' })}：</span>
          <span className="text-[24px] font-medium text-[#F83431]">￥{totalReceivableAmount}</span>
        </span>
      </ProCard>

      <DetailDrawer {...detailDrawerProps} onCancel={closeDetailDrawer} />
    </PageContainer>
  );
};

export default withKeepAlive(FinReceivable);