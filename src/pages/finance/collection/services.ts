import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { type FinReceivableEntity } from './types/FinReceivableEntity.entity';
import { FinReceivableGroupEntity } from './types/FinReceivableGroupEntity.entity';

/**
 * 应收分页查询
 *
 * @param params
 * @returns
 */
export const queryFinReceivablePage = async (
  params: Partial<FinReceivableEntity> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<FinReceivableGroupEntity>>(
    `/ipmsaccount/queryReceivablePageGroup`,
    {
      data: params,
    },
  );
};
/**
 * 应收明细查询
 *
 * @param params
 * @returns
 */
export const queryReceivableDetailPage = async (
  params: Partial<FinReceivableEntity> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<FinReceivableGroupEntity>>(
    `/ipmsaccount/queryReceivableDetailPage`,
    {
      data: params,
    },
  );
};
/**
 * 应收明细查询
 *
 * @param params
 * @returns
 */
export const queryReceivableList = async (
  params: Partial<FinReceivableEntity> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<FinReceivableEntity>>(`/ipmsaccount/queryReceivableList`, {
    data: params,
  });
};
