import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { type CommonModelForm } from '@/types/CommonModelForm';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { Space } from 'antd';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import CreateFormModal from './components/CreateFormModal';
import { PostListTableColumns } from './config/postListTableColumns';
import {
  createMemberAccount,
  invalidMemberAccount,
  queryMemberAccountPage,
  updateMemberAccount,
} from './services';
import type { MemberAccountEntity } from './types/MemberAccountEntity';
import type { MemberAccountQueryType } from './types/MemberAccountQueryType';

const FinCustomer = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [createModalProps, setCreateModalProps] = useState<
    CommonModelForm<MemberAccountQueryType, MemberAccountEntity>
  >({
    visible: false,
    recordId: undefined,
    title: '',
  });


  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 禁用事件
   * @param ids
   */
  const handleDeleteItem = async (accountId: number) => {
    await invalidMemberAccount({ accountId });
    actionRef.current?.reload(true);
  };

  const handleUpdateItem = async (params: MemberAccountQueryType) => {
    setCreateModalProps({
      visible: true,
      recordId: params,
      title: intl.formatMessage({ id: 'finance.customer.editAccount' }),
      readOnly: true,
    });
  };

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setCreateModalProps({
      visible: false,
      recordId: undefined,
      title: '',
    });
  };

  /**
   * 新增或编辑
   * @param values
   */
  const handleSaveOrUpdate = async (values: MemberAccountEntity) => {
    try {
      const { id, ...restValues } = values;
      let result = false;
      if (id) {
        //编辑
        result = await updateMemberAccount(values);
      } else {
        //新增
        result = await createMemberAccount(restValues);
      }
      if (result) {
        hideModal();
        actionRef.current?.reload(true);
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  return (
    <PageContainer>
      <FunProTable<MemberAccountEntity, any>
        rowKey="id"
        requestPage={queryMemberAccountPage}
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        columns={PostListTableColumns({
          handleUpdateItem,
          handleDeleteItem,
        })}
        headerTitle={
          <Space>
            <AuthButton
              authority="addFinAccount"
              type="primary"
              key="primary"
              onClick={() =>
                setCreateModalProps({
                  visible: true,
                  recordId: undefined,
                  readOnly: false,
                  title: intl.formatMessage({ id: 'finance.customer.addAccount' }),
                })
              }
            >
              {intl.formatMessage({ id: 'finance.customer.addAccount' })}
            </AuthButton>
          </Space>
        }
      />
      <CreateFormModal {...createModalProps} onOk={handleSaveOrUpdate} onCancel={hideModal} />
    </PageContainer>
  );
};

export default withKeepAlive(FinCustomer);