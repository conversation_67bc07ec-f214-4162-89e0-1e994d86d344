import { queryStoreByAccount } from '@/pages/system/user/services';
import type { CommonModelForm } from '@/types/CommonModelForm';
import { REG_LENGTH_REMARK_RULE, REQUIRED_RULES } from '@/utils/RuleUtils';
import { ProFormMoney } from '@ant-design/pro-components';
import { ModalForm, ProFormSelect, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { useForm } from 'antd/lib/form/Form';
import { queryMemberAccount } from '../../services';
import type { MemberAccountEntity } from '../../types/MemberAccountEntity';
const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};
export default (
  props: CommonModelForm<{ customerId: number; memberAccountName: string }, MemberAccountEntity>,
) => {
  const intl = useIntl();
  const [form] = useForm();
  useAsyncEffect(async () => {
    if (!props.recordId) {
      form.resetFields();
    } else {
      const data = await queryMemberAccount(props.recordId);
      form.setFieldsValue({
        ...data,
        accountExtends: data.accounExtends?.map((accountExt) => accountExt.belongToStoreId),
      });
    }
  }, [props.visible]);

  return (
    <ModalForm
      {...layout}
      form={form}
      layout="horizontal"
      title={props.title}
      open={props.visible}
      width="40%"
      modalProps={{
        maskClosable: false,
        centered: true,
        onCancel: props.onCancel,
      }}
      onFinish={props.onOk}
    >
      <ProFormText name="id" hidden />
      <ProFormText rules={[REQUIRED_RULES]} name="memberAccountName" label={intl.formatMessage({ id: 'finance.customer.form.accountName' })} />
      <ProFormText name="tripartiteAccountDesc" label={intl.formatMessage({ id: 'finance.customer.form.bankName' })} />
      <ProFormText name="tripartiteAccount" label={intl.formatMessage({ id: 'finance.customer.form.bankCardNumber' })} />
      <ProFormSelect
        rules={[REQUIRED_RULES]}
        name="accountExtends"
        label={intl.formatMessage({ id: 'finance.customer.form.belongToStore' })}
        mode="multiple"
        fieldProps={{
          showSearch: true,
          fieldNames: { label: 'name', value: 'id' },
          maxTagCount: 3,
        }}
        request={async () => await queryStoreByAccount({ status: 1 })}
      />
      <ProFormMoney name="totalAmountYuan" label={intl.formatMessage({ id: 'finance.customer.form.initialBalance' })} disabled={props.readOnly} />
      <ProFormTextArea
        fieldProps={{ maxLength: 50 }}
        name="remark"
        label={intl.formatMessage({ id: 'common.column.remark' })}
        rules={[REG_LENGTH_REMARK_RULE]}
      />
    </ModalForm>
  );
};
