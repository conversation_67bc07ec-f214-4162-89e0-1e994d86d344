import type { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { MemberAccountEntity } from './types/MemberAccountEntity';

/**
 * 财务管理-账户管理-列表查询
 * @param params
 * @returns
 */
export const queryMemberAccountPage = (params: Partial<MemberAccountEntity>) => {
  return request<PageResponseDataType<MemberAccountEntity>>(
    `/ipmsaccount/memberAccount/queryByPage`,
    {
      data: params,
    },
  );
};

/**
 * 财务管理-账户管理-删除账户
 * @param params
 * @returns
 */
export const invalidMemberAccount = async (params: { accountId: number }) => {
  return request<boolean>(`/ipmsaccount/invalidMemberAccount`, {
    data: params,
  });
};
/**
 * 财务管理-账户管理-新增账户
 * @param params
 * @returns
 */
export const createMemberAccount = async (params: Partial<MemberAccountEntity>) => {
  return request<boolean>(`/ipmsaccount/createMemberAccount`, {
    data: params,
  });
};
/**
 * 财务管理-账户管理-编辑账户
 * @param params
 * @returns
 */
export const updateMemberAccount = async (params: Partial<MemberAccountEntity>) => {
  return request<boolean>(`/ipmsaccount/updateMemberAccount`, {
    data: params,
  });
};
/**
 * 财务管理-账户管理-编辑账户
 * @param params
 * @returns
 */
export const queryMemberAccount = async (params: {
  customerId: number | undefined;
  memberAccountName: string;
}) => {
  return request<MemberAccountEntity>(`/ipmsaccount/queryMemberAccount`, {
    data: params,
  });
};
