import { getCstList } from '@/pages/customer/list/services';
import type { ReceivedEntity } from '@/pages/finance/receive/types/ReceivedEntity';
import { accountListQuerySimple } from '@/pages/system/user/services';
import type { ProColumns } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';

export const getTableColumns = (intl: IntlShape): ProColumns<ReceivedEntity>[] => [
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.store' }),
    dataIndex: 'storeName',
    width: 120,
    hideInSearch: true,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.receiveTime' }),
    dataIndex: 'businessTimeQuery',
    width: 140,
    valueType: 'dateRange',
    hideInTable: true,
    search: {
      transform: (value: any) => {
        return {
          startBusinessTime: value[0],
          endBusinessTime: value[1],
        };
      },
    },
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.receiveTime' }),
    dataIndex: 'businessTime',
    width: 140,
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.customer' }),
    dataIndex: 'buyerName',
    width: 100,
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.customer' }),
    dataIndex: 'buyerId',
    hideInTable: true,
    valueType: 'select',
    fieldProps: {
      showSearch: true,
    },
    request: async (query) => {
      const data = await getCstList({ keyword: query.keyWords });
      return data?.map(({ cstId, cstName }) => ({
        value: cstId,
        label: cstName,
      }));
    },
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.receivedAccount' }),
    dataIndex: 'receivedAccountName',
    search: false,
    width: 100,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.receivedAmount' }),
    dataIndex: 'totalReceivedAmountYuan',
    search: false,
    width: 100,
    valueType: 'money',
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.creator' }),
    dataIndex: 'createPerson',
    width: 60,
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.creator' }),
    dataIndex: 'createPerson',
    key: 'createPerson',
    search: true,
    width: 60,
    valueType: 'select',
    hideInTable: true,
    fieldProps: {
      showSearch: true,
      fieldNames: { label: 'name', value: 'id' },
    },
    request: (query: any) => {
      return accountListQuerySimple({ name: query.keyWords });
    },
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.remark' }),
    dataIndex: 'remark',
    search: false,
    width: 100,
    ellipsis: true,
  },
];
