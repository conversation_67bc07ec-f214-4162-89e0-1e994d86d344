import type { ProColumns } from '@ant-design/pro-components';
import { type GoodsEntity } from '../types/ReceivedEntity';

export const SelectColumns: ProColumns<GoodsEntity>[] = [
  {
    title: '序号',
    valueType: 'index',
    fixed: 'left',
    width: 40,
  },

  {
    title: '门店名称',
    dataIndex: 'itemaSn',
    width: 100,
  },
  {
    title: '业务单号',
    dataIndex: 'itemSn',
    width: 100,
  },
  {
    title: '订单完成时间',
    dataIndex: 'oeNoList',
    width: 140,
    valueType: 'date',
  },
  {
    title: '订单金额',
    dataIndex: '12oeNoList',
    search: false,
    width: 100,
    valueType: 'money',
  },
  {
    title: '已收金额',
    dataIndex: '12oeNoList',
    search: false,
    width: 100,
    valueType: 'money',
  },
  {
    title: '未收金额',
    dataIndex: '12oeNoList1',
    search: false,
    width: 100,
    valueType: 'money',
  },
];
