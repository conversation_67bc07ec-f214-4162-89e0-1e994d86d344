import type { ReceivedFlowEntity } from '@/pages/finance/receive/types/ReceivedFlowEntity';
import type { ProColumns } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';

export const getDetailColumns = (intl: IntlShape): ProColumns<ReceivedFlowEntity>[] => [
  {
    title: intl.formatMessage({ id: 'common.column.index' }),
    valueType: 'index',
    fixed: 'left',
    width: 40,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.storeName' }),
    dataIndex: 'storeName',
    fixed: 'left',
    width: 100,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.businessOrderNo' }),
    dataIndex: 'orderNo',
    width: 100,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.orderCompleteTime' }),
    dataIndex: 'billDate',
    width: 140,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.orderAmount' }),
    dataIndex: 'orderAmountYuan',
    width: 100,
    valueType: 'money',
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.writeOffAmount' }),
    dataIndex: 'receivedAmountYuan',
    width: 100,
    valueType: 'money',
  },
];
