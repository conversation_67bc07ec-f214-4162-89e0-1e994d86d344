import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { queryReceivedPage } from '@/pages/finance/receive/services';
import type { ReceivedDetailModalType } from '@/pages/finance/receive/types/ReceivedDetailModalType';
import type { ReceivedEntity } from '@/pages/finance/receive/types/ReceivedEntity';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ProColumns } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { Space } from 'antd';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import CreateModal from './components/CreateModal';
import DetailDrawer from './components/DetailDrawer';
import { getTableColumns } from './config/TableColumns';

const FinReceive = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  // 新增
  const [createModalProps, setCreateModalProps] = useState<ReceivedDetailModalType>({
    visible: false,
    serialNumber: undefined,
    readOnly: false,
    title: intl.formatMessage({ id: 'finance.receive.add' }),
  });

  // 明细
  const [detailDrawerProps, setDetailDrawerProps] = useState<ReceivedDetailModalType>({
    visible: false,
  });

  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 关闭【新增】对话框
   */
  const hideCreateModal = () => {
    setCreateModalProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
    }));
  };
  /**
   * 打开【明细】对话框
   */
  const openDetailDrawer = (record: ReceivedEntity) => {
    setDetailDrawerProps((preModalProps) => ({
      ...preModalProps,
      title: intl.formatMessage({ id: 'finance.receive.detail' }),
      visible: true,
      serialNumber: record.serialNumber,
    }));
  };
  /**
   * 关闭【明细】对话框
   */
  const closeDetailDrawer = () => {
    setDetailDrawerProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      serialNumber: undefined,
    }));
  };

  /**
   * 新增
   * @param values
   */
  const handleReceivedCreate = async () => {
    try {
      setCreateModalProps((preModalProps) => ({
        ...preModalProps,
        visible: false,
      }));
      actionRef.current?.reload(true);
      return true;
    } catch (error) {
      return false;
    }
  };

  const preColumns: ProColumns<ReceivedEntity>[] = [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'finance.receive.columns.serialNumber' }),
      dataIndex: 'serialNumber',
      width: 120,
      search: false,
      render: (text, record) => <a onClick={() => openDetailDrawer(record)}>{text}</a>,
    },
  ];

  return (
    <PageContainer>
      <FunProTable<ReceivedEntity, any>
        requestPage={queryReceivedPage}
        options={{ setting: true, density: false, reload: false }}
        actionRef={actionRef}
        columns={[...preColumns, ...getTableColumns(intl)]}
        scroll={{ x: 'max-content' }}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              key="create"
              authority="addReceive"
              onClick={() => {
                setCreateModalProps((preModalProps) => ({
                  ...preModalProps,
                  visible: true,
                  title: intl.formatMessage({ id: 'finance.receive.add' }),
                }));
              }}
            >
              {intl.formatMessage({ id: 'finance.receive.add' })}
            </AuthButton>
          </Space>
        }
      />
      <DetailDrawer {...detailDrawerProps} onCancel={closeDetailDrawer} />
      <CreateModal {...createModalProps} onCancel={hideCreateModal} onOk={handleReceivedCreate} />
    </PageContainer>
  );
};

export default withKeepAlive(FinReceive);