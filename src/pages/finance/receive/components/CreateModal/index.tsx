import LeftTitle from '@/components/LeftTitle';
import FunProTable from '@/components/common/FunProTable';
import { getCstList } from "@/pages/customer/list/services";
import { queryReceivableList } from "@/pages/finance/collection/services";
import type { FinReceivableEntity } from "@/pages/finance/collection/types/FinReceivableEntity.entity";
import { queryMemberAccountPage } from "@/pages/finance/customer/services";
import { getCreateColumns } from "@/pages/finance/receive/config/CreateColumns";
import { receivedConfirmation } from "@/pages/finance/receive/services";
import type { GoodsCreateDrawerFormType } from "@/pages/goods/list/types/GoodsCreateDrawerFormType";
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import {
  DrawerForm,
  ProCard,
  ProFormMoney,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from "ahooks";
import { Al<PERSON>, Button, Flex, Form, Input, message, Space } from 'antd';
import dayjs from "dayjs";
import _ from 'lodash';
import React, { useEffect, useState } from "react";

const labelCol = { span: 5 };
const colProps = { span: 19 };
const width = 'lg';
export default (props: GoodsCreateDrawerFormType) => {
  const intl = useIntl();
  const { Search } = Input;
  const [form] = Form.useForm();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [receivableList, setReceivableList] = useState<FinReceivableEntity[]>([]);
  const [cstSelect, setCstSelect] = useState();
  const [accountSelect, setAccountSelect] = useState();
  const [totalReceivedAmountYuan, setTotalReceivedAmountYuan] = useState<number>(0);
  const [currTotalReceivedAmountYuan, setCurrTotalReceivedAmountYuan] = useState<number>(0);
  const [searchOrderNo, setSearchOrderNo] = useState<string>('');
  const [alertMsg, setAlertMsg] = useState<string>('');

  useAsyncEffect(async () => {
    if (!props.visible) {
      form.resetFields();
      setReceivableList([]);
      setEditableRowKeys([]);
      setCstSelect(undefined);
      setAccountSelect(undefined);
      setTotalReceivedAmountYuan(0);
      setCurrTotalReceivedAmountYuan(0);
      setAlertMsg("");
      setSearchOrderNo("");
    }
  }, [props.visible]);


  const watchedTotalReceivedAmountYuanValue = Form.useWatch('totalReceivedAmountYuan', form);
  useAsyncEffect(async () => {
    setTotalReceivedAmountYuan(watchedTotalReceivedAmountYuanValue)
    handleAlertMsg(watchedTotalReceivedAmountYuanValue, currTotalReceivedAmountYuan);
  }, [watchedTotalReceivedAmountYuanValue]);

  const handleAlertMsg = (totalReceivedAmount, currTotalReceivedAmount) => {
    if (totalReceivedAmount != currTotalReceivedAmount) {
      setAlertMsg(intl.formatMessage({ id: 'finance.receive.writeOffAmountMismatch' }));
      return;
    }
    setAlertMsg("");
  };

  const autoAssignOrders = () => {
    let totalReceivedAmount: number = form.getFieldValue("totalReceivedAmountYuan");
    const oldTotalReceivedAmount = totalReceivedAmount;
    if (totalReceivedAmount == undefined) {
      message.error(intl.formatMessage({ id: 'finance.receive.noAmountError' }));
      return;
    }
    if (totalReceivedAmount <= 0) {
      message.error(intl.formatMessage({ id: 'finance.receive.negativeAmountError' }));
      return;
    }

    const newReceivableList = receivableList.map(item => {
      if (totalReceivedAmount == 0) {
        item.currReceivedAmount = undefined;
        return item;
      }
      if (totalReceivedAmount >= item.remainReceivableAmountYuan) {
        item.currReceivedAmount = item.remainReceivableAmountYuan;
        totalReceivedAmount = _.round(_.subtract(totalReceivedAmount, item.remainReceivableAmountYuan as number), 2);
        return item;
      }

      item.currReceivedAmount = totalReceivedAmount
      totalReceivedAmount = 0;
      return item;
    })
    setReceivableList(newReceivableList);
    const currTotalReceivedAmount = _.round(_.subtract(oldTotalReceivedAmount, totalReceivedAmount), 2);
    setCurrTotalReceivedAmountYuan(currTotalReceivedAmount);
    handleAlertMsg(oldTotalReceivedAmount, currTotalReceivedAmount);
  };

  const handleCstSelectChange = async (cstId, option) => {
    setCstSelect(option);
    if (!cstId) {
      return;
    }
    const data = await queryReceivableList({ buyerId: cstId, receivableFlag: 1 });
    setReceivableList(data?.filter(item => item.remainReceivableAmountYuan != 0).sort((a, b) => new Date(a.billDate) - new Date(b.billDate)));
    setEditableRowKeys(data?.map(item => item.id));
    setCurrTotalReceivedAmountYuan(0);
    handleAlertMsg(totalReceivedAmountYuan, currTotalReceivedAmountYuan);
  };

  const handleAccountSelectChange = async (id, option) => {
    setAccountSelect(option);
  };

  const hideModal = () => {
    props.onCancel(false);
  };

  const handleUpdate = (record: FinReceivableEntity) => {
    setReceivableList((prevList) =>
      prevList.map((item) =>
        item.id === record.id ? { ...item, currReceivedAmount: record.currReceivedAmount } : item
      )
    );
  };

  useEffect(() => {
    let totalReceivedAmount = 0;
    receivableList?.forEach((item) => {
      if (item.currReceivedAmount) {
        totalReceivedAmount = _.round(_.add(Number(item.currReceivedAmount), totalReceivedAmount), 2);
      }
    });
    setCurrTotalReceivedAmountYuan(totalReceivedAmount);
    handleAlertMsg(totalReceivedAmountYuan, totalReceivedAmount);
  }, [receivableList]);

  const orderSearch = async (orderNo) => {
    if (!cstSelect) {
      return;
    }
    const data = await queryReceivableList({ buyerId: cstSelect.value, orderNo: orderNo, receivableFlag: 1 });
    setReceivableList(data?.sort((a, b) => new Date(a.billDate) - new Date(b.billDate)));
    setEditableRowKeys(data?.map(item => item.id));
    handleAlertMsg(totalReceivedAmountYuan, currTotalReceivedAmountYuan);
  };

  const handleAccountSelectRequest = async (query) => {
    const data = await queryMemberAccountPage({ memberAccountName: query.keyWords, pageSize: 1000 });
    const accountOptions = data?.data?.map(({ id, memberAccountName }) => ({
      value: id,
      label: memberAccountName,
    }));

    // 如果是首次加载数据，则设置默认值
    if (!accountSelect && data?.data && data.data.length > 0) {
      form.setFieldsValue({
        receivedAccountId: data.data[0].id,
      });
      setAccountSelect(accountOptions?.[0]);
    }
    return accountOptions;
  };

  return (
    <DrawerForm
      form={form}
      title={intl.formatMessage({ id: 'finance.receive.add' })}
      width={1080}
      layout="horizontal"
      open={props.visible}
      labelCol={labelCol}
      colProps={colProps}
      drawerProps={{
        maskClosable: false,
        destroyOnClose: true,
        classNames: {
          body: 'bg-[#f2f2f2]',
        },
        onClose: props.onCancel,
      }}
      submitter={{
        render: (props, doms) => {
          return (
            <Flex gap={40} key="summary" className="flex justify-between items-center w-full">
              <Space size={40}>
                <span className="flex flex-row items-center">
                  <span className="text-[16px] font-semibold text-[#000000D9]">{intl.formatMessage({ id: 'finance.receive.receivedAmount' })}：</span>
                  <span className="text-[24px] font-medium text-[#F83431]">￥{totalReceivedAmountYuan}</span>
                </span>
                <span className="flex flex-row items-center">
                  <span className="text-[16px] font-semibold text-[#000000D9]">{intl.formatMessage({ id: 'finance.receive.currentWriteOff' })}：</span>
                  <span className="text-[24px] font-medium text-[#F83431]">￥{currTotalReceivedAmountYuan}</span>
                </span>
                {alertMsg && <Alert message={alertMsg} banner className="bg-white text-yellow-500" />}
              </Space>
              <Space>
                <Button key="rest" danger onClick={() => hideModal()}>
                  {intl.formatMessage({ id: 'common.button.cancel' })}
                </Button>
                <Button type="primary" key="launch" onClick={() => props.form?.submit?.()}>
                  {intl.formatMessage({ id: 'finance.receive.confirm' })}
                </Button>
              </Space>
            </Flex>
          );
        },
      }}
      onFinish={async (formData: any) => {
        const selectedOrderDetailList = receivableList.filter(item => item.currReceivedAmount).map(item => ({
          receivableId: item.id,
          receivedAmountYuan: item.currReceivedAmount,
          ledgerType: item.ledgerType
        }));
        if (!selectedOrderDetailList || selectedOrderDetailList.length == 0) {
          message.warning(intl.formatMessage({ id: 'finance.receive.noOrderSelectedWarning' }));
          return false;
        }
        const totalReceivedAmount = selectedOrderDetailList.reduce((accumulator, currentItem) => {
          return _.add(accumulator, Number(currentItem.receivedAmountYuan));
        }, 0);
        const inTotalReceivedAmount = formData.totalReceivedAmountYuan as number;
        if (inTotalReceivedAmount != totalReceivedAmount) {
          message.error(intl.formatMessage({ id: 'finance.receive.amountMismatchError' }));
          return false;
        }

        const result = await receivedConfirmation({
          ...formData,
          receivedAccountName: accountSelect?.label,
          buyerName: cstSelect?.label,
          ledgerType: inTotalReceivedAmount > 0 ? 1 : 2,
          finReceivedOrderDetailCmdList: selectedOrderDetailList
        });
        if (!result) {
          return false;
        }
        return props.onOk();
      }}
    >
      <ProCard className="rounded-lg" style={{ display: 'flex', alignItems: 'flex-start' }}>
        <ProFormSelect
          width={width}
          required
          rules={[REQUIRED_RULES]}
          name="buyerId"
          label={intl.formatMessage({ id: 'finance.receive.columns.customerName' })}
          labelCol={{ span: 6 }}
          showSearch
          request={
            async (query) => {
              const data = await getCstList({ keyword: query.keyWords, cstStatus: 0 });
              return data?.map(({ cstId, cstName }) => ({
                value: cstId,
                label: cstName,
              }));
            }
          }
          onChange={handleCstSelectChange}
        />
        <Flex justify="space-around" align="flex-start">
          <ProFormMoney
            width={width}
            required
            name="totalReceivedAmountYuan"
            label={intl.formatMessage({ id: 'finance.receive.columns.receivedAmount' })}
            labelCol={{ span: 7 }}
            rules={[REQUIRED_RULES]}
          />
          <Button
            type="primary"
            size="middle"
            style={{ marginLeft: 10 }}
            onClick={() =>
              autoAssignOrders()
            }
          >
            {intl.formatMessage({ id: 'finance.receive.autoAssign' })}
          </Button>
        </Flex>
        <ProFormSelect
          width={width}
          required
          name="receivedAccountId"
          label={intl.formatMessage({ id: 'finance.receive.columns.receivedAccount' })}
          labelCol={{ span: 6 }}
          showSearch
          rules={[REQUIRED_RULES]}
          onChange={handleAccountSelectChange}
          request={handleAccountSelectRequest}
        />
      </ProCard>
      <FunProTable<FinReceivableEntity, any>
        scroll={{ x: 'max-content' }}
        className="mt-4"
        title={() => (
          <Flex justify="space-between" align="flex-end">
            <LeftTitle title={intl.formatMessage({ id: 'finance.receive.writeOffOrder' })} />
            <Search
              placeholder={intl.formatMessage({ id: 'finance.receive.placeholders.businessOrderNo' })}
              allowClear
              onSearch={orderSearch}
              style={{ width: 300 }}
              key={dayjs().valueOf()}
            />
          </Flex>
        )}
        search={false}
        pagination={false}
        editable={{
          editableKeys
        }}
        dataSource={receivableList}
        options={false}
        columns={getCreateColumns(intl, { handleUpdate })}
      />
      <ProCard style={{ display: 'flex', alignItems: 'flex-start' }}>
        <ProFormTextArea name="remark" label={intl.formatMessage({ id: 'finance.receive.columns.remark' })} width={600} labelCol={{ span: 6 }} />
      </ProCard>
    </DrawerForm>
  );
};
