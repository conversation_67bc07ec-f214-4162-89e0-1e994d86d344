import LeftTitle from '@/components/LeftTitle';
import FunProTable from '@/components/common/FunProTable';
import { LedgerTypeEnum } from "@/pages/finance/customer/types/LedgerType";
import { queryReceivedFlowList } from "@/pages/finance/receive/services";
import type { ReceivedDetailModalType } from "@/pages/finance/receive/types/ReceivedDetailModalType";
import type { ReceivedEntity } from "@/pages/finance/receive/types/ReceivedEntity";
import type { ReceivedFlowEntity } from "@/pages/finance/receive/types/ReceivedFlowEntity";
import { DrawerForm, ProCard, ProDescriptions } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { useState } from "react";
import { getDetailColumns } from '../../config/DetailColumns';
export default (props: ReceivedDetailModalType) => {
  const intl = useIntl();
  const [finReceivedRo, setFinReceivedRo] = useState<ReceivedEntity>(null);
  const [finReceivedFlowRoList, setFinReceivedFlowRoList] = useState<ReceivedFlowEntity[]>([]);

  useAsyncEffect(async () => {
    if (!props.serialNumber) {
      return;
    }
    const receivedFlow = await queryReceivedFlowList(props.serialNumber);
    setFinReceivedRo({ ...receivedFlow?.finReceivedRo, receivedAccountName: receivedFlow?.finReceivedRo?.receivedAccountName || '' });
    setFinReceivedFlowRoList(
      receivedFlow?.finReceivedFlowRoList?.map(flow => ({
        ...flow,
        storeName: receivedFlow?.finReceivedRo?.storeName,
        receivedAmount: flow.receivedAmount || '0',
      }))
    );
  }, [props.visible]);

  return (
    <DrawerForm
      title={props.title}
      width={1080}
      drawerProps={{
        classNames: {
          body: 'bg-[#f2f2f2]',
        },
        maskClosable: false,
        onClose: props.onCancel,
      }}
      open={props.visible}
      submitter={false}
    >
      <ProCard className="mb-4">
        <ProDescriptions<ReceivedEntity, any>
          title={props.serialNumber}
          column={2}
          dataSource={finReceivedRo}
          columns={[
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.customerName' }),
              dataIndex: 'buyerName',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.receiveType' }),
              dataIndex: 'ledgerType',
              valueEnum: LedgerTypeEnum,
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.receivedAccount' }),
              dataIndex: 'receivedAccountName',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.receivedAmount' }),
              dataIndex: 'totalReceivedAmountYuan',
              valueType: 'money',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.receiveTime' }),
              dataIndex: 'businessTime',
              valueType: 'date',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.creator' }),
              dataIndex: 'createPerson',
            },
            {
              title: intl.formatMessage({ id: 'finance.receive.columns.remark' }),
              dataIndex: 'remark',
            },
          ]}
        />
      </ProCard>
      <FunProTable<ReceivedFlowEntity, any>
        headerTitle={<LeftTitle title={intl.formatMessage({ id: 'finance.receive.writeOffOrder' })} />}
        scroll={{ x: 'max-content' }}
        search={false}
        pagination={false}
        dataSource={finReceivedFlowRoList}
        options={false}
        columns={getDetailColumns(intl)}
      />
    </DrawerForm>
  );
};
