import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { type ReceivedEntity } from './types/ReceivedEntity';
import { ReceivedFlowDetailEntity } from '@/pages/finance/receive/types/ReceivedFlowDetailEntity';
import { ReceivedConfirmation } from '@/pages/finance/receive/types/ReceivedConfirmation';

/**
 * 收款分页查询
 *
 * @param params
 * @returns
 */
export const queryReceivedPage = async (
  params: Partial<ReceivedEntity> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<ReceivedEntity>>(`/ipmsaccount/queryRecevedPage`, {
    data: params,
  });
};
/**
 * 查询实收流水
 *
 * @param params
 * @returns
 */
export const queryReceivedFlowList = async (params: string) => {
  return request<PageResponseDataType<ReceivedFlowDetailEntity>>(
    `/ipmsaccount/queryReceivedFlowList`,
    {
      data: { receivedSerialNumber: params },
    },
  );
};
/**
 * 实收
 *
 * @param params
 * @returns
 */
export const receivedConfirmation = async (params: ReceivedConfirmation) => {
  return request<PageResponseDataType<string>>(`/ipmsaccount/receivedConfirmation`, {
    data: params,
  });
};
