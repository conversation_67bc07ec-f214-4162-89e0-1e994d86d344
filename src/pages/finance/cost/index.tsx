import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import ExpendTableColumns from '@/pages/finance/cost/config/ExpendTableColumns';
import IncomeTableColumns from '@/pages/finance/cost/config/IncomeTableColumns';
import { FinanceCostEntity } from '@/pages/finance/cost/types/FinanceCostEntity.entity';
import { PropertyModalFromType } from '@/types/PropertyModalFromType';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer, ProCard, ProColumns } from '@ant-design/pro-components';
import { ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import type { TabsProps } from 'antd';
import { Popconfirm, Space, Tabs, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import ExpendCostFormModal from './components/ExpendCostFormModal';
import IncomeCostFormModal from './components/IncomeCostFormModal';
import { createCostFlow, invalidCostFlow, queryCostFlowPage } from './services';

const FinCost = () => {
  const intl = useIntl();
  const [createBtnText, setCreateBtnText] = useState<string>(intl.formatMessage({ id: 'finance.cost.income' }));
  const [rowKey, setRowKey] = useState<RowKeyType>('id');
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ActionType>();
  const [columns, setColumns] = useState<ProColumns<FinanceCostEntity>[]>(IncomeTableColumns({ intl }));
  const [tabActiveKey, setTabActiveKey] = useState<string>('Income');

  useActivate(() => {
    actionRef.current?.reload();
  });

  useEffect(() => {
    const operatorColumn: {
      valueType: string;
      width: number;
      title: string;
      render: (_text, record, _, action) => null | JSX.Element;
    } = {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      valueType: 'option',
      width: 80,
      render: (_text, record, _, action) => {
        return (
          <Popconfirm title={intl.formatMessage({ id: 'finance.cost.confirmInvalidate' })} onConfirm={() => onDelete(record)}>
            <AuthButton isHref authority={`delete${tabActiveKey}`} key="delete">
              {intl.formatMessage({ id: 'finance.cost.invalidate' })}
            </AuthButton>
          </Popconfirm>
        );
      },
    };
    let newColumns: ProColumns<FinanceCostEntity>[] = [];
    let btnText = '';
    let tableRowKey: RowKeyType = 'id';
    if (tabActiveKey == 'Income') {
      btnText = intl.formatMessage({ id: 'finance.cost.income' });
      tableRowKey = 'id';
      newColumns = IncomeTableColumns({ tabActiveKey, intl });
    } else if (tabActiveKey == 'Expend') {
      newColumns = ExpendTableColumns({ tabActiveKey, intl });
      tableRowKey = 'id';
      btnText = intl.formatMessage({ id: 'finance.cost.expend' });
    }
    setRowKey(tableRowKey);
    setCreateBtnText(btnText);
    // @ts-ignore
    setColumns([...newColumns, operatorColumn]);
  }, [rowKey, tabActiveKey]);

  const [createIncomeModalProps, setCreateIncomeModalProps] = useState<
    PropertyModalFromType<number>
  >({
    inputFieldLabel: '',
    inputFieldName: '',
    onCancel(): void { },
    onOk(value: any): Promise<boolean | void> {
      return Promise.resolve(undefined);
    },
    visible: false,
    recordId: 0,
    readOnly: false,
    title: `${intl.formatMessage({ id: 'finance.cost.add' })} ${createBtnText}`,
  });

  const [createExpendModalProps, setCreateExpendModalProps] = useState<
    PropertyModalFromType<number>
  >({
    inputFieldLabel: '',
    inputFieldName: '',
    onCancel(): void { },
    onOk(value: any): Promise<boolean | void> {
      return Promise.resolve(undefined);
    },
    visible: false,
    recordId: 0,
    readOnly: false,
    title: `${intl.formatMessage({ id: 'finance.cost.add' })} ${createBtnText}`,
  });

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    if (tabActiveKey == 'Income') {
      setCreateIncomeModalProps((preModalProps) => ({
        ...preModalProps,
        visible: false,
        recordId: 0,
        readOnly: false,
      }));
    } else {
      setCreateExpendModalProps((preModalProps) => ({
        ...preModalProps,
        visible: false,
        recordId: 0,
        readOnly: false,
      }));
    }
  };

  /**
   * 新增保存
   * @param values
   */
  const handleSave = async (values: FinanceCostEntity) => {
    const result = await createCostFlow(values);
    if (result) {
      hideModal();
      actionRef.current?.reset?.();
      actionRef.current?.reload(true);
      return true;
    }
  };

  /**
   * 删除
   * @param ids
   */
  const onDelete = async (record: FinanceCostEntity) => {
    const result = await invalidCostFlow(record);
    if (result) {
      message.success(intl.formatMessage({ id: 'finance.cost.invalidateSuccess' }));
      actionRef.current?.reset?.();
      actionRef.current?.reload(true);
      return true;
    }
  };

  /**
   * 头部tabs
   */
  const items: TabsProps['items'] = [
    {
      key: 'Income',
      label: intl.formatMessage({ id: 'finance.cost.otherIncome' }),
    },
    {
      key: 'Expend',
      label: intl.formatMessage({ id: 'finance.cost.otherExpend' }),
    },
  ];

  // 切换tabs
  const onChange = (key: string) => {
    actionRef.current?.reload(true);
    // 切换tabs清空搜索条件
    formRef.current?.resetFields();
    setTabActiveKey(key);
  };

  return (
    <PageContainer>
      <ProCard bodyStyle={{ paddingLeft: 24, paddingTop: 0, paddingBottom: 0 }}>
        <Tabs
          defaultActiveKey="Income"
          items={items}
          onChange={onChange}
          tabBarStyle={{ marginBottom: 0 }}
        />
      </ProCard>
      <FunProTable<FinanceCostEntity, any>
        rowKey={rowKey}
        requestPage={(params) =>
          queryCostFlowPage({ ...params, ledgerType: tabActiveKey == 'Income' ? 1 : 2 })
        }
        actionRef={actionRef}
        formRef={formRef}
        scroll={{ x: 'max-content' }}
        columns={columns}
        headerTitle={
          <Space>
            <AuthButton
              authority={`add${tabActiveKey}`}
              type="primary"
              key="crete"
              onClick={() => {
                if (tabActiveKey == 'Income') {
                  setCreateIncomeModalProps((preModalProps) => ({
                    ...preModalProps,
                    // inputFieldName: inputFieldNameMap[tabActiveKey],
                    // inputFieldLabel: inputFieldLabelMap[tabActiveKey],
                    visible: true,
                    readOnly: false,
                    title: `${intl.formatMessage({ id: 'finance.cost.add' })} ${createBtnText}`,
                  }));
                } else {
                  setCreateExpendModalProps((preModalProps) => ({
                    ...preModalProps,
                    // inputFieldName: inputFieldNameMap[tabActiveKey],
                    // inputFieldLabel: inputFieldLabelMap[tabActiveKey],
                    visible: true,
                    readOnly: false,
                    title: `${intl.formatMessage({ id: 'finance.cost.add' })} ${createBtnText}`,
                  }));
                }
              }}
            >
              {intl.formatMessage({ id: 'finance.cost.add' })} {createBtnText}
            </AuthButton>
          </Space>
        }
      />
      <IncomeCostFormModal {...createIncomeModalProps} onCancel={hideModal} onOk={handleSave} />
      <ExpendCostFormModal {...createExpendModalProps} onCancel={hideModal} onOk={handleSave} />
    </PageContainer>
  );
};

export default withKeepAlive(FinCost);