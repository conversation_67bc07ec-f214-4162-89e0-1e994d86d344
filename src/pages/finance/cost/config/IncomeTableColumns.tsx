import type { FinanceCostEntity } from '@/pages/finance/cost/types/FinanceCostEntity.entity';
import { queryTagList } from '@/pages/finance/tag/services';
import { queryStoreByAccount } from '@/pages/system/user/services';
import type { ProColumns } from '@ant-design/pro-components';

export default (props = {}): ProColumns<FinanceCostEntity>[] => {
  const { intl } = props;
  return [
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.store' }),
      dataIndex: 'storeIdList',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
        showSearch: true,
      },
      formItemProps: {
        name: 'storeIdList',
      },
      request: async () => {
        const data = await queryStoreByAccount({});
        return data?.map(({ id, name }) => ({
          value: id,
          label: name,
        }));
      },
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.incomeType' }),
      dataIndex: 'tagId',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'tagName', value: 'id' },
      },
      params: { tabActiveKey: props?.tabActiveKey },
      request: async (query) => await queryTagList({ ledgerType: 1, ...query }),
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.createTime' }),
      dataIndex: 'createTime',
      width: 140,
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value: any) => {
          return {
            startBizTime: value[0],
            endBizTime: value[1],
          };
        },
      },
    },

    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      editable: false,
      fixed: 'left',
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.createTime' }),
      dataIndex: 'createTime',
      width: 140,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.serialNumber' }),
      dataIndex: 'serialNumber',
      width: 160,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.incomeStore' }),
      dataIndex: 'storeName',
      width: 140,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.incomeType' }),
      dataIndex: 'tagName',
      width: 120,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.incomeAmount' }),
      dataIndex: 'amountYuan',
      valueType: 'money',
      width: 120,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.settlementAccount' }),
      dataIndex: 'accountName',
      width: 120,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.cost.columns.creator' }),
      dataIndex: 'createPerson',
      width: 60,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'common.column.remark' }),
      dataIndex: 'remark',
      width: 140,
      search: false,
    },
  ] as ProColumns<FinanceCostEntity>[];
};
