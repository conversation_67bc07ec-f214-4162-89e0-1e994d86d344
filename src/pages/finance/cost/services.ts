import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { FinanceCostEntity } from '@/pages/finance/cost/types/FinanceCostEntity.entity';

export const queryCostFlowPage = (params: Partial<FinanceCostEntity> & PageRequestParamsType) => {
  return request<PageResponseDataType<FinanceCostEntity>>(`/ipmsaccount/queryCostFlowPage`, {
    data: params,
  });
};

export const invalidCostFlow = async (params: FinanceCostEntity) => {
  return request<boolean>(`/ipmsaccount/invalidCostFlow`, {
    data: params,
  });
};

export const createCostFlow = async (params: FinanceCostEntity) => {
  return request<boolean>(`/ipmsaccount/createCostFlow`, {
    data: params,
  });
};
