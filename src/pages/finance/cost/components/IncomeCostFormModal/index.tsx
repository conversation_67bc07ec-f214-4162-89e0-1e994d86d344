import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { queryTagList } from '@/pages/finance/tag/services';
import { queryStoreByAccount } from '@/pages/system/user/services';
import { type PropertyModalFromType } from '@/types/PropertyModalFromType';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { ProFormDigit, ProFormTextArea } from '@ant-design/pro-components';
import { ModalForm, ProFormSelect } from '@ant-design/pro-form';
import { useIntl } from '@umijs/max';
import { useForm } from 'antd/lib/form/Form';
import { useEffect } from 'react';

export default ({
  title,
  recordId,
  visible,
  onCancel,
  onOk,
  readOnly,
  inputFieldName,
  inputFieldLabel,
}: PropertyModalFromType<number>) => {
  const intl = useIntl();
  const [form] = useForm();

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [visible]);

  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };
  return (
    <ModalForm
      {...layout}
      form={form}
      layout="horizontal"
      title={title}
      open={visible}
      width="40%"
      modalProps={{
        maskClosable: false,
        centered: true,
        onCancel: onCancel,
      }}
      submitTimeout={2000}
      onFinish={onOk}
    >
      <ProFormSelect
        rules={[REQUIRED_RULES]}
        name="storeId"
        label={intl.formatMessage({ id: 'finance.cost.form.incomeStore' })}
        mode="single"
        fieldProps={{
          showSearch: true,
          fieldNames: { label: 'name', value: 'id' },
        }}
        request={async () => {
          const result = await queryStoreByAccount({ status: 1 });
          if (result.length > 0) {
            form.setFieldValue('storeId', result[0].id);
          }
          return result;
        }}
      />

      <ProFormSelect
        rules={[REQUIRED_RULES]}
        name="tagId"
        label={intl.formatMessage({ id: 'finance.cost.form.incomeType' })}
        mode="single"
        fieldProps={{
          showSearch: true,
          fieldNames: { label: 'tagName', value: 'id' },
        }}
        request={async () => await queryTagList({ ledgerType: 1 })}
      />

      <ProFormDigit
        min={0.01}
        name="amountYuan"
        label={intl.formatMessage({ id: 'finance.cost.form.incomeAmount' })}
        rules={[REQUIRED_RULES]}
        fieldProps={{
          controls: false,
          precision: 2,
          addonAfter: intl.formatMessage({ id: 'finance.cost.form.yuan' }),
        }}
      />

      <ProFormSelect
        rules={[REQUIRED_RULES]}
        name="accountId"
        label={intl.formatMessage({ id: 'finance.cost.form.settlementAccount' })}
        mode="single"
        fieldProps={{
          showSearch: true,
        }}
        request={async (query) => {
          const data = await queryMemberAccountPage({ pageSize: 1000 });
          if (data?.data?.length) {
            form.setFieldValue('accountId', data?.data[0].id);
          }
          return data?.data?.map(({ id, memberAccountName }) => ({
            value: id,
            label: memberAccountName,
          }));
        }}
      />

      <ProFormTextArea name="remark" label={intl.formatMessage({ id: 'finance.cost.form.incomeRemark' })} />
    </ModalForm>
  );
};
