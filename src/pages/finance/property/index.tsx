import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { type PropertyModalFromType } from '@/types/PropertyModalFromType';
import { PageContainer, ProCard, type ProColumns } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import type { TabsProps } from 'antd';
import { Space, Tabs } from 'antd';
import { useEffect, useRef, useState } from 'react';
import FinancePropertyFormModal from './components/FinancePropertyFormModal';
import InoutTypeColumns from './config/InoutTypeColumns';
import { addTag, editTag, queryCustomerPropertyPage } from './services';
import type { CustomerPropertyTagEntity } from './types/CustomerPropertyTagEntity';

const inputFieldLabelMap: Record<string, string> = {
  inoutType: '收支类型',
};
const inputFieldNameMap: Record<string, string> = {
  inoutType: 'tagName',
};
export default () => {
  const [createBtnText, setCreateBtnText] = useState<string>('收支类型');
  const actionRef = useRef<ActionType>();

  const [columns, setColumns] = useState<ProColumns<CustomerPropertyTagEntity>[]>(InoutTypeColumns);
  // 动态设置
  const [tabActiveKey, setTabActiveKey] = useState<string>('inoutType');
  useEffect(() => {
    const operatorColumn: ProColumns<CustomerPropertyTagEntity> = {
      title: '操作',
      valueType: 'option',
      render: (_text, record, _, action) => {
        const { source } = record;
        if (source == 0) {
          return null;
        }
        return (
          <AuthButton
            isHref
            key="create"
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          >
            编辑
          </AuthButton>
        );
      },
    };
    let newColumns: ProColumns<CustomerPropertyTagEntity>[] = [];
    let btnText = '';
    if (tabActiveKey == 'inoutType') {
      btnText = '收支类型';
      newColumns = InoutTypeColumns;
    }
    setCreateBtnText(btnText);
    setColumns(() => [...newColumns, operatorColumn]);
  }, [tabActiveKey]);

  const [createModalProps, setCreateModalProps] = useState<PropertyModalFromType<number>>({
    inputFieldName: inputFieldNameMap[tabActiveKey],
    inputFieldLabel: inputFieldLabelMap[tabActiveKey],
    visible: false,
    recordId: 0,
    readOnly: false,
    title: `新增${createBtnText}`,
  });
  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setCreateModalProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      recordId: 0,
      readOnly: false,
    }));
  };

  /**
   * 新增或编辑
   * @param values
   */
  const handleSaveOrUpdate = async (values: any) => {
    try {
      const result = await addTag({ ...values, tagType: 1 });
      if (result) {
        hideModal();
        actionRef.current?.reload(true);
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  const items: TabsProps['items'] = [
    {
      key: 'inoutType',
      label: '收支类型管理',
    },
  ];
  const onChange = (key: string) => {
    actionRef.current?.reload(true);
    setTabActiveKey(key);
  };
  // 编辑-保存
  const onEditSave = (_key: React.Key | React.Key[], record: CustomerPropertyTagEntity) => {
    return editTag(record);
  };
  return (
    <PageContainer>
      <ProCard bodyStyle={{ paddingLeft: 24, paddingTop: 0, paddingBottom: 0 }}>
        <Tabs
          defaultActiveKey="inoutType"
          tabBarStyle={{ marginBottom: 0 }}
          items={items}
          onChange={onChange}
        />
      </ProCard>
      <FunProTable<CustomerPropertyTagEntity, CustomerPropertyTagEntity>
        editable={{
          type: 'single',
          onSave: onEditSave,
          actionRender: (_row, _config, defaultDom) => {
            return [defaultDom.save, defaultDom.cancel];
          },
        }}
        rowKey="id"
        requestPage={(params) => queryCustomerPropertyPage({ ...params, tagType: 1 })}
        actionRef={actionRef}
        columns={columns}
        scroll={{ x: 'max-content' }}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              key="create"
              onClick={() => {
                setCreateModalProps((preModalProps) => ({
                  ...preModalProps,
                  inputFieldName: inputFieldNameMap[tabActiveKey],
                  inputFieldLabel: inputFieldLabelMap[tabActiveKey],
                  visible: true,
                  readOnly: false,
                  title: `${createBtnText}添加`,
                }));
              }}
            >
              新增{createBtnText}
            </AuthButton>
          </Space>
        }
      />
      <FinancePropertyFormModal
        {...createModalProps}
        onCancel={hideModal}
        onOk={handleSaveOrUpdate}
      />
    </PageContainer>
  );
};
