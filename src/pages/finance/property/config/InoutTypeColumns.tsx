import { CommonStatusValueEnum } from '@/types/CommonStatus';
import { type ProColumns } from '@ant-design/pro-components';
import type { CustomerPropertyTagEntity } from '../types/CustomerPropertyTagEntity';

export default [
  {
    title: '序号',
    valueType: 'index',
    editable: false,
    width: 40,
  },
  {
    title: '收支类型',
    dataIndex: 'tagName',
    editable: () => true,
  },
  {
    title: '收支方向',
    dataIndex: 'sourceName',
    search: false,
    editable: () => true,
  },
  {
    title: '来源',
    dataIndex: 'sourceName',
    search: false,
    editable: false,
  },
  {
    title: '是否启用',
    dataIndex: 'tagStatus',
    search: false,
    valueType: 'select',
    editable: () => true,
    valueEnum: CommonStatusValueEnum,
  },
] as ProColumns<CustomerPropertyTagEntity>[];
