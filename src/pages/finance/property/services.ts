import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { type CustomerPriceLevelEntity } from './types/CustomerPriceLevelEntity';
import { type CustomerTagEntity } from './types/CustomerPropertyTagEntity';
export type CustomerPropertyTableType = CustomerTagEntity & CustomerPriceLevelEntity;
/**
 * 用户管理-分页查询客户标签
 *
 * @param params
 * @returns
 */
export const queryCustomerPropertyPage = async (params: CustomerTagEntity) => {
  return request<PageResponseDataType<CustomerTagEntity>>(
    `/ipmscst/CstTagManageFacade/getTagPaged`,
    {
      data: params,
    },
  );
};
/**
 * 用户管理-新增属性标签
 *
 * @param params
 * @returns
 */
export const addTag = async (params: CustomerTagEntity) => {
  return request<boolean>(`/ipmscst/CstTagManageFacade/addTag`, {
    data: params,
  });
};

/**
 * 用户管理-修改属性标签
 *
 * @param params
 * @returns
 */
export const editTag = async (params: CustomerTagEntity) => {
  return request<boolean>(`/ipmscst/CstTagManageFacade/editTag`, {
    data: params,
  });
};
