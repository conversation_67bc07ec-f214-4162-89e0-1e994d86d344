/**
 * 客户属性-价格级别
 */
export interface CustomerPriceLevelEntity {
  /**三方数据源编码*/
  sourceCode: string;
  /**状态0删除1启用2禁用*/
  categoryStatus: number;
  /**类目类型：0-商品类1-服务类2-虚拟类*/
  categoryType: number;
  /**类目ID*/
  id: string;
  /**类目名称*/
  categoryName: string;
  /**门店零售商id*/
  memberId: string;
  /**备注*/
  remark: string;
  /**类目层级（1-一级类目，2-二级类目，3-三级类目）*/
  grade: number;
  /**创建人 */
  createPerson: string;
  /**修改人 */
  updatePerson: string;
  /**是否删除 */
  isDelete: number;
  /**创建时间 */
  createTime: Date;
  /**更新时间 */
  updateTime: Date;
}
