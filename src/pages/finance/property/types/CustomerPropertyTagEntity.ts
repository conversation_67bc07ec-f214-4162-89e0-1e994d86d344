import type { PageRequestParamsType } from '@/types/PageRequestParamsType';

/**
 * 客户属性-客户标签
 */
export interface CustomerPropertyTagEntity extends PageRequestParamsType {
  /** 零售商ID */
  memberId: string;
  /** 来源0=系统配置1=自建 */
  sourceName: string;
  /** 状态: 0=启用 1=禁用 */
  tagStatus: number;
  /** 状态名称 */
  tagStatusName: string;
  /** 标签类型: 1=客户标签 2=价格级别 */
  tagType: 1 | 2;
  /** 来源: 0=系统配置 1=自建 */
  source: number;
  /** 标签名称 */
  tagName: string;
  /** 主键 */
  id: string;
}
