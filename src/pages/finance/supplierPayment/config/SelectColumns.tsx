import type { ProColumns } from '@ant-design/pro-components';
import { type GoodsEntity } from '../types/FinPaymentEntity';

export const SelectColumns: ProColumns<GoodsEntity>[] = [
  {
    title: '序号',
    valueType: 'index',
    fixed: 'left',
    width: 40,
  },

  {
    title: '业务单号',
    dataIndex: 'itemSn',
    width: 100,
  },
  {
    title: '交易时间',
    dataIndex: 'oeNoList',
    width: 140,
    valueType: 'date',
  },
  {
    title: '订单金额',
    dataIndex: '12oeNoList',
    search: false,
    width: 100,
    valueType: 'money',
  },
  {
    title: '已付金额',
    dataIndex: '12oeNoList',
    search: false,
    width: 100,
    valueType: 'money',
  },
  {
    title: '未付金额',
    dataIndex: '12oeNoList1',
    search: false,
    width: 100,
    valueType: 'money',
  },
];
