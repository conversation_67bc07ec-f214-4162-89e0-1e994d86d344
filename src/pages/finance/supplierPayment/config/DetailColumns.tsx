import type { FinPaymentFlowEntity } from '@/pages/finance/supplierPayment/types/FinPaymentFlowEntity';
import type { ProColumns } from '@ant-design/pro-components';

export const DetailColumns: ProColumns<FinPaymentFlowEntity>[] = [
  {
    title: '序号',
    valueType: 'index',
    fixed: 'left',
    width: 40,
  },
  {
    title: '门店名称',
    dataIndex: 'storeName',
    fixed: 'left',
    width: 100,
  },
  {
    title: '业务单号',
    dataIndex: 'orderNo',
    width: 100,
  },
  {
    title: '业务完成时间',
    dataIndex: 'billDate',
    width: 140,
  },
  {
    title: '单据金额',
    dataIndex: 'orderAmountYuan',
    width: 100,
    valueType: 'money',
  },
  {
    title: '核销金额',
    dataIndex: 'paymentAmountYuan',
    width: 100,
    valueType: 'money',
  },
];
