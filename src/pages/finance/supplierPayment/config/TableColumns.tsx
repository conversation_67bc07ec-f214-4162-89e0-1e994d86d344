import type { FinPaymentEntity } from '@/pages/finance/supplierPayment/types/FinPaymentEntity';
import { querySupplierList } from '@/pages/purchase/supplier/services';
import { accountListQuerySimple } from '@/pages/system/user/services';
import type { ProColumns } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';

export const getTableColumns = (intl: IntlShape): ProColumns<FinPaymentEntity>[] => [
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentStore' }),
    dataIndex: 'buyerName',
    search: false,
    width: 120,
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentTime' }),
    dataIndex: 'businessTimeQuery',
    width: 140,
    valueType: 'dateRange',
    hideInTable: true,
    search: {
      transform: (value: any) => {
        return {
          startBusinessTime: value[0],
          endBusinessTime: value[1],
        };
      },
    },
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentTime' }),
    dataIndex: 'businessTime',
    search: false,
    width: 140,
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.supplier' }),
    dataIndex: 'sellerName',
    search: false,
    width: 100,
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.supplier' }),
    dataIndex: 'sellerId',
    width: 120,
    hideInTable: true,
    request: async () => {
      const data = await querySupplierList({});
      return data?.map(({ supplierId, supplierName }) => ({
        key: supplierId,
        value: supplierId,
        label: supplierName,
      }));
    },
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentAccount' }),
    dataIndex: 'paymentAccountName',
    search: false,
    width: 100,
    valueType: 'money',
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentAmount' }),
    dataIndex: 'totalPaymentAmountYuan',
    search: false,
    width: 100,
    valueType: 'money',
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.creator' }),
    dataIndex: 'createPerson',
    width: 60,
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'finance.supplierPayment.columns.creator' }),
    dataIndex: 'createPerson',
    key: 'createPerson',
    search: true,
    width: 60,
    valueType: 'select',
    hideInTable: true,
    fieldProps: {
      showSearch: true,
      fieldNames: { label: 'name', value: 'id' },
    },
    request: (query: any) => {
      return accountListQuerySimple({ name: query.keyWords });
    },
  },
  {
    title: intl.formatMessage({ id: 'common.column.remark' }),
    dataIndex: 'remark',
    search: false,
    width: 100,
  },
];
