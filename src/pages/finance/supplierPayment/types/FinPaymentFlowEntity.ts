export interface FinPaymentFlowEntity {
  paymentAccountId?: string;
  paymentAccountName?: string;
  buyerName?: string;
  storeId?: string;
  totalPaymentAmount?: string;
  totalPaymentAmountYuan?: string;
  storeName?: string;
  businessTime?: string;
  buyerId?: string;
  sellerId?: string;
  ledgerType?: string;
  sellerName?: string;
  serialNumber?: string;
  createPerson?: string;
  remark?: string;
  orderAmountYuan?: string;
}
