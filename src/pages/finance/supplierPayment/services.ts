import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { PaymentFlowDetailEntity } from '@/pages/finance/supplierPayment/types/PaymentFlowDetailEntity';
import { FinPaymentEntity } from '@/pages/finance/supplierPayment/types/FinPaymentEntity';
import { FinPayableEntity } from '@/pages/finance/payment/types/FinPayableEntity';
import { ReceivedConfirmation } from '@/pages/finance/receive/types/ReceivedConfirmation';
import { PaymentConfirmation } from '@/pages/finance/supplierPayment/types/PaymentConfirmation';

/**
 * 付款分页查询
 *
 * @param params
 * @returns
 */
export const queryPaymentPage = async (
  params: Partial<FinPaymentEntity> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<FinPaymentEntity>>(`/ipmsaccount/queryPaymentPage`, {
    data: params,
  });
};
/**
 * 付款流水
 *
 * @param params
 * @returns
 */
export const queryPaymentFlowList = async (params: string) => {
  return request<PageResponseDataType<PaymentFlowDetailEntity>>(
    `/ipmsaccount/queryPaymentFlowList`,
    {
      data: { paymentSerialNumber: params },
    },
  );
};

/**
 * 应付列表
 *
 * @param params
 * @returns
 */
export const queryPayableList = async (
  params: Partial<FinPayableEntity> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<FinPayableEntity>>(`/ipmsaccount/queryPayableList`, {
    data: params,
  });
};

/**
 * 实付
 *
 * @param params
 * @returns
 */
export const paymentConfirmation = async (params: PaymentConfirmation) => {
  return request<PageResponseDataType<string>>(`/ipmsaccount/paymentConfirmation`, {
    data: params,
  });
};
