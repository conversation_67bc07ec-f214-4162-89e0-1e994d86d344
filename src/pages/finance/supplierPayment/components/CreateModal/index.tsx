import LeftTitle from '@/components/LeftTitle';
import FunProTable from '@/components/common/FunProTable';
import { queryMemberAccountPage } from "@/pages/finance/customer/services";
import type { FinPayableEntity } from "@/pages/finance/payment/types/FinPayableEntity";
import CreateColumns from "@/pages/finance/supplierPayment/config/CreateColumns";
import { paymentConfirmation, queryPayableList } from "@/pages/finance/supplierPayment/services";
import type { PaymentDetailModalType } from "@/pages/finance/supplierPayment/types/PaymentDetailModalType";
import { querySupplierList } from "@/pages/purchase/supplier/services";
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import {
  DrawerForm,
  ProCard,
  ProFormMoney,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from "ahooks";
import { Alert, Button, Flex, Form, Input, message, Space } from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from "react";

const labelCol = { span: 4 };
const colProps = { span: 20 };
const width = 'md';
export default (props: PaymentDetailModalType) => {
  const intl = useIntl();
  const { Search } = Input;
  const [form] = Form.useForm();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [paymentList, setPaymentList] = useState<FinPayableEntity[]>([]);
  const [supplierSelect, setSupplierSelect] = useState();
  const [accountSelect, setAccountSelect] = useState();
  const [totalPaymentAmountYuan, setTotalPaymentAmountYuan] = useState<number>(0);
  const [currTotalPaymentAmountYuan, setCurrTotalPaymentAmountYuan] = useState<number>(0);
  const [alertMsg, setAlertMsg] = useState<string>('');

  useAsyncEffect(async () => {
    if (!props.visible) {
      form.resetFields();
      setPaymentList([]);
      setEditableRowKeys([]);
      setSupplierSelect(undefined);
      setAccountSelect(undefined);
      setTotalPaymentAmountYuan(0);
      setCurrTotalPaymentAmountYuan(0);
      setAlertMsg("");
    }
  }, [props.visible]);

  const watchedTotalPaymentAmountYuanValue = Form.useWatch('totalPaymentAmountYuan', form);
  useAsyncEffect(async () => {
    setTotalPaymentAmountYuan(watchedTotalPaymentAmountYuanValue)
    handleAlertMsg(watchedTotalPaymentAmountYuanValue, currTotalPaymentAmountYuan);
  }, [watchedTotalPaymentAmountYuanValue]);

  const handleAlertMsg = (totalPaymentAmount, currTotalPaymentAmount) => {
    if (totalPaymentAmount != currTotalPaymentAmount) {
      setAlertMsg(intl.formatMessage({ id: 'finance.supplierPayment.writeOffAmountMismatch' }));
      return;
    }
    setAlertMsg("");
  };

  const autoAssignOrders = () => {
    let totalPaymentAmountYuan: number = form.getFieldValue("totalPaymentAmountYuan");
    const oldTotalPaymentAmountYuan = totalPaymentAmountYuan;
    if (!totalPaymentAmountYuan == undefined) {
      message.error(intl.formatMessage({ id: 'finance.supplierPayment.noAmountError' }));
      return;
    }
    if (totalPaymentAmountYuan <= 0) {
      message.error(intl.formatMessage({ id: 'finance.supplierPayment.negativeAmountError' }));
      return;
    }
    const newPaymentList = paymentList.map(item => {
      if (totalPaymentAmountYuan == 0) {
        item.currPayAmount = undefined;
        return item;
      }
      if (totalPaymentAmountYuan >= item.remainPayableAmountYuan) {
        item.currPayAmount = item.remainPayableAmountYuan;
        totalPaymentAmountYuan = _.round(_.subtract(totalPaymentAmountYuan, item.remainPayableAmountYuan as number), 2);
        return item;
      }

      item.currPayAmount = totalPaymentAmountYuan;
      totalPaymentAmountYuan = 0;
      return item;
    })
    setPaymentList(newPaymentList);
    const currTotalPaymentAmount = _.round(_.subtract(oldTotalPaymentAmountYuan, totalPaymentAmountYuan), 2);
    setCurrTotalPaymentAmountYuan(currTotalPaymentAmount);
    handleAlertMsg(oldTotalPaymentAmountYuan, currTotalPaymentAmount);
  };

  const handleSupplierSelectChange = async (sellerId, option) => {
    setSupplierSelect(option);
    if (!sellerId) {
      return;
    }
    const data = await queryPayableList({ sellerId: sellerId, payableFlag: 1 });
    setPaymentList(data?.filter(item => item.remainPayableAmountYuan !== 0).sort((a, b) => new Date(a.billDate) - new Date(b.billDate)));
    setEditableRowKeys(data?.map(item => item.id));
    setCurrTotalPaymentAmountYuan(0);
    handleAlertMsg(totalPaymentAmountYuan, currTotalPaymentAmountYuan);
  };

  const handleAccountSelectChange = async (id, option) => {
    setAccountSelect(option);
  };

  const handleUpdate = (record: FinPayableEntity) => {
    setPaymentList((prevList) =>
      prevList.map((item) =>
        item.id === record.id ? { ...item, currPayAmount: record.currPayAmount } : item
      )
    );
  };

  useEffect(() => {
    let totalPaymentAmount: number = 0;
    paymentList?.forEach(item => {
      if (item.currPayAmount) {
        totalPaymentAmount = _.round(_.add(Number(item.currPayAmount), totalPaymentAmount), 2);
      }
    })
    setCurrTotalPaymentAmountYuan(totalPaymentAmount);
    handleAlertMsg(totalPaymentAmountYuan, totalPaymentAmount);
  }, [paymentList]);

  const orderSearch = async (orderNo) => {
    const data = await queryPayableList({ sellerId: supplierSelect.value, orderNo: orderNo, payableFlag: 1 });
    setPaymentList(data?.sort((a, b) => new Date(a.billDate) - new Date(b.billDate)));
    setEditableRowKeys(data?.map(item => item.id));
    handleAlertMsg(totalPaymentAmountYuan, currTotalPaymentAmountYuan);
  };

  const hideModal = () => {
    if (props.onCancel) {
      props.onCancel();
    }
  };

  const handleAccountSelectRequest = async (query) => {
    const data = await queryMemberAccountPage({ memberAccountName: query.keyWords, pageSize: 1000 });
    const accountOptions = data?.data?.map(({ id, memberAccountName }) => ({
      value: id,
      label: memberAccountName,
    }));

    // 如果是首次加载数据，则设置默认值
    if (!accountSelect && data?.data && data.data.length > 0) {
      form.setFieldsValue({
        paymentAccountId: data.data[0].id,
      });
      setAccountSelect(accountOptions?.[0]);
    }
    return accountOptions;
  };

  return (
    <DrawerForm
      form={form}
      title={intl.formatMessage({ id: 'finance.supplierPayment.add' })}
      width={1080}
      layout="horizontal"
      open={props.visible}
      labelCol={labelCol}
      colProps={colProps}
      drawerProps={{
        maskClosable: false,
        destroyOnClose: true,
        classNames: {
          body: 'bg-[#f2f2f2]',
        },
        onClose: props.onCancel,
      }}
      submitter={{
        render: (props, doms) => {
          return (
            <Flex gap={40} key="summary" className="flex justify-between items-center w-full">
              <Space size={40}>
                <span className="flex flex-row items-center">
                  <span className="text-[16px] font-semibold text-[#000000D9]">{intl.formatMessage({ id: 'finance.supplierPayment.paymentAmount' })}：</span>
                  <span className="text-[24px] font-medium text-[#F83431]">￥{totalPaymentAmountYuan}</span>
                </span>
                <span className="flex flex-row items-center">
                  <span className="text-[16px] font-semibold text-[#000000D9]">{intl.formatMessage({ id: 'finance.supplierPayment.currentWriteOff' })}：</span>
                  <span className="text-[24px] font-medium text-[#F83431]">￥{currTotalPaymentAmountYuan}</span>
                </span>
                {alertMsg && <Alert message={alertMsg} banner className="bg-white text-yellow-500" />}
              </Space>
              <Space>
                <Button key="rest" danger onClick={() => hideModal()}>
                  {intl.formatMessage({ id: 'common.button.cancel' })}
                </Button>
                <Button type="primary" key="launch" onClick={() => props.form?.submit?.()}>
                  {intl.formatMessage({ id: 'finance.supplierPayment.confirm' })}
                </Button>
              </Space>
            </Flex>
          );
        },
      }}
      onFinish={async (formData: any) => {
        const selectedOrderDetailList = paymentList.filter(item => item.currPayAmount).map(item => ({
          payableId: item.id,
          paymentAmountYuan: item.currPayAmount,
          ledgerType: item.ledgerType
        }));
        if (!selectedOrderDetailList || selectedOrderDetailList.length == 0) {
          message.warning(intl.formatMessage({ id: 'finance.supplierPayment.noOrderSelectedWarning' }));
          return false;
        }
        const totalPayedAmount = selectedOrderDetailList.reduce((accumulator, currentItem) => {
          return _.add(accumulator, Number(currentItem.paymentAmountYuan));
        }, 0);
        const inTotalPayedAmount = formData.totalPaymentAmountYuan as number;
        if (inTotalPayedAmount != totalPayedAmount) {
          message.error(intl.formatMessage({ id: 'finance.supplierPayment.amountMismatchError' }));
          return false;
        }

        const result = await paymentConfirmation({
          ...formData,
          memberAccountName: accountSelect?.label,
          paymentAccountName: accountSelect?.label,
          sellerName: supplierSelect?.label,
          ledgerType: inTotalPayedAmount > 0 ? 2 : 1,
          finPaymentOrderDetailCmdList: selectedOrderDetailList
        });
        if (!result) {
          return false;
        }
        return props.onOk();
      }}
    >
      <ProCard className="rounded-lg" style={{ display: 'flex', alignItems: 'flex-start' }}>
        <ProFormSelect
          width={width}
          required
          rules={[REQUIRED_RULES]}
          name="sellerId"
          label={intl.formatMessage({ id: 'finance.supplierPayment.columns.supplier' })}
          labelCol={{ span: 4 }}
          showSearch
          request={
            async () => {
              const data = await querySupplierList({});
              return data?.map(({ supplierId, supplierName }) => ({
                key: supplierId,
                value: supplierId,
                label: supplierName,
              }));
            }
          }
          onChange={handleSupplierSelectChange}
        />
        <Flex justify="space-around" align="flex-start">
          <ProFormMoney
            width={width}
            required
            name="totalPaymentAmountYuan"
            label={intl.formatMessage({ id: 'finance.supplierPayment.paymentAmount' })}
            labelCol={{ span: 5 }}
            rules={[REQUIRED_RULES]}
          />
          <Button
            type="primary"
            size="middle"
            style={{ marginLeft: 10 }}
            onClick={() =>
              autoAssignOrders()
            }
          >
            {intl.formatMessage({ id: 'finance.supplierPayment.autoAssign' })}
          </Button>
        </Flex>
        <ProFormSelect
          width={width}
          required
          name="paymentAccountId"
          label={intl.formatMessage({ id: 'finance.supplierPayment.columns.paymentAccount' })}
          labelCol={{ span: 4 }}
          showSearch
          rules={[REQUIRED_RULES]}
          onChange={handleAccountSelectChange}
          request={handleAccountSelectRequest}
        />
      </ProCard>
      <FunProTable<FinPayableEntity, any>
        scroll={{ x: 'max-content' }}
        className="mt-4"
        title={() => (
          <Flex justify="space-between" align="flex-end">
            <LeftTitle title={intl.formatMessage({ id: 'finance.supplierPayment.writeOffOrder' })} />
            <Search
              placeholder={intl.formatMessage({ id: 'finance.supplierPayment.placeholders.businessOrderNo' })}
              allowClear
              onSearch={orderSearch}
              style={{ width: 300 }}
              key={Date.now()}
            />
          </Flex>
        )}
        search={false}
        pagination={false}
        editable={{
          editableKeys
        }}
        dataSource={paymentList}
        options={false}
        columns={CreateColumns({ handleUpdate, intl })}
      />
      <ProCard style={{ display: 'flex', alignItems: 'flex-start' }}>
        <ProFormTextArea name="remark" label={intl.formatMessage({ id: 'finance.supplierPayment.columns.remark' })} width={600} labelCol={{ span: 3 }} />
      </ProCard>
    </DrawerForm>
  );
};
