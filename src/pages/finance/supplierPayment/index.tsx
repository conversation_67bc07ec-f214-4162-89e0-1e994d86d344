import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { queryPaymentPage } from '@/pages/finance/supplierPayment/services';
import type { FinPaymentEntity } from '@/pages/finance/supplierPayment/types/FinPaymentEntity';
import type { PaymentDetailModalType } from '@/pages/finance/supplierPayment/types/PaymentDetailModalType';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ProColumns } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { Space } from 'antd';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import CreateModal from './components/CreateModal';
import DetailDrawer from './components/DetailDrawer';
import { getTableColumns } from './config/TableColumns';

const FinSupplierPayment = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  // 新增
  const [createModalProps, setCreateModalProps] = useState<PaymentDetailModalType>({
    visible: false,
    serialNumber: undefined,
    readOnly: false,
    title: intl.formatMessage({ id: 'finance.supplierPayment.add' }),
  });

  // 明细
  const [detailDrawerProps, setDetailDrawerProps] = useState<PaymentDetailModalType>({
    visible: false,
  });


  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 关闭【新增】对话框
   */
  const hideCreateModal = () => {
    setCreateModalProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      serialNumber: undefined,
    }));
  };
  /**
   * 打开【明细】对话框
   */
  const openDetailDrawer = (record: FinPaymentEntity) => {
    setDetailDrawerProps((preModalProps) => ({
      ...preModalProps,
      title: intl.formatMessage({ id: 'finance.supplierPayment.detail' }),
      visible: true,
      serialNumber: record.serialNumber,
    }));
  };
  /**
   * 关闭【明细】对话框
   */
  const closeDetailDrawer = () => {
    setDetailDrawerProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      serialNumber: undefined,
    }));
  };

  /**
   * 新增
   * @param values
   */
  const handlePaymentCreate = async () => {
    try {
      setCreateModalProps((preModalProps) => ({
        ...preModalProps,
        visible: false,
      }));
      actionRef.current?.reload(true);
      return true;
    } catch (error) {
      return false;
    }
  };

  const preColumns: ProColumns<FinPaymentEntity>[] = [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'finance.supplierPayment.columns.serialNumber' }),
      dataIndex: 'serialNumber',
      width: 120,
      search: false,
      render: (text, record) => <a onClick={() => openDetailDrawer(record)}>{text}</a>,
    },
  ];

  return (
    <PageContainer>
      <FunProTable<FinPaymentEntity, any>
        requestPage={queryPaymentPage}
        options={{ setting: true, density: false, reload: false }}
        actionRef={actionRef}
        scroll={{ x: 'max-content' }}
        columns={[...preColumns, ...getTableColumns(intl)]}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              key="create"
              authority="addPayment"
              onClick={() => {
                setCreateModalProps((preModalProps) => ({
                  ...preModalProps,
                  visible: true,
                  title: intl.formatMessage({ id: 'finance.supplierPayment.add' }),
                }));
              }}
            >
              {intl.formatMessage({ id: 'finance.supplierPayment.add' })}
            </AuthButton>
          </Space>
        }
      />
      <DetailDrawer {...detailDrawerProps} onCancel={closeDetailDrawer} />
      <CreateModal {...createModalProps} onCancel={hideCreateModal} onOk={handlePaymentCreate} />
    </PageContainer>
  );
};

export default withKeepAlive(FinSupplierPayment);