import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { FinPayableEntity } from './types/FinPayableEntity';
import { FinPayableGroupEntity } from '@/pages/finance/payment/types/FinPayableGroupEntity';

/**
 * 应付分页查询
 *
 * @param params
 * @returns
 */
export const queryPayablePageGroup = async (
  params: Partial<FinPayableEntity> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<FinPayableGroupEntity>>(
    `/ipmsaccount/queryPayablePageGroup`,
    {
      data: params,
    },
  );
};

/**
 * 应付明细分页查询
 *
 * @param params
 * @returns
 */
export const queryPayableDetailPage = async (
  params: Partial<FinPayableEntity> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<FinPayableGroupEntity>>(
    `/ipmsaccount/queryPayableDetailPage`,
    {
      data: params,
    },
  );
};
