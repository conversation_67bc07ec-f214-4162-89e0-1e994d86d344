import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { queryPayablePageGroup } from '@/pages/finance/payment/services';
import { FinPayableDetailModalType } from '@/pages/finance/payment/types/FinPayableDetailModalType';
import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { exportData } from '@/utils/exportData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProCard, ProFormInstance } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Checkbox, Flex } from 'antd';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import DetailDrawer from './components/DetailDrawer';
import { getTableColumns } from './config/TableColumns';
import type { FinPayableEntity } from './types/FinPayableEntity';

const FinPayable = () => {
  const intl = useIntl();
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [totalPayableAmount, setTotalPayableAmount] = useState<string>('0');
  const [payableFlag, setPayableFlag] = useState(false);


  useActivate(() => {
    actionRef.current?.reload();

  });

  useAsyncEffect(async () => {
    actionRef.current?.reload(true);
  }, [payableFlag]);

  // 明细
  const [detailDrawerProps, setDetailDrawerProps] = useState<FinPayableDetailModalType>({
    visible: false,
  });

  /**
   * 打开【明细】对话框
   */
  const openDetailDrawer = (record: FinPayableEntity) => {
    setDetailDrawerProps((preModalProps) => ({
      ...preModalProps,
      title: intl.formatMessage({ id: 'finance.payment.detail' }),
      visible: true,
      record: record,
    }));
  };
  const operatorColumn: ProColumns<FinPayableEntity> = {
    title: intl.formatMessage({ id: 'common.column.operation' }),
    valueType: 'option',
    width: 120,
    render: (_text, record) => [
      <AuthButton
        isHref
        authority="payableDetail"
        key="crete"
        onClick={() => {
          openDetailDrawer(record);
        }}
      >
        {intl.formatMessage({ id: 'finance.payment.detailTitle' })}
      </AuthButton>,
    ],
  };
  /**
   * 关闭【明细】对话框
   */
  const closeDetailDrawer = () => {
    setDetailDrawerProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      record: undefined,
    }));
  };

  /**
   * 分页查询数据
   */
  const queryPayablePageGroupQuery = async (
    params: Partial<FinPayableEntity> & PageRequestParamsType,
  ) => {
    const queryResult = await queryPayablePageGroup({
      ...params,
      payableFlag: payableFlag ? 1 : 0,
    });
    setTotalPayableAmount(queryResult?.data?.[0].totalPayableAmountYuan || '0');
    return { ...queryResult, data: queryResult?.data?.[0].finPayableList || [] };
  };

  return (
    <PageContainer>
      <FunProTable<FinPayableEntity, any>
        scroll={{ x: 'max-content' }}
        requestPage={queryPayablePageGroupQuery}
        options={{ setting: false, density: false, reload: false }}
        formRef={formRef}
        actionRef={actionRef}
        columns={[...getTableColumns(intl), operatorColumn]}
        title={() => (
          <Flex justify="space-between" align="flex-end">
            <AuthButton
              authority="exportPayable"
              danger
              onClick={() => {
                exportData({
                  systemId: 'ETC_SAAS_SYS',
                  taskDesc: intl.formatMessage({ id: 'finance.payment.exportDescription' }),
                  moduleId: 'PAYABLE_GROUP_EXPORT',
                  params: {
                    ...formRef.current?.getFieldsValue(),
                    payableFlag: payableFlag ? 1 : 0,
                    storeIdList: formRef.current?.getFieldValue('storeIdList')
                      ? [formRef.current?.getFieldValue('storeIdList')]
                      : [],
                  },
                });
              }}
            >
              {intl.formatMessage({ id: 'common.button.export' })}
            </AuthButton>
            <Checkbox className="mb-0" onChange={(e) => setPayableFlag(e.target.checked)}>
              {intl.formatMessage({ id: 'finance.payment.onlyShowPayable' })}
            </Checkbox>
          </Flex>
        )}
      />
      <ProCard bordered>
        <Flex key="summary" justify="flex-start" align="center">
          <span className="text-[16px] font-semibold text-[#000000D9]">{intl.formatMessage({ id: 'finance.payment.totalPayableAmount' })}：</span>
          <span className="text-[24px] font-medium text-[#F83431]">￥{totalPayableAmount}</span>
        </Flex>
      </ProCard>

      <DetailDrawer {...detailDrawerProps} onCancel={closeDetailDrawer} />
    </PageContainer>
  );
};

export default withKeepAlive(FinPayable);