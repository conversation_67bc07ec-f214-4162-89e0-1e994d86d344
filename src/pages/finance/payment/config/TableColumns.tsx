import type { FinPayableEntity } from '@/pages/finance/payment/types/FinPayableEntity';
import { querySupplierList } from '@/pages/purchase/supplier/services';
import { queryStoreByAccount } from '@/pages/system/user/services';
import type { ProColumns } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';

export const getTableColumns = (intl: IntlShape): ProColumns<FinPayableEntity>[] => [
  {
    title: intl.formatMessage({ id: 'common.column.index' }),
    valueType: 'index',
    width: 40,
  },
  {
    title: intl.formatMessage({ id: 'finance.payment.columns.supplier' }),
    dataIndex: 'sellerId',
    width: 160,
    ellipsis: true,
    hideInTable: true,
    request: async () => {
      const data = await querySupplierList({});
      return data?.map(({ supplierId, supplierName }) => ({
        key: supplierId,
        value: supplierId,
        label: supplierName,
      }));
    },
  },
  {
    title: intl.formatMessage({ id: 'finance.payment.columns.supplier' }),
    dataIndex: 'sellerName',
    width: 160,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'finance.payment.columns.store' }),
    dataIndex: 'storeName',
    width: 160,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'finance.payment.columns.store' }),
    dataIndex: 'storeIdList',
    hideInTable: true,
    valueType: 'select',
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 3,
      showSearch: true,
    },
    request: async () => {
      const data = await queryStoreByAccount({});
      return data?.map(({ id, name }) => ({
        value: id,
        label: name,
      }));
    },
  },

  {
    title: intl.formatMessage({ id: 'finance.payment.columns.payableAmount' }),
    dataIndex: 'remainPayableAmountYuan',
    valueType: 'money',
    width: 100,
    search: false,
  },
];
