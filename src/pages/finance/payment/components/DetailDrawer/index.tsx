import LeftTitle from '@/components/LeftTitle';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { queryPayableDetailPage } from '@/pages/finance/payment/services';
import type { FinPayableDetailModalType } from '@/pages/finance/payment/types/FinPayableDetailModalType';
import type { FinPayableEntity } from '@/pages/finance/payment/types/FinPayableEntity';
import { queryFullById } from '@/pages/purchase/supplier/services';
import { exportData } from '@/utils/exportData';
import { DrawerForm, ProCard, ProDescriptions } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Checkbox, Flex, Space } from 'antd';
import { useRef, useState } from 'react';
import { getDetailColumns } from '../../config/DetailColumns';

export default (props: FinPayableDetailModalType) => {
  const intl = useIntl();
  const [orderAmount, setOrderAmount] = useState<string>('0');
  const [payableAmount, setPayableAmount] = useState<string>('0');
  const [payedAmount, setPayedAmount] = useState<string>('0');
  const [supplierName, setSupplierName] = useState<string>('0');
  const [payableFlag, setPayableFlag] = useState(false);

  const actionRef = useRef<ActionType>();
  useAsyncEffect(async () => {
    actionRef.current?.reload(true);
  }, [payableFlag]);

  useAsyncEffect(async () => {
    await setPayableFlag(false);
  }, [props.visible]);

  /**
   * 分页查询数据
   */
  const queryPayableDetailPageQuery = async ({ current = 1, pageSize = 10 }) => {
    if (!props.record) {
      return;
    }
    const queryResult = await queryPayableDetailPage({
      ...props.record,
      pageNo: current,
      pageSize,
      storeIdList: props.record.storeId ? [props.record.storeId] : [],
      payableFlag: payableFlag ? 1 : 0,
    });
    if (!queryResult || !queryResult.data) {
      setOrderAmount('0');
      setPayableAmount('0');
      setPayedAmount('0');
      return [];
    }
    setOrderAmount(queryResult?.data?.[0].orderAmountYuan);
    setPayableAmount(queryResult?.data?.[0].payableAmountYuan);
    setPayedAmount(queryResult?.data?.[0].paymentAmountYuan);
    return { ...queryResult, data: queryResult.data[0].detailRoList };
  };

  return (
    <DrawerForm
      title={props.title}
      width={1080}
      drawerProps={{
        classNames: {
          body: 'bg-[#f2f2f2]',
        },
        maskClosable: false,
        onClose: props.onCancel,
      }}
      open={props.visible}
      submitter={false}
    >
      <ProCard className="mb-4">
        <ProDescriptions
          key={props.record?.buyerId + props.record?.storeName}
          title={supplierName}
          column={3}
          request={async () => {
            if (!props.record?.buyerId) {
              return null;
            }
            const supplier = await queryFullById({ id: props.record?.sellerId || '' });
            setSupplierName(supplier?.supplierInfo?.supplierName || '');
            let defaultContacts = supplier?.supplierConcatList?.filter(
              (item) => item.isDefault === 1,
            )?.[0];
            if (!defaultContacts) {
              defaultContacts = supplier?.supplierConcatList?.[0];
            }
            let defaultAddress = supplier?.supplierAddressList?.filter(
              (item) => item.isDefault === 1,
            )?.[0];
            if (!defaultAddress) {
              defaultAddress = supplier?.supplierAddressList?.[0];
            }

            return Promise.resolve({
              success: true,
              data: {
                storeName: props.record?.storeName,
                supplierCode: supplier?.supplierInfo?.supplierCode,
                concatPerson: `${defaultContacts?.concatPerson ?? ''}`,
                concatPhone: `${defaultContacts?.concatPhone ?? ''}`,
                contactsAddress: `${defaultAddress?.province ?? ''}${defaultAddress?.city ?? ''}${defaultAddress?.area ?? ''
                  }${defaultAddress?.detailAddress ?? ''}`.trim(),
              },
            });
          }}
          columns={[
            {
              title: intl.formatMessage({ id: 'finance.payment.columns.store' }),
              dataIndex: 'storeName',
            },
            {
              title: intl.formatMessage({ id: 'finance.payment.columns.supplierCode' }),
              dataIndex: 'supplierCode',
            },
            {
              title: intl.formatMessage({ id: 'finance.payment.columns.contact' }),
              dataIndex: 'concatPerson',
            },
            {
              title: intl.formatMessage({ id: 'finance.payment.columns.contactPhone' }),
              dataIndex: 'concatPhone',
            },
            {
              title: intl.formatMessage({ id: 'finance.payment.columns.contactAddress' }),
              dataIndex: 'contactsAddress',
            },
          ]}
        />
      </ProCard>
      <FunProTable<FinPayableEntity, any>
        actionRef={actionRef}
        scroll={{ x: 'max-content' }}
        key={props.record?.buyerId + props.record?.storeId + props.record?.status}
        title={() => (
          <Flex justify="space-between" align="flex-end">
            <Space direction="vertical" size={16}>
              <LeftTitle title={intl.formatMessage({ id: 'finance.payment.detailTitle' })} />
              <AuthButton
                className=""
                authority="exportPayableDetail"
                danger
                onClick={() => {
                  exportData({
                    systemId: 'ETC_SAAS_SYS',
                    taskDesc: intl.formatMessage({ id: 'finance.payment.exportDetailDescription' }),
                    moduleId: 'PAYABLE_DETAIL_EXPORT',
                    params: {
                      ...props.record,
                      payableFlag: payableFlag ? 1 : 0,
                      storeIdList: props.record?.storeId ? [props.record.storeId] : [],
                    },
                  });
                  props.onCancel?.();
                }}
              >
                {intl.formatMessage({ id: 'common.button.export' })}
              </AuthButton>
            </Space>
            <Checkbox onChange={(e) => setPayableFlag(e.target.checked)}>
              {intl.formatMessage({ id: 'finance.payment.onlyShowRemaining' })}
            </Checkbox>
          </Flex>
        )}
        search={false}
        options={false}
        columns={getDetailColumns(intl)}
        request={queryPayableDetailPageQuery}
      />
      <ProCard bordered>
        <Flex gap={40} key="summary" className="flex flex-row justify-end items-center">
          <span className="text-[16px] font-semibold text-[#000000D9]">
            {intl.formatMessage({ id: 'finance.payment.summary.orderTotal' })}：￥{orderAmount}
          </span>
          <span className="text-[16px] font-semibold text-[#000000D9]">
            {intl.formatMessage({ id: 'finance.payment.summary.paidTotal' })}：￥{payedAmount}
          </span>
          <span className="flex flex-row items-center">
            <span className="text-[16px] font-semibold text-[#000000D9]">{intl.formatMessage({ id: 'finance.payment.summary.payableTotal' })}：</span>
            <span className="text-[24px] font-medium text-[#F83431]">￥{payableAmount}</span>
          </span>
        </Flex>
      </ProCard>
    </DrawerForm>
  );
};
