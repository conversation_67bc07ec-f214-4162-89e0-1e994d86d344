import FunProTable from '@/components/common/FunProTable';
import { queryCapitalFlowPage } from '@/pages/finance/flow/services';
import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { Flex, Space } from 'antd';
import { useRef, useState } from 'react';
import { getTableColumns } from './config/TableColumns';
import type { CapitalEntity } from './types/CapitalEntity.entity';

const CapitalFlow = () => {
  const intl = useIntl();
  const [pageParams, setPageParams] = useState<any>();
  const actionRef = useRef<ActionType>();
  const [totalIncomeAmountYuan, setTotalIncomeAmountYuan] = useState<string>('0');
  const [totalExpendAmountYuan, setTotalExpendAmountYuan] = useState<string>('0');

  /**
   * 分页查询数据
   */
  const queryCapitalFlowByPage = async (params: Partial<CapitalEntity> & PageRequestParamsType) => {
    const queryResult = await queryCapitalFlowPage({
      ...params,
    });
    setTotalIncomeAmountYuan(queryResult?.data?.[0].totalIncomeAmountYuan || '0');
    setTotalExpendAmountYuan(queryResult?.data?.[0].totalExpendAmountYuan || '0');
    return { ...queryResult, data: queryResult?.data?.[0].finCapitalFlowPageRoList || [] };
  };

  return (
    <PageContainer>
      <FunProTable<CapitalEntity, any>
        onReset={() => {
          setPageParams({ pageSize: 10, pageNo: 1 });
        }}
        params={pageParams}
        scroll={{ x: 'max-content' }}
        requestPage={queryCapitalFlowByPage}
        options={{ setting: true, density: false, reload: false }}
        actionRef={actionRef}
        columns={getTableColumns()}
        headerTitle={
          <Space direction="horizontal" size={40}>
            <ProCard bordered>
              <Flex className="class" align="center">
                <span className="text-[14px] text-[#00000]">{intl.formatMessage({ id: 'finance.flow.totalIncome' })}</span>
                <span className="pl-4 text-[24px] font-medium text-[#F83431]">
                  ￥{totalIncomeAmountYuan}
                </span>
              </Flex>
            </ProCard>
            <ProCard bordered>
              <Flex className="class" align="center">
                <span className="text-[14px] text-[#00000]">{intl.formatMessage({ id: 'finance.flow.totalExpend' })}</span>
                <span className="pl-4 text-[24px] font-medium text-[#33CC47FF]">
                  ￥{totalExpendAmountYuan}
                </span>
              </Flex>
            </ProCard>
          </Space>
        }
      />
      {/* <DetailDrawer {...detailDrawerProps} onCancel={closeDetailDrawer} /> */}
    </PageContainer>
  );
};

export default withKeepAlive(CapitalFlow);