import { queryStoreByAccount } from '@/pages/system/user/services';
import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { CapitalEntity } from '../types/CapitalEntity.entity';

export const getTableColumns = (): ProColumns<CapitalEntity>[] => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      fixed: 'left',
    },
    {
      title: intl.formatMessage({ id: 'finance.flow.columns.businessOrderNo' }),
      dataIndex: 'bizNo',
      width: 160,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.flow.columns.occurTime' }),
      dataIndex: 'bizTime',
      width: 140,
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value: any) => {
          return {
            startBizTime: value[0],
            endBizTime: value[1],
          };
        },
      },
    },
    {
      title: intl.formatMessage({ id: 'finance.flow.columns.occurTime' }),
      dataIndex: 'bizTime',
      width: 140,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.flow.columns.customerOrSupplier' }),
      dataIndex: 'customerOrSupplierName',
      width: 120,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.flow.columns.incomeExpendType' }),
      dataIndex: 'bizType',
      width: 120,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.flow.columns.store' }),
      dataIndex: 'storeName',
      width: 120,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'finance.flow.columns.settlementAccount' }),
      dataIndex: 'accountName',
      width: 120,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'finance.flow.columns.income' }),
      dataIndex: 'incomeAmountYuan',
      valueType: 'money',
      width: 120,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'finance.flow.columns.expend' }),
      dataIndex: 'expendAmountYuan',
      valueType: 'money',
      width: 120,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'finance.flow.columns.store' }),
      dataIndex: 'storeIdList',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
        showSearch: true,
      },
      formItemProps: {
        name: 'storeIdList',
      },
      request: async () => {
        const data = await queryStoreByAccount({});
        return data?.map(({ id, name }) => ({
          value: id,
          label: name,
        }));
      },
    },
  ];
};
