import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { type CapitalEntity } from './types/CapitalEntity.entity';

/**
 * 资金流水分页查询
 * @param params
 */
export const queryCapitalFlowPage = async (
  params: Partial<CapitalEntity> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<CapitalEntity>>(`/ipmsaccount/queryCapitalFlowPage`, {
    data: params,
  });
};
