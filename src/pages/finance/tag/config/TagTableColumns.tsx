import {
  CommonStatusValueEnum,
  FinanceLedgerTypeValueEnum,
  FinanceTagSourceValueEnum,
} from '@/types/CommonStatus';
import { REG_LENGTH_RULE, REQUIRED_RULES } from '@/utils/RuleUtils';
import { type ProColumns } from '@ant-design/pro-components';
import type { FinanceTagEntity } from '../types/FinanceTagEntity.entity';

export const getTagTableColumns = (props): ProColumns<FinanceTagEntity>[] => {
  const { intl } = props;
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      editable: false,
      fixed: 'left',
    },
    {
      title: intl.formatMessage({ id: 'finance.tag.columns.incomeExpenseType' }),
      dataIndex: 'tagName',
      search: true,
      valueType: 'text',
      ellipsis: true,
      formItemProps: {
        rules: [REQUIRED_RULES, REG_LENGTH_RULE],
      },
    },
    {
      title: intl.formatMessage({ id: 'finance.tag.columns.incomeDirection' }),
      dataIndex: 'ledgerType',
      valueType: 'select',
      search: false,
      editable: false,
      valueEnum: FinanceLedgerTypeValueEnum,
    },
    {
      title: intl.formatMessage({ id: 'finance.tag.columns.source' }),
      dataIndex: 'source',
      search: false,
      editable: false,
      valueEnum: FinanceTagSourceValueEnum,
    },
    {
      title: intl.formatMessage({ id: 'finance.tag.columns.isEnabled' }),
      dataIndex: 'tagStatus',
      search: false,
      valueEnum: CommonStatusValueEnum,
      fieldProps: {
        allowClear: false,
      },
    },
  ] as ProColumns<FinanceTagEntity>[];
};
