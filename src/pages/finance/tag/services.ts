import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { type FinanceTagEntity } from './types/FinanceTagEntity.entity';
import { PostSelect } from '@/pages/system/user/list/types/post.select';

/**
 * 财务属性分页查询
 * @param params
 */
export const queryFinanceTagPage = async (
  params: Partial<FinanceTagEntity> & PageRequestParamsType,
) => {
  const result = await request<PageResponseDataType<FinanceTagEntityEntity>>(
    `/ipmsaccount/queryFinanceTagPage`,
    {
      data: params,
    },
  );

  const newData =
    result?.data?.map((t) => ({
      ...t,
      ledgerType: `${t.ledgerType}`,
      tagStatus: `${t.tagStatus}`,
    })) ?? [];

  return { ...result, data: newData };
};

/**
 * 财务属性新增
 * @param params
 */
export const createFinTag = async (params: FinanceTagEntity) => {
  return request<boolean>(`/ipmsaccount/createFinTag`, {
    data: params,
  });
};

/**
 * 财务属性修改
 * @param params
 */
export const updateFinTag = async (params: FinanceTagEntity) => {
  return request<boolean>(`/ipmsaccount/updateFinTag`, {
    data: params,
  });
};

/**
 * 财务属性修改
 * @param params
 */
export const queryTagList = async (params?: { ledgerType?: 1 | 2 }) => {
  return request<boolean>(`/ipmsaccount/queryTagList`, {
    data: params,
  });
};
