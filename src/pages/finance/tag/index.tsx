import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { type PropertyModalFromType } from '@/types/PropertyModalFromType';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer, ProColumns } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { Space } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import FinancePropertyFormModal from './components/FinancePropertyFormModal';
import { getTagTableColumns } from './config/TagTableColumns';
import { createFinTag, queryFinanceTagPage, updateFinTag } from './services';
import type { FinanceTagEntity } from './types/FinanceTagEntity.entity';

const FinanceTag = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();

  // 设置新增按钮label
  const [createBtnText, setCreateBtnText] = useState<string>(intl.formatMessage({ id: 'finance.tag.incomeExpenseType' }));

  const [columns, setColumns] = useState<ProColumns<FinanceTagEntity>[]>();

  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 初始化操作
   */
  useEffect(() => {
    /**
     * 设置操作列
     */
    const operatorColumn: {
      valueType: string;
      width: number;
      title: string;
      render: (_text, record, _, action) => null | JSX.Element;
    } = {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      valueType: 'option',
      width: 150,
      render: (_text, record, _, action) => {
        const { source } = record;
        if (source == 0) {
          return null;
        }
        return (
          <AuthButton
            isHref
            authority="editFinTag"
            key="edit"
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          >
            {intl.formatMessage({ id: 'common.button.edit' })}
          </AuthButton>
        );
      },
    };
    let newColumns: ProColumns<FinanceTagEntity>[] = [];
    newColumns = getTagTableColumns({ intl });
    setColumns(() => [...newColumns, operatorColumn]);
  }, []);

  const [createModalProps, setCreateModalProps] = useState<PropertyModalFromType<number>>({
    inputFieldLabel: '',
    inputFieldName: '',
    onCancel(): void { },
    onOk(value: any): Promise<boolean | void> {
      return Promise.resolve(undefined);
    },
    visible: false,
    recordId: 0,
    readOnly: false,
    title: intl.formatMessage({ id: 'finance.tag.addIncomeExpenseType' }),
  });

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setCreateModalProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      recordId: 0,
      readOnly: false,
    }));
  };

  /**
   * 新增
   * @param values
   */
  const handleSaveOrUpdate = async (values: any) => {
    try {
      const result = await createFinTag({ ...values });
      if (result) {
        hideModal();
        actionRef.current?.reset?.();
        actionRef.current?.reload(true);
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  /**
   * 编辑-保存
   * @param _key
   * @param record
   */
  const onEditSave = async (_key: React.Key | React.Key[], record: FinanceTagEntity) => {
    const result = await updateFinTag(record);
    if (!result) {
      return Promise.reject();
    }
    return true;
  };

  return (
    <PageContainer>
      <FunProTable<FinanceTagEntity, any>
        editable={{
          // 单行编辑
          type: 'single',
          onSave: onEditSave,
          actionRender: (_row, _config, defaultDom) => {
            return [defaultDom.save, defaultDom.cancel];
          },
        }}
        rowKey="id"
        scroll={{ x: 'max-content' }}
        requestPage={queryFinanceTagPage}
        actionRef={actionRef}
        columns={columns}
        headerTitle={
          <Space>
            <AuthButton
              authority="addFinTag"
              type="primary"
              key="create"
              onClick={() => {
                setCreateModalProps((preModalProps) => ({
                  ...preModalProps,
                  // inputFieldName: inputFieldNameMap[tabActiveKey],
                  // inputFieldLabel: inputFieldLabelMap[tabActiveKey],
                  visible: true,
                  readOnly: false,
                  title: intl.formatMessage({ id: 'finance.tag.addIncomeExpenseType' }),
                }));
              }}
            >
              {intl.formatMessage({ id: 'finance.tag.addIncomeExpenseType' })}
            </AuthButton>
          </Space>
        }
      />
      <FinancePropertyFormModal
        {...createModalProps}
        onCancel={hideModal}
        onOk={handleSaveOrUpdate}
      />
    </PageContainer>
  );
};

export default withKeepAlive(FinanceTag);