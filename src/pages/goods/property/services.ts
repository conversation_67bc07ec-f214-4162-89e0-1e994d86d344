import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { type GoodsBrandEntity } from './types/GoodsBrand.entity';
import { type GoodsCategoryEntity } from './types/GoodsCategory.entity';
import { type GoodsOriginRegionEntity } from './types/GoodsOriginRegion.entity';
import { type GoodsUnitEntity } from './types/GoodsUnit.entity';
import { type GoodsPriceLevelEntity } from './types/GoodsPriceLevel.entity';

export type GoodsPropertyTableType = GoodsCategoryEntity &
  GoodsBrandEntity &
  GoodsUnitEntity &
  GoodsOriginRegionEntity &
  GoodsPriceLevelEntity;

const CONVERT_MAP: Record<
  string,
  'brandStatus' | 'unitStatus' | 'categoryStatus' | 'relationStatus' | 'levelStatus'
> = {
  brand: 'brandStatus',
  unit: 'unitStatus',
  category: 'categoryStatus',
  originRegionRelation: 'relationStatus',
  priceLevel: 'levelStatus',
};
/**
 * 商品属性-分页查询
 *
 * @param params
 * @returns
 */
export const queryGoodsPropertyPage = async (
  params: Partial<GoodsPropertyTableType> & PageRequestParamsType,
  requestType: string,
) => {
  const result = await request<PageResponseDataType<GoodsPropertyTableType>>(
    `/ipmsgoods/${requestType}/pageQuery`,
    { data: params },
  );
  if (result) {
    const status = CONVERT_MAP[requestType];
    return {
      ...result,
      data: result.data.map((t) => ({ ...t, [status]: `${t[status]}` })),
    };
  }
  return { data: [] };
};
export const addGoodsProperty = async (
  params: Partial<GoodsPropertyTableType>,
  requestType: string,
) => {
  return request<boolean>(`/ipmsgoods/${requestType}/insert`, {
    data: params,
  });
};
// 新增品牌
export const addBrandProperty = async (params: { brandName: string }) => {
  return request<string[]>(`/ipmsgoods/brand/insert`, {
    data: params,
  });
};
export const editGoodsCategory = async (
  params: Partial<GoodsPropertyTableType>,
  requestType: string,
) => {
  return request<boolean>(`/ipmsgoods/${requestType}/update`, {
    data: params,
  });
};

export const pageQueryPriceLevel = async (
  params: Partial<GoodsPropertyTableType> & PageRequestParamsType,
  requestType: string,
) => {
  const result = await request<PageResponseDataType<GoodsPropertyTableType>>(
    `/ipmsgoods/priceLevel/queryPage`,
    { data: params },
  );
  if (result) {
    const status = CONVERT_MAP[requestType];
    return {
      ...result,
      data: result.data.map((t) => ({ ...t, [status]: `${t[status]}` })),
    };
  }
  return { data: [] };
};
