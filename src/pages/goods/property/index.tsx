import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import GoodsPriceLevelColumns from "@/pages/goods/property/config/GoodsPriceLevelColumns";
import { type PropertyModalFromType } from '@/types/PropertyModalFromType';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer, ProCard, type ProColumns } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import type { TabsProps } from 'antd';
import { Space, Tabs } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import { useIntl } from 'umi';
import GoodsPropertyFormModal from './components/GoodsPropertyFormModal';
import GoodsBrandColumns from './config/GoodsBrandColumns';
import GoodsCategoryColumns from './config/GoodsCategoryColumns';
import GoodsRegionColumns from './config/GoodsRegionColumns';
import GoodsUnitColumns from './config/GoodsUnitColumns';
import {
  addGoodsProperty,
  editGoodsCategory,
  queryGoodsPropertyPage,
  type GoodsPropertyTableType,
} from './services';

type RowKeyType = 'id' | 'brandId' | 'unitId' | 'categoryId' | 'levelId';

const inputFieldNameMap: Record<string, string> = {
  brand: 'brandName',
  unit: 'unitName',
  category: 'categoryName',
  originRegionRelation: 'originRegionName',
  priceLevel: 'levelName',
};
const GoodsProperty = () => {
  const intl = useIntl();
  const [createBtnText, setCreateBtnText] = useState<string>(intl.formatMessage({ id: 'goods.property.button.brand' }));
  const [rowKey, setRowKey] = useState<RowKeyType>('id');
  const actionRef = useRef<ActionType>();
  const [columns, setColumns] = useState<ProColumns<GoodsPropertyTableType>[]>(GoodsBrandColumns(intl));
  const [tabActiveKey, setTabActiveKey] = useState<string>('brand');

  const inputFieldLabelMap: Record<string, string> = {
    brand: intl.formatMessage({ id: 'goods.property.inputFieldLabel.brandName' }),
    unit: intl.formatMessage({ id: 'goods.property.inputFieldLabel.unitName' }),
    category: intl.formatMessage({ id: 'goods.property.inputFieldLabel.categoryName' }),
    originRegionRelation: intl.formatMessage({ id: 'goods.property.inputFieldLabel.originRegionName' }),
    priceLevel: intl.formatMessage({ id: 'goods.property.inputFieldLabel.priceLevelName' }),
  };

  useActivate(() => {
    actionRef.current?.reload();
  });

  useEffect(() => {
    const operatorColumn: ProColumns<GoodsPropertyTableType> = {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      valueType: 'option',
      width: 80,
      fixed: 'right',
      align: "center",
      render: (_text, record, _, action) => {
        const { dataType } = record;
        if (dataType == '0') {
          return null;
        }
        return (
          <AuthButton
            isHref
            authority={`edit${tabActiveKey}`}
            key="create"
            onClick={() => {
              action?.startEditable?.(record[rowKey]);
            }}
          >
            {intl.formatMessage({ id: 'common.button.edit' })}
          </AuthButton>
        );
      },
    };
    let newColumns: ProColumns<GoodsPropertyTableType>[] = [];
    let btnText = '';
    let tableRowKey: RowKeyType = 'id';
    if (tabActiveKey == 'brand') {
      btnText = intl.formatMessage({ id: 'goods.property.button.brand' });
      tableRowKey = 'brandId';
      newColumns = GoodsBrandColumns(intl);
    } else if (tabActiveKey == 'category') {
      newColumns = GoodsCategoryColumns(intl);
      tableRowKey = 'categoryId';
      btnText = intl.formatMessage({ id: 'goods.property.button.category' });
    } else if (tabActiveKey == 'unit') {
      newColumns = GoodsUnitColumns(intl);
      tableRowKey = 'unitId';
      btnText = intl.formatMessage({ id: 'goods.property.button.unit' });
    } else if (tabActiveKey == 'originRegionRelation') {
      newColumns = GoodsRegionColumns(intl);
      tableRowKey = 'id';
      btnText = intl.formatMessage({ id: 'goods.property.button.originRegionRelation' });
    } else if (tabActiveKey == 'priceLevel') {
      newColumns = GoodsPriceLevelColumns(intl);
      tableRowKey = 'levelId';
      btnText = intl.formatMessage({ id: 'goods.property.button.priceLevel' });
    }
    setRowKey(tableRowKey);
    setCreateBtnText(btnText);
    setColumns(() => [...newColumns, operatorColumn]);
  }, [rowKey, tabActiveKey]);

  const [createModalProps, setCreateModalProps] = useState<PropertyModalFromType<number>>({
    inputFieldName: inputFieldNameMap[tabActiveKey],
    inputFieldLabel: inputFieldLabelMap[tabActiveKey],
    visible: false,
    recordId: 0,
    readOnly: false,
    title: intl.formatMessage({ id: 'goods.property.modal.title.add' }, { name: createBtnText }),
  });
  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setCreateModalProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      recordId: 0,
      readOnly: false,
    }));
  };

  /**
   * 新增保存
   * @param values
   */
  const handleSave = async (values: any) => {
    const result = await addGoodsProperty(values, tabActiveKey);
    if (result) {
      hideModal();
      actionRef.current?.reset?.();
      actionRef.current?.reload(true);
    }
  };

  /**
   * 头部tabs
   */
  const items: TabsProps['items'] = [
    {
      key: 'brand',
      label: intl.formatMessage({ id: 'goods.property.tab.brandManagement' }),
    },
    {
      label: intl.formatMessage({ id: 'goods.property.tab.categoryManagement' }),
      key: 'category',
    },
    {
      label: intl.formatMessage({ id: 'goods.property.tab.unitManagement' }),
      key: 'unit',
    },
    {
      label: intl.formatMessage({ id: 'goods.property.tab.originRegionRelationManagement' }),
      key: 'originRegionRelation',
    },
    {
      label: intl.formatMessage({ id: 'goods.property.tab.priceLevelManagement' }),
      key: 'priceLevel',
    },
  ];
  // 切换tabs
  const onChange = (key: string) => {
    actionRef.current?.reload(true);
    setTabActiveKey(key);
  };
  // 编辑-保存
  const onEditSave = async (_key: React.Key | React.Key[], record: GoodsPropertyTableType) => {
    const result = await editGoodsCategory(record, tabActiveKey);
    if (!result) {
      return Promise.reject();
    }
    return true;
  };
  return (
    <PageContainer>
      <ProCard bodyStyle={{ paddingLeft: 24, paddingTop: 0, paddingBottom: 0 }}>
        <Tabs
          defaultActiveKey="brand"
          items={items}
          onChange={onChange}
          tabBarStyle={{ marginBottom: 0 }}
        />
      </ProCard>
      <FunProTable<GoodsPropertyTableType, any>
        scroll={{ x: 800 }}
        editable={{
          type: 'single',
          onSave: onEditSave,
          actionRender: (_row, _config, defaultDom) => {
            return [defaultDom.save, defaultDom.cancel];
          },
        }}
        rowKey={rowKey}
        requestPage={(params) => queryGoodsPropertyPage(params, tabActiveKey)}
        actionRef={actionRef}
        columns={columns}
        headerTitle={
          <Space>
            <AuthButton
              authority={`add${tabActiveKey}`}
              type="primary"
              key="crete"
              onClick={() => {
                setCreateModalProps((preModalProps) => ({
                  ...preModalProps,
                  inputFieldName: inputFieldNameMap[tabActiveKey],
                  inputFieldLabel: inputFieldLabelMap[tabActiveKey],
                  visible: true,
                  readOnly: false,
                  title: intl.formatMessage({ id: 'goods.property.modal.title.addSuffix' }, { name: createBtnText }),
                }));
              }}
            >
              {intl.formatMessage({ id: 'goods.property.modal.title.add' }, { name: createBtnText })}
            </AuthButton>
          </Space>
        }
      />
      <GoodsPropertyFormModal {...createModalProps} onCancel={hideModal} onOk={handleSave} />
    </PageContainer>
  );
};

export default withKeepAlive(GoodsProperty);
