import { type PropertyModalFromType } from '@/types/PropertyModalFromType';
import { ModalForm, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { useAsyncEffect } from 'ahooks';
import { useForm } from 'antd/lib/form/Form';
import { useIntl } from 'umi';
export default ({
  title,
  visible,
  onCancel,
  onOk,
  readOnly,
  inputFieldName,
  inputFieldLabel,
}: PropertyModalFromType<number>) => {
  const intl = useIntl();
  const [form] = useForm();
  useAsyncEffect(async () => {
    if (visible) {
      form.resetFields();
    }
  }, [visible]);

  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };
  const rules = [{ required: !readOnly, max: 1000 }];
  return (
    <ModalForm
      {...layout}
      form={form}
      layout="horizontal"
      title={title}
      open={visible}
      width={480}
      modalProps={{
        centered: true,
        onCancel: onCancel,
      }}
      submitTimeout={2000}
      onFinish={onOk}
    >
      {inputFieldName == 'categoryName' && <ProFormText hidden name="grade" initialValue={3} />}
      <ProFormTextArea
        rules={rules}
        name={inputFieldName}
        label={inputFieldLabel}
        placeholder={intl.formatMessage({ id: 'goods.property.formModal.placeholder' }, { label: inputFieldLabel })}
      />
    </ModalForm>
  );
};
