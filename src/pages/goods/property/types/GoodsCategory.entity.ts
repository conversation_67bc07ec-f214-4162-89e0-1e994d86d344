/**
 * 商品属性-分类
 */
export interface GoodsCategoryEntity {
  /**父类ID*/
  parentId: number;
  /**是否返回树结构*/
  isReturnTree: boolean;
  /**三方数据源编码*/
  sourceCode: string;
  /**状态0禁用1启用*/
  categoryStatus: number;
  /**类目类型：0-商品类1-服务类2-虚拟类*/
  categoryType: number;
  /**类目ID*/
  categoryId: number;
  /**类目名称*/
  categoryName: string;

  /**数据类型，0：标准数据，1：自建数据，为空则查询所有 */
  dataType: string;
  /**门店零售商id*/
  memberId: string;
  /**备注*/
  remark: string;
  /**类目层级（1-一级类目，2-二级类目，3-三级类目）*/
  grade: number;
}
