import { CommonStatusValueEnum } from '@/types/CommonStatus';
import { REG_LENGTH_RULE, REQUIRED_RULES } from '@/utils/RuleUtils';
import { type ProColumns } from '@ant-design/pro-components';
import { type GoodsPropertyTableType } from '../services';
import { GoodsDataTypeEnum } from '../types/GoodsPropertyValueEnum';

export default (intl) => [
  {
    title: intl.formatMessage({ id: 'common.column.index' }),
    valueType: 'index',
    width: 40,
    editable: false,
  },
  {
    title: intl.formatMessage({ id: 'goods.property.inputFieldLabel.categoryName' }),
    dataIndex: 'categoryName',
    search: true,
    formItemProps: {
      rules: [REQUIRED_RULES, REG_LENGTH_RULE],
    },
  },
  {
    title: intl.formatMessage({ id: 'goods.property.common.source' }),
    dataIndex: 'dataType',
    editable: false,
    search: false,
    width: 150,
    valueEnum: GoodsDataTypeEnum,
  },
  {
    title: intl.formatMessage({ id: 'goods.property.common.status' }),
    dataIndex: 'categoryStatus',
    search: false,
    valueType: 'select',
    valueEnum: CommonStatusValueEnum,
    width: 150,
    fixed: "right",
    align: 'center',
    fieldProps: {
      allowClear: false,
    },
  },
] as ProColumns<GoodsPropertyTableType>[];
