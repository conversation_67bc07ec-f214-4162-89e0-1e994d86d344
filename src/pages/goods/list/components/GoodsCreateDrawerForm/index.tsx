import LeftTitle from '@/components/LeftTitle';
import { type GoodsCreateDrawerFormType } from '@/pages/goods/list/types/GoodsCreateDrawerFormType';
import { addBrandProperty, queryGoodsPropertyPage } from '@/pages/goods/property/services';
import {
  REG_LENGTH_REMARK_RULE,
  REG_LENGTH_RULE,
  REG_ONLY_ALPHA_AND_DIGIT_RULE,
  REQUIRED_RULES,
} from '@/utils/RuleUtils';

import FunProFormUploadButtonDragable from '@/components/FunProFormUploadButtonDragable';
import type { GoodsPriceLevelEntity } from '@/pages/goods/property/types/GoodsPriceLevel.entity';
import { queryPostList } from '@/pages/purchase/supplier/services';
import { MAX_AMOUNT } from '@/utils/Constants';
import { transformCategoryTree } from '@/utils/transformCategoryTree';
import { ProFormTreeSelect } from '@ant-design/pro-components';
import {
  DrawerForm,
  ProFormField,
  ProFormGroup,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-form';
import { Access, useAccess, useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import {
  Button,
  Flex,
  Form,
  message,
} from 'antd';
import type { UploadFile } from 'antd/es/upload';
import { isEmpty, uniqueId } from 'lodash';
import { useEffect, useState } from 'react';
import {
  addGoods,
  goodsDetail,
  updateGoods,
} from '../../services';
import type { GoodsAddEntity } from '../../types/GoodsEntity.entity';

const labelCol = { span: 6 };
const colProps = { span: 12 };

/**
 * 创建商品
 */
export default (props: GoodsCreateDrawerFormType) => {
  const [form] = Form.useForm();
  const access = useAccess();
  const intl = useIntl();

  const [priceDate, setPriceDate] = useState<GoodsPriceLevelEntity[]>();

  const [goodsBrandName, setGoodsBrandName] = useState<string>();
  const [defaultFileList, setDefaultFileList] = useState<UploadFile[]>([]);
  const [submitImages, setSubmitImages] = useState<UploadFile[]>([]);

  /**
   * 新增商品
   * @param values
   */
  const handleGoodsCreate = async (values: any) => {
    try {
      if (submitImages) {
        values.images = submitImages.map((t, sort) => ({ imageUrl: t.url, sort }));
      }
      let result = false;
      if (values?.itemId) {
        result = await updateGoods(values);
      } else {
        result = await addGoods(values);
      }
      if (result) {
        props.onCancel(isEmpty(values?.itemId));
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  useAsyncEffect(async () => {
    setDefaultFileList([]);
    if (props.recordId == 0) {
      form.resetFields();
    } else if (props?.recordId) {
      const result = await goodsDetail({ itemId: props.recordId });
      const { images, oeNos, brandPartNos, priceDetails, ...restValues } = result;
      form.setFieldsValue(restValues);
      if (oeNos) {
        form.setFieldValue('oeNos', oeNos.join(','));
      }
      if (brandPartNos) {
        form.setFieldValue('brandPartNos', brandPartNos.join(','));
      }
      if (images) {
        const files: UploadFile[] = images.map((t: string) => ({
          uid: uniqueId('fileuid_'),
          name: uniqueId('filename_'),
          url: t,
          status: 'done',
        }));
        setDefaultFileList(files);
      }
      if (priceDetails) {
        //定义一个数组
        const priceDetailsArr: Record<number, any> = {};
        priceDetails.forEach((item) => {
          priceDetailsArr[item.levelId! as unknown as number] = item.levelPrice;
        });
        form.setFieldsValue({ priceDetails: priceDetailsArr });
      }
    }
  }, [props.visible, props?.recordId]);

  //品牌输入值
  const [searchBrandName, setSearchBrandName] = useState<string>();
  const [brandOptions, setBrandOptions] = useState<{ label: any; value: number }[]>([]);
  useAsyncEffect(async () => {
    if (!props.visible) {
      return;
    }
    const { data: brandData } = await queryGoodsPropertyPage(
      { brandName: searchBrandName, pageNo: 1, pageSize: 1000, brandStatus: '1' },
      'brand',
    );
    setBrandOptions(
      brandData.map((t) => ({
        label: t.brandName,
        value: t.brandId,
      })),
    );
  }, [searchBrandName, props.visible]);

  const addGoodsBrand = async () => {
    if (searchBrandName) {
      const result = await addBrandProperty({ brandName: searchBrandName });
      if (result) {
        const brandId = parseInt(result[0]);
        setBrandOptions((pre) => [{ value: brandId, label: searchBrandName }, ...pre]);
        form.setFieldValue('brandId', brandId);
        message.success(intl.formatMessage({ id: 'goods.createForm.addBrandSuccess' }, { brandName: searchBrandName }));
      }
    } else {
      message.error(intl.formatMessage({ id: 'goods.createForm.inputBrandNameRequired' }));
    }
  };

  // 外部oe信息创建商品
  useEffect(() => {
    if (props.visible && props?.oEOuterParams) {
      form.setFieldsValue(props?.oEOuterParams);
    }
  }, [props.visible, props.oEOuterParams, form]);
  // 外部sku信息创建商品
  useEffect(() => {
    if (props.visible && props?.cloudOuterParams) {
      form.setFieldsValue(props.cloudOuterParams);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.visible, props.cloudOuterParams]);

  const queryLevelPrice = () => {
    queryGoodsPropertyPage(
      { levelName: searchBrandName, pageNo: 1, pageSize: 1000, levelStatus: 1 },
      'priceLevel',
    ).then((result) => {
      if (result) {
        setPriceDate(result.data as GoodsPriceLevelEntity[]);
      }
    });
  };

  useEffect(() => {
    if (!props.visible) {
      return;
    }
    queryLevelPrice();
  }, [searchBrandName, props.visible]);

  return (
    <DrawerForm<GoodsAddEntity>
      form={form}
      grid
      layout="horizontal"
      labelWrap={true}
      title={props.title}
      open={props.visible}
      width={1080}
      drawerProps={{
        destroyOnClose: true,
        maskClosable: false,
        styles: { body: { backgroundColor: '#F2F2F2' } },
        onClose: () => {
          props.onCancel(false);
        },
      }}
      onFinish={async (formData: any) => {
        const priceDetails = [];
        for (const key in formData.priceDetails) {
          priceDetails.push({ levelId: key, levelPrice: formData.priceDetails[key] });
        }
        formData.priceDetails = priceDetails;
        return handleGoodsCreate(formData).then(() => {
          props.onRefresh?.();
        });
      }}
    >
      <ProFormText name="itemId" hidden />
      <ProFormText name="skuId" hidden />
      <ProFormGroup
        style={{
          backgroundColor: 'white',
          padding: 24,
          borderRadius: 8,
        }}
      >
        <ProFormGroup title={<LeftTitle title={intl.formatMessage({ id: 'goods.createForm.baseInfoTitle' })} />}>
          <ProFormField
            label={intl.formatMessage({ id: 'goods.createForm.oeLabel' })}
            name="oeNos"
            labelCol={labelCol}
            colProps={colProps}
            transform={(value: string | string[]) => {
              if (typeof value === 'string') {
                return { oeNos: !isEmpty(value) ? value.split(',') : value };
              } else {
                return { oeNos: value };
              }
            }}
          />
          <ProFormField
            label={intl.formatMessage({ id: 'goods.createForm.brandPartNoLabel' })}
            name="brandPartNos"
            labelCol={labelCol}
            colProps={colProps}
            transform={(value: string) => {
              return { brandPartNos: !isEmpty(value) ? value.split(',') : value };
            }}
          />
          <ProFormText
            rules={[REQUIRED_RULES]}
            name="itemName"
            required
            disabled={props.readOnly}
            label={intl.formatMessage({ id: 'goods.createForm.itemNameLabel' })}
            labelCol={labelCol}
            colProps={colProps}
          />
          <ProFormText
            name="itemSn"
            disabled={props.readOnly}
            label={intl.formatMessage({ id: 'goods.createForm.itemSnLabel' })}
            rules={[
              REG_ONLY_ALPHA_AND_DIGIT_RULE,
              REG_LENGTH_RULE,
              { required: !isEmpty(props?.recordId) },
            ]}
            labelCol={labelCol}
            colProps={colProps}
          />
          <ProFormText
            labelCol={labelCol}
            colProps={colProps}
            name="memCode"
            disabled={props.readOnly}
            label={intl.formatMessage({ id: 'goods.createForm.memCodeLabel' })}
          />
          <ProFormSelect
            labelCol={labelCol}
            colProps={colProps}
            name="brandId"
            showSearch
            label={intl.formatMessage({ id: 'goods.createForm.brandLabel' })}
            options={brandOptions}
            fieldProps={{
              filterOption: false,
              onSearch: (value) => setSearchBrandName(value),
              onSelect: (_, option) => {
                setGoodsBrandName(option.label as string);
              },
              notFoundContent: (
                <Flex vertical align="center" justify="center">
                  <span className="mb-4">{intl.formatMessage({ id: 'goods.createForm.searchEmpty' })}</span>
                  <Button type="link" onClick={addGoodsBrand}>
                    {intl.formatMessage({ id: 'goods.createForm.addBrandAndSelectButton' })}
                  </Button>
                </Flex>
              ),
            }}
          />

          <ProFormTreeSelect
            name="categoryId"
            labelCol={labelCol}
            colProps={colProps}
            fieldProps={{
              filterTreeNode: (text: string, treeNode: any) => treeNode.text?.includes(text),
              showSearch: true,
            }}
            label={intl.formatMessage({ id: 'goods.createForm.categoryLabel' })}
            // placeholder={intl.formatMessage({ id: 'goods.createForm.pleaseSelectPlaceholder' })}
            request={async () => {
              const { data: categoryData } = await queryGoodsPropertyPage(
                { pageNo: 1, pageSize: 999, categoryStatus: 1, isReturnTree: true },
                'category',
              );
              return transformCategoryTree(categoryData);
            }}
          />
          <ProFormSelect
            showSearch
            name="unitId"
            labelCol={labelCol}
            colProps={colProps}
            label={intl.formatMessage({ id: 'goods.createForm.unitLabel' })}
            fieldProps={{ filterOption: false }}
            // placeholder={intl.formatMessage({ id: 'goods.createForm.pleaseSelectPlaceholder' })}
            request={async ({ keyWords: unitName }) => {
              const { data: unitData } = await queryGoodsPropertyPage(
                { unitName, pageNo: 1, pageSize: 1000, unitStatus: 1 },
                'unit',
              );
              return unitData.map((t) => ({
                label: t.unitName,
                value: t.unitId,
              }));
            }}
          />
          <ProFormText
            name="spec"
            disabled={props.readOnly}
            label={intl.formatMessage({ id: 'goods.createForm.specLabel' })}
            labelCol={labelCol}
            colProps={colProps}
          />
          <ProFormSelect
            showSearch
            name="originRegionId"
            labelCol={labelCol}
            colProps={colProps}
            disabled={props.readOnly}
            fieldProps={{ fieldNames: { label: 'originRegionName', value: 'id' } }}
            label={intl.formatMessage({ id: 'goods.createForm.originRegionLabel' })}
            // placeholder={intl.formatMessage({ id: 'goods.createForm.pleaseSelectPlaceholder' })}
            request={async ({ keyWords: originRegionName }) => {
              const { data: originRegionData } = await queryGoodsPropertyPage(
                { originRegionName, pageNo: 1, pageSize: 1000, relationStatus: 1 },
                'originRegionRelation',
              );
              return originRegionData;
            }}
          />

          <ProFormSelect
            name="supplierIdList"
            labelCol={{ span: 3 }}
            colProps={{ span: 24 }}
            disabled={props.readOnly}
            label={intl.formatMessage({ id: 'goods.createForm.supplierLabel' })}
            // placeholder={intl.formatMessage({ id: 'goods.createForm.pleaseSelectPlaceholder' })}
            mode="multiple"
            request={async ({ keyWords }) => {
              const { data } = await queryPostList({ supplierName: keyWords, pageNo: 1, pageSize: 1000 });
              return data?.map((t) => ({
                label: t.supplierInfo?.supplierName,
                value: t.supplierInfo?.id,
              }));
            }}
          />

          <ProFormText
            name="adaptModel"
            disabled={props.readOnly}
            label={intl.formatMessage({ id: 'goods.createForm.adaptModelLabel' })}
            rules={[REG_LENGTH_REMARK_RULE]}
            labelCol={{ span: 3 }}
            colProps={{ span: 24 }}
          />

          <ProFormText
            name="remark"
            labelCol={{ span: 3 }}
            colProps={{ span: 24 }}
            disabled={props.readOnly}
            rules={[REG_LENGTH_REMARK_RULE]}
            label={intl.formatMessage({ id: 'goods.createForm.remarkLabel' })}
          />
        </ProFormGroup>
        <FunProFormUploadButtonDragable
          name="images"
          dragEnable
          defaultFiles={defaultFileList}
          labelCol={{ span: 3 }}
          onFileListChange={(fileList) => setSubmitImages(fileList)}
          max={5}
          label={intl.formatMessage({ id: 'goods.createForm.imagesLabel' })}
        />
      </ProFormGroup>
      <ProFormGroup
        title={<LeftTitle title={intl.formatMessage({ id: 'goods.createForm.priceInfoTitle' })} />}
        style={{
          backgroundColor: 'white',
          padding: 24,
          marginTop: 16,
          borderRadius: 8,
        }}
      >
        <ProFormMoney
          tooltip={intl.formatMessage({ id: 'goods.createForm.suggestPriceTooltip' })}
          name="suggestPrice"
          labelCol={labelCol}
          colProps={colProps}
          disabled={props.readOnly}
          label={intl.formatMessage({ id: 'goods.createForm.suggestPriceLabel' })}
          fieldProps={{
            precision: 2,
            max: MAX_AMOUNT,
          }}
        />

        <ProFormMoney
          tooltip={intl.formatMessage({ id: 'goods.createForm.lowPriceTooltip' })}
          name="lowPrice"
          disabled={props.readOnly}
          label={intl.formatMessage({ id: 'goods.createForm.lowPriceLabel' })}
          labelCol={labelCol}
          colProps={colProps}
          fieldProps={{
            precision: 2,
            max: MAX_AMOUNT,
            min: 0,
          }}
        />
        <Access accessible={access.hasButtonPerms('setPurchasePrice')}>
          <ProFormMoney
            name="purchasePrice"
            tooltip={intl.formatMessage({ id: 'goods.createForm.purchasePriceTooltip' })}
            labelCol={labelCol}
            colProps={colProps}
            label={intl.formatMessage({ id: 'goods.createForm.purchasePriceLabel' })}
            fieldProps={{
              precision: 2,
              max: MAX_AMOUNT,
              min: 0,
            }}
          />
          {priceDate?.map((item, index) => {
            return (
              <ProFormMoney
                key={item.levelId}
                name={['priceDetails', item.levelId]}
                labelCol={labelCol}
                colProps={colProps}
                disabled={props.readOnly}
                label={item.levelName}
                fieldProps={{
                  precision: 2,
                  max: MAX_AMOUNT,
                  min: 0,
                }}
              />
            );
          })}
        </Access>
      </ProFormGroup>
    </DrawerForm>
  );
};
