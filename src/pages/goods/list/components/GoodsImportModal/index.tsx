import downloadIcon from '@/assets/icons/icon_download.png';
import importIcon from '@/assets/icons/icon_import.png';
import { createImportTask, downloadFile, uploadFile } from '@/services/systerm';
import { history } from '@@/core/history';
import { useIntl } from '@umijs/max';
import { Image, Modal, Space, Upload } from 'antd';
import { type RcFile } from 'antd/es/upload';
import { isEmpty } from 'lodash';
import { useState } from 'react';
import { type GoodsImportModalType } from '../../types/GoodsImportModalType';
const ACCEPT_FILE_EXT = '.xls,.xlsx,.csv';
type FileImportModuleIdType =
  | 'GOODS_MEMBER_CLEAN_IMPORT'
  | 'GOODS_STANDARD_MEMBER_BATCH_IMPORT'
  | 'GOODS_ITEM_PRICE_IMPORT'
  | 'GOODS_CUSTOM_MEMBER_BATCH_IMPORT';
type FileImportDescType = '零售商商品清洗' | '零售商批量创建标准商品' | '零售商批量创建自建商品' | '零售商批量创建商品价格';
export default (props: GoodsImportModalType) => {
  const intl = useIntl();
  const [fileImportParams, setFileImportParams] = useState<{
    systemId: string;
    taskDesc?: FileImportDescType;
    moduleId?: FileImportModuleIdType;
  }>({
    systemId: 'ETC_SAAS_SYS',
  });
  const beforeUpload = async (file: RcFile) => {
    const formData = new FormData();
    formData.append('file', file);
    const result = await uploadFile(formData);
    if (!isEmpty(result)) {
      const taskResult = await createImportTask({
        ...fileImportParams,
        params: [{ key: 'url', value: result[0] }],
      });
      if (taskResult) {
        Modal.success({
          title: intl.formatMessage({ id: 'goods.importModal.taskCreatedTitle' }),
          centered: true,
          content: intl.formatMessage({ id: 'goods.importModal.taskCreatedContent' }),
          okText: intl.formatMessage({ id: 'goods.importModal.viewResultButton' }),
          onOk: () => {
            history.push('/system/job?jobType=Import');
          },
          closable: true,
        });
        props.onCancel?.();
      }
    }
    return Upload.LIST_IGNORE;
  };
  return (
    <>
      <Modal
        title={intl.formatMessage({ id: 'goods.importModal.title' })}
        width={640}
        maskClosable={false}
        open={props.visible}
        centered
        onCancel={props.onCancel}
        footer={null}
      >
        <div className="rounded p-4 mt-4 bg-[#00000008] flex flex-col">
          <span className="font-semibold text-[16px]">{intl.formatMessage({ id: 'goods.importModal.createCustomGoodsTitle' })}</span>
          <div className="text-[#00000073]">
            {intl.formatMessage({ id: 'goods.importModal.createCustomGoodsDesc' })}
          </div>
          <Space className="mt-3" size={40}>
            <div className="cursor-pointer flex justify-center items-center">
              <Image src={downloadIcon} height={24} width={24} preview={false} />
              <span
                className="ml-2"
                onClick={() =>
                  downloadFile(
                    'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E8%87%AA%E5%BB%BA%E5%95%86%E5%93%81%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
                  )
                }
              >
                {intl.formatMessage({ id: 'goods.importModal.downloadTemplate' })}
              </span>
            </div>
            <Upload beforeUpload={beforeUpload} accept={ACCEPT_FILE_EXT} showUploadList={false}>
              <div
                className="cursor-pointer flex justify-center items-center"
                onClick={() =>
                  setFileImportParams((pre) => ({
                    ...pre,
                    moduleId: 'GOODS_CUSTOM_MEMBER_BATCH_IMPORT',
                    taskDesc: '零售商批量创建自建商品',
                  }))
                }
              >
                <Image src={importIcon} height={24} width={24} preview={false} />
                <span className="ml-2">{intl.formatMessage({ id: 'goods.importModal.importFile' })}</span>
              </div>
            </Upload>
          </Space>
        </div>
        <div className="rounded p-4 mt-4 bg-[#00000008] flex flex-col">
          <span className="font-semibold text-[16px]">{intl.formatMessage({ id: 'goods.importModal.importPriceTitle' })}</span>
          <div className="text-[#00000073]">
            {intl.formatMessage({ id: 'goods.importModal.importPriceDesc' })}
          </div>
          <Space className="mt-3" size={40}>
            <div className="cursor-pointer flex justify-center items-center">
              <Image src={downloadIcon} height={24} width={24} preview={false} />
              <span
                className="ml-2"
                onClick={() =>
                  downloadFile(
                    'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E5%95%86%E5%93%81%E4%BB%B7%E6%A0%BC%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
                  )
                }
              >
                {intl.formatMessage({ id: 'goods.importModal.downloadTemplate' })}
              </span>
            </div>
            <Upload beforeUpload={beforeUpload} accept={ACCEPT_FILE_EXT} showUploadList={false}>
              <div
                className="cursor-pointer flex justify-center items-center"
                onClick={() =>
                  setFileImportParams((pre) => ({
                    ...pre,
                    moduleId: 'GOODS_ITEM_PRICE_IMPORT',
                    taskDesc: '零售商批量创建商品价格',
                  }))
                }
              >
                <Image src={importIcon} height={24} width={24} preview={false} />
                <span className="ml-2">{intl.formatMessage({ id: 'goods.importModal.importFile' })}</span>
              </div>
            </Upload>
          </Space>
        </div>
        <div className="rounded p-4 mt-4 bg-[#00000008] flex flex-col">
          <span className="font-semibold text-[16px]">{intl.formatMessage({ id: 'goods.importModal.importSupplierRelationTitle' })}</span>
          <div className="text-[#00000073]">
            {intl.formatMessage({ id: 'goods.importModal.importSupplierRelationDesc' })}
          </div>
          <Space className="mt-3" size={40}>
            <div className="cursor-pointer flex justify-center items-center">
              <Image src={downloadIcon} height={24} width={24} preview={false} />
              <span
                className="ml-2"
                onClick={() =>
                  downloadFile(
                    'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E4%BE%9B%E5%BA%94%E5%95%86%E4%B8%8E%E5%95%86%E5%93%81%E5%85%B3%E8%81%94%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'
                  )
                }
              >
                {intl.formatMessage({ id: 'goods.importModal.downloadTemplate' })}
              </span>
            </div>
            <Upload beforeUpload={beforeUpload} accept={ACCEPT_FILE_EXT} showUploadList={false}>
              <div
                className="cursor-pointer flex justify-center items-center"
                onClick={() =>
                  setFileImportParams((pre) => ({
                    ...pre,
                    moduleId: 'GOODS_SUPPLIER_IMPORT',
                    taskDesc: '供应商与商品关系批量导入',
                  }))
                }
              >
                <Image src={importIcon} height={24} width={24} preview={false} />
                <span className="ml-2">{intl.formatMessage({ id: 'goods.importModal.importFile' })}</span>
              </div>
            </Upload>
          </Space>
        </div>
      </Modal>
    </>
  );
};
