import StocksInfoDrawer, { StocksInfoDrawerProps } from '@/components/StocksInfoDrawer';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { exportData } from '@/utils/exportData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ProFormInstance } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { Space } from 'antd';
import React, { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import GoodsCreateDrawerForm from './components/GoodsCreateDrawerForm';
import GoodsImportModal from './components/GoodsImportModal';
import { PostListTableColumns } from './config/GoodsTableColumns';
import { queryGoodsPage, updateGoodsStatusBatch } from './services';
import { type GoodsCreateDrawerFormType } from './types/GoodsCreateDrawerFormType';
import { type GoodsEntity } from './types/GoodsEntity.entity';
import { type GoodsImportModalType } from './types/GoodsImportModalType';

const GoodsList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  // 显示总库存信息
  const [stocksDrawer, setStocksDrawer] = useState<StocksInfoDrawerProps>({
    visible: false,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  // 新增商品
  const [createModalProps, setCreateModalProps] = useState<GoodsCreateDrawerFormType>({
    visible: false,
    recordId: 0,
    readOnly: false,
    title: intl.formatMessage({ id: 'goods.list.createGoodsTitle' }),
    onCancel: () => null,
  });

  // 导入商品
  const [importModalProps, setImportModalProps] = useState<GoodsImportModalType>({
    visible: false,
  });


  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 关闭【新增商品】对话框
   */
  const hideCreateModal = (reload: boolean) => {
    setCreateModalProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      recordId: 0,
      readOnly: false,
    }));
    if (!reload) return;
    setTimeout(() => {
      actionRef.current?.reload(reload);
    }, 1500);
  };

  /**
   * 关闭【导入商品】对话框
   */
  const hideImportModal = () => {
    setImportModalProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
    }));
  };

  /**
   * 上架/下架
   * @param id
   */
  const handleUpOrDownItem = async (ids: React.Key[], status: number) => {
    const result = await updateGoodsStatusBatch({ itemIdList: ids, itemStatus: status });
    if (result) {
      setTimeout(() => {
        actionRef.current?.reload(true);
      }, 1500);
      setSelectedRowKeys([]);
    }
  };

  /**
   * 编辑
   * @param id
   */
  const handleUpdateItem = async (id: string) => {
    setCreateModalProps((preModalProps) => ({
      ...preModalProps,
      visible: true,
      readOnly: false,
      recordId: id,
      title: intl.formatMessage({ id: 'goods.list.editGoodsTitle' }),
    }));
  };
  /**
   * 查看库存信息面板
   */
  const handleViewStocksInfo = (data: StocksInfoDrawerProps) => {
    setStocksDrawer((pre) => ({
      ...pre,
      ...data,
      visible: true,
    }));
  };

  return (
    <PageContainer>
      <FunProTable<GoodsEntity, any>
        rowKey="itemId"
        requestPage={queryGoodsPage}
        formRef={formRef}
        actionRef={actionRef}
        rowSelection={{
          selectedRowKeys: selectedRowKeys,
          onChange: (selectedKeys) => {
            setSelectedRowKeys(selectedKeys);
          },
        }}
        columns={PostListTableColumns({
          handleUpdateItem,
          handleUpOrDownItem,
          handleViewStocksInfo,
        })}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              key="create"
              authority="addGoods"
              onClick={() => {
                setCreateModalProps((preModalProps) => ({
                  ...preModalProps,
                  visible: true,
                  readOnly: false,
                  title: intl.formatMessage({ id: 'goods.list.createGoodsTitle' }),
                }));
              }}
            >
              {intl.formatMessage({ id: 'goods.list.createGoodsButton' })}
            </AuthButton>
            <AuthButton
              danger
              key="importGoods"
              authority="importGoods"
              onClick={() => {
                setImportModalProps((preModalProps) => ({ ...preModalProps, visible: true }));
              }}
            >
              {intl.formatMessage({ id: 'goods.list.importGoodsButton' })}
            </AuthButton>
            <AuthButton
              danger
              authority="exportGoods"
              onClick={async () => {
                const params = formRef.current?.getFieldsValue();
                if (params.brandId) {
                  params.brandIdList = [params.brandId];
                }
                if (params.categoryId) {
                  params.categoryIdList = params.categoryId;
                }
                exportData({
                  systemId: 'ETC_SAAS_SYS',
                  taskDesc: intl.formatMessage({ id: 'goods.list.exportGoodsTaskDesc' }),
                  moduleId: 'GOODS_MEMBER_EXPORT',
                  params: params,
                });
              }}
            >
              {intl.formatMessage({ id: 'goods.list.exportGoodsButton' })}
            </AuthButton>

            <AuthButton
              disabled={selectedRowKeys.length <= 0}
              danger
              authority="enableGoods"
              onClick={() => handleUpOrDownItem(selectedRowKeys, 1)}
            >
              {intl.formatMessage({ id: 'goods.list.onlineButton' })}
            </AuthButton>
            <AuthButton
              disabled={selectedRowKeys.length <= 0}
              danger
              authority="disableGoods"
              onClick={() => handleUpOrDownItem(selectedRowKeys, 0)}
            >
              {intl.formatMessage({ id: 'goods.list.offlineButton' })}
            </AuthButton>
          </Space>
        }
      />

      <GoodsCreateDrawerForm {...createModalProps} onCancel={hideCreateModal} onRefresh={() => {
        const timer = setTimeout(() => {
          actionRef.current?.reload(true);
          clearTimeout(timer);
        }, 1000)
      }} />
      <GoodsImportModal {...importModalProps} onCancel={hideImportModal} />
      <StocksInfoDrawer {...stocksDrawer} onClose={() => setStocksDrawer({})} />
    </PageContainer>
  );
};

export default withKeepAlive(GoodsList);