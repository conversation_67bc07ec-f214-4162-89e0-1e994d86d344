import type { PageRequestParamsType } from '@/types/PageRequestParamsType';

export interface ItemQueryPageRequest extends PageRequestParamsType {
  skuType: number;
  pageNo?: number;
  pageSize?: number;
  oeNo?: string;
  brandPartNo?: string;
  brandId?: string;
  categoryId?: string[];
  skuStatus?: number;
  /**
   * 品牌ID集合，精准匹配
   */
  brandIdList?: string[];
  /**
   * 类目ID集合，只传三级类目ID，精准匹配
   */
  categoryIdList?: string[];
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * etc号集合，精准匹配
   */
  etcNoList?: string[];
  /**
   * 是否仅看有货
   */
  hasStock?: boolean;
  /**
   * 是否精准查询，针对使用关键字查询
   */
  isAccurate?: boolean;
  /**
   * 是否获取品牌聚合数据
   */
  isFetchBrandAggs?: boolean;
  /**
   * 是否获取类目聚合数据
   */
  isFetchCategoryAggs?: boolean;
  /**
   * 商品ID集合，精准匹配
   */
  itemIdList?: string[];
  /**
   * 商品编码集合，精确匹配
   */
  itemSnList?: string[];
  /**
   * 状态:0-删除1-启用2-禁用
   */
  itemStatus?: number;
  /**
   * 1-标准2-非标
   */
  itemType?: number;
  /**
   * 门店零售商id
   */
  memberId?: string;
  /**
   * 车型ID，vin解析场景，精准匹配
   */
  modelId?: string;
  /**
   * OE码集合，epc、日产、车型查询场景，精准匹配
   */
  oeNoList?: string[];
  /**
   * 查询关键字，通过商品名称、商品编码、OE、品牌件号、助记码进行匹配
   */
  queryKeyWord?: string;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 仓库ID，仅看有货时必传
   */
  warehouseId?: string;

  isFetchAllInventory?: boolean;
}
