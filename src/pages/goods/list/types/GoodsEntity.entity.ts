import type { YesOrNoType } from '@/types/CommonStatus';

export interface GoodsEntity {
  /** 商品ID */
  itemId: string;
  /** 图片列表 */
  images: string[];
  /** 采购状态: 0-否 1-是 */
  purchaseStatus: YesOrNoType;
  /** skuId */
  skuId: string;
  /** 商品名称 */
  itemName: string;
  /** 商品编码 */
  itemSn: string;
  /** 状态: 0-下架 1-上架 */
  itemStatus: YesOrNoType;
  /** 销售状态: 0-否 1-是 */
  saleStatus: YesOrNoType;
  /** 产地id */
  originRegionId: string;
  /** 适用车型 */
  adaptModel: string;
  /** 助记码 */
  memCode: string;
  /** 商品条形码 */
  barCode: string;
  /** 最低限价 */
  lowPrice: number;
  /** 建议售价 */
  suggestPrice: number;
  /** 采购价 */
  purchasePrice: number;
  /** oe号列表 */
  oeNos: string[];
  /** 品牌件号列表 */
  brandPartNos: string[];
  /** etc适配车型id列表 */
  adaptModelIds: string[];
  /** 是否套装：0-否 1-是 */
  isSuit: YesOrNoType;
  /** 适配车系 */
  adaptSeries: string;
  /** 适配车系code */
  adaptSeriesCode: string;
  /** 商品备注 */
  skuRemark: string;
  /** 三方商品号 */
  thirdNo: string;
  /** SKU商品名称 */
  skuName: string;
  /** 门店零售商id */
  memberId: string;
  /** 单位id */
  unitId: string;
  /** 单位名称 */
  unitName: string;
  /** 产地名称 */
  originRegionName: string;
  /** 最小包装量 */
  minPackNum: number;
  /** 最小起订量 */
  minOrderNum: number;
  /** 品牌ID */
  brandId: string;
  /** 品牌名称 */
  brandName: string;
  /** 三方数据编码 */
  sourceCode: string;
  /** 0-标准 1-非标 */
  itemType: 0 | 1;
  /** 三级类目名称 */
  categoryName: string;
  /** 类目ID */
  categoryId: string;
  /** 备注 */
  remark: string;
  /** 商品规格 */
  spec: string;
  /** 更新人员 */
  updatePerson?: string;
  /** 创建人员 */
  createPerson?: string;
  /** 更新时间 */
  updateTime?: Date;
  /** 是否删除 */
  isDelete?: YesOrNoType;
  /** 创建时间 */
  createTime?: string;
  // 可用库存
  inventoryNum: number;
  // 锁定库存
  lockedNum: number;
  /**
   * 价格等级信息
   */
  priceDetails?: IcItemPriceCmdList[];
}

/**
 * 商品新增请求参数
 */
export interface GoodsAddEntity {
  /**
   * 适用车型
   */
  adaptModel?: string;
  /**
   * 商品条形码
   */
  barCode?: string;
  /**
   * 品牌ID
   */
  brandId?: string;
  /**
   * 关联品牌件号列表
   */
  brandPartNoList?: string[];
  /**
   * 三级类目ID
   */
  categoryId?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 商品ID主键
   */
  itemId?: string;
  /**
   * 图片信息
   */
  itemImageList?: GoodsImageList[];
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 状态:0-删除1-启用2-禁用
   */
  itemStatus?: number;
  /**
   * 最低限价
   */
  lowPrice?: number;
  /**
   * 门店零售商id
   */
  memberId?: string;
  /**
   * 助记码
   */
  memCode?: string;
  /**
   * 最小起订量
   */
  minOrderNum?: number;
  /**
   * 最小包装量
   */
  minPackNum?: number;
  /**
   * 关联OE码列表
   */
  oeNoList?: string[];
  /**
   * 产地属性ID
   */
  originRegionId?: string;
  /**
   * 采购价
   */
  purchasePrice?: number;
  /**
   * 采购状态:0-否1-是
   */
  purchaseStatus?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 销售状态:0-否1-是
   */
  saleStatus?: number;
  /**
   * skuId
   */
  skuId?: string;
  /**
   * 商品规格
   */
  spec?: string;
  /**
   * 建议售价
   */
  suggestPrice?: number;
  /**
   * 单位id
   */
  unitId?: string;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 价格等级信息
   */
  priceDetails?: IcItemPriceCmdList[];
}

export interface GoodsImageList {
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 图片地址
   */
  imageUrl?: string;
  /**
   * 是否删除:0-未删除1-已删除
   */
  isDelete?: number;
  /**
   * 商品ID
   */
  itemId?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 类目排序
   */
  sort?: number;
  /**
   * 图片标题
   */
  title?: string;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}
export interface IcItemPriceCmdList {
  /**
   * 分层定价id
   */
  levelId?: string;
  /**
   * 分层定价name
   */
  levelName?: string;
  /**
   * 分层价格
   */
  levelPrice?: number;
}
/**
 * 根据OE号查询商品结果数据
 */
export interface GoodsInfoByOEResult {
  /**
   * 适用品牌
   */
  carBrandName?: string;
  /**
   * 零件名称
   */
  label?: string;
  /**
   * oe号
   */
  oeNo?: string;
  /**
   * 零件备注
   */
  remark?: string;
  /**
   * 原厂名称
   */
  standardLabel?: string;
  /**
   * 标准名称位置信息
   */
  standardLabelRemark?: string;
}

/**
 * 查询套装商品信息结果
 */
export interface SuitGoodsResult {
  /**
   * 单品号
   */
  code?: string;
  /**
   * 数量
   */
  num?: string;
  /**
   * 原厂oe号
   */
  oeNo?: string;
  /**
   * 单品名称
   */
  skuName?: string;
}

/**
 * 云端商品选择
 */
export interface GoodsSelectType {
  skuId: string;
  oeNoStr: string;
  brandPartNoStr: string;
  /**
   * 商品名称
   */
  itemName: string;
  /**
   * 品牌
   */
  brandName: string;
  /**
   * 商品分类
   */
  categoryName: string;
  /**
   * 单位名称
   */
  unitName: string;
  /**
   * 适用车系
   */
  adaptorSeris: string;
  /**
   * 商品备注
   */
  skuRemark: string;
}
