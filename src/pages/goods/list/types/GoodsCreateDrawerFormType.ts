export interface GoodsCreateDrawerFormType {
  title: string;
  recordId?: React.Key;
  visible: boolean;
  readOnly: boolean;
  onCancel: (reload: boolean) => void;
  onRefresh?: () => void;
  /**
   * 云端商品添加
   */
  cloudOuterParams?: GoodsCreateOuterCloud;
  /**
   *  oe添加商品
   */
  oEOuterParams?: GoodsCreateOuterOe;
}
export interface GoodsCreateOuterCloud {
  /**
   * 商品skuId
   */
  skuId: string;
  /**
   * 商品skuName
   */
  itemName?: string;
  oeNos?: string;
}
export interface GoodsCreateOuterOe {
  /**
   * 回填商品名称
   */
  itemName?: string;
  /**
   * 回填OE号
   */
  oeNos: string[];
}
