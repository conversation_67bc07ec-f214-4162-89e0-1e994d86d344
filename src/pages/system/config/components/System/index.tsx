import LeftTitle from '@/components/LeftTitle';
import { querySysPropertyList, setSysProperty } from '@/services/systerm';
import {
  ProCard,
  ProForm,
  ProFormInstance,
  ProFormSwitch
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { message, Space } from 'antd';
import { useEffect, useRef } from 'react';

const defaultFormData = {
  externalPurchaseAudit: false,
  vinRule: 'VIN',
  pushRule: [],
  priceRule: true,
  stockRule: false,
};

const System = ({ type }) => {
  const intl = useIntl();
  const formRef = useRef<ProFormInstance>();

  const handleValuesChange = (changedValue: any, allValues: any) => {
    if (changedValue.hasOwnProperty('pushRule') && changedValue?.pushRule.length === 0) {
      message.error(intl.formatMessage({ id: 'system.config.system.settingFailedEmpty' }));
      queryFormData();
      return;
    } else if (
      changedValue.hasOwnProperty('pushRule') &&
      changedValue?.pushRule.includes('CUSTOM') &&
      !allValues.priceRule
    ) {
      message.error(intl.formatMessage({ id: 'system.config.system.priceRuleError' }));
      formRef.current.setFieldsValue({ pushRule: ['TXT'] });
      return;
    } else if (changedValue.hasOwnProperty('priceRule') && !changedValue?.priceRule) {
      formRef.current.setFieldsValue({ pushRule: ['TXT'] });
    }
    const values = formRef.current.getFieldsValue();
    setSysProperty({ type, value: values, propDimensions: 'MEMBER' }).then((result) => {
      if (result) {
        message.success(intl.formatMessage({ id: 'system.config.system.saveSuccess' }));
        queryFormData();
      }
    });
  };

  useEffect(() => {
    queryFormData();
  }, []);

  const queryFormData = () => {
    querySysPropertyList({ type, propDimensions: 'MEMBER' }).then((result) => {
      if (result?.[0]?.value) {
        formRef.current?.setFieldsValue(JSON.parse(result?.[0].value));
      }
    });
  };

  return (
    <ProForm
      submitter={false}
      layout="horizontal"
      onValuesChange={handleValuesChange}
      formRef={formRef}
      initialValues={defaultFormData}
    >
      <Space direction={'vertical'} className="w-full" size={'middle'}>
        <ProCard title={<LeftTitle title={intl.formatMessage({ id: 'system.config.system.purchase' })} />} bordered={true}>
          <ProFormSwitch label={intl.formatMessage({ id: 'system.config.system.externalPurchaseAudit' })} name="externalPurchaseAudit" />
          <p className="text-gray-400 -mt-[16px]">{intl.formatMessage({ id: 'system.config.system.externalPurchaseAuditDesc' })}</p>
          <ProFormSwitch label={intl.formatMessage({ id: 'system.config.system.purchaseInSyncGood' })} name="purchaseInSyncGood" />
          <p className="text-gray-400 -mt-[16px]">{intl.formatMessage({ id: 'system.config.system.purchaseInSyncGoodDesc' })}</p>
        </ProCard>
      </Space>
    </ProForm>
  );
};

export default System;
