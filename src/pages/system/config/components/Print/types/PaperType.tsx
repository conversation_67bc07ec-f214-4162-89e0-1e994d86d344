import { FormattedMessage } from '@umijs/max';

export enum PaperType {
  Full,
  half,
  Third,
}

export enum PaperTypeName {
  Full = 'system.config.paperType.full',
  half = 'system.config.paperType.half',
  Third = 'system.config.paperType.third',
}

export const PaperTypeSize = {
  Full: [2100, 2800],
  half: [2100, 1400],
  Third: [2100, 930],
};

export const paperTypeOptions = [
  {
    label: <FormattedMessage id={PaperTypeName.Full} />,
    value: PaperType.Full,
  },
  {
    label: <FormattedMessage id={PaperTypeName.half} />,
    value: PaperType.half,
  },
  {
    label: <FormattedMessage id={PaperTypeName.Third} />,
    value: PaperType.Third,
  },
];
