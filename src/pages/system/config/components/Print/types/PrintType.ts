export enum PrintType {
  salesOrder = 'salesOrder',
  salesReturnOrder = 'salesReturnOrder',
  outStockOrder = 'outStockOrder',
  inStockOrder = 'inStockOrder',
  checkOrder = 'checkOrder',
  purchaseOrder = 'purchaseOrder',
  purchaseReturnOrder = 'purchaseReturnOrder',
  goodTag = 'goodTag',
  locationTag = 'locationTag',
}

export enum PrintTypeName {
  salesOrder = '销售单',
  salesReturnOrder = '销售退货单',
  outStockOrder = '出库单',
  inStockOrder = '入库单',
  checkOrder = '盘点单',
  purchaseOrder = '采购单',
  purchaseReturnOrder = '采购退货单',
  goodTag = '商品标签',
  locationTag = '库位标签',
}
