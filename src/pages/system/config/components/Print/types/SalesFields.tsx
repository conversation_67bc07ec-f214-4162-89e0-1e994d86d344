import { FormattedMessage } from '@umijs/max';

export enum SalesFields {
  ItemSn = 'itemSn',
  ItemName = 'itemName',
  OeNo = 'oeNo',
  BrandPartNo = 'brandPartNo',
  BrandName = 'brandName',
  OriginRegionName = 'originRegionName',
  Spec = 'spec',
  AdaptModel = 'adaptModel',
  SaleNum = 'saleNum',
  UnitName = 'unitName',
  UnitPriceYuan = 'unitPriceYuan',
  ActualSellingTotalAmountYuan = 'actualSellingTotalAmountYuan',
  LocationCode = 'locationCode',
}

export enum SalesFieldsName {
  ItemSn = 'system.config.salesFields.itemSn',
  ItemName = 'system.config.salesFields.itemName',
  OeNo = 'system.config.salesFields.oeNo',
  BrandPartNo = 'system.config.salesFields.brandPartNo',
  BrandName = 'system.config.salesFields.brandName',
  OriginRegionName = 'system.config.salesFields.originRegionName',
  Spec = 'system.config.salesFields.spec',
  AdaptModel = 'system.config.salesFields.adaptModel',
  SaleNum = 'system.config.salesFields.saleNum',
  UnitName = 'system.config.salesFields.unitName',
  UnitPriceYuan = 'system.config.salesFields.unitPriceYuan',
  ActualSellingTotalAmountYuan = 'system.config.salesFields.actualSellingTotalAmountYuan',
  LocationCode = 'system.config.salesFields.locationCode',
}

export const salesFieldsOptions = [
  {
    label: <FormattedMessage id={SalesFieldsName.ItemSn} />,
    value: SalesFields.ItemSn,
    disabled: true,
  },
  {
    label: <FormattedMessage id={SalesFieldsName.ItemName} />,
    value: SalesFields.ItemName,
    disabled: true,
  },
  {
    label: <FormattedMessage id={SalesFieldsName.OeNo} />,
    value: SalesFields.OeNo,
  },
  {
    label: <FormattedMessage id={SalesFieldsName.BrandPartNo} />,
    value: SalesFields.BrandPartNo,
  },
  {
    label: <FormattedMessage id={SalesFieldsName.BrandName} />,
    value: SalesFields.BrandName,
  },
  {
    label: <FormattedMessage id={SalesFieldsName.OriginRegionName} />,
    value: SalesFields.OriginRegionName,
  },
  {
    label: <FormattedMessage id={SalesFieldsName.Spec} />,
    value: SalesFields.Spec,
  },
  {
    label: <FormattedMessage id={SalesFieldsName.AdaptModel} />,
    value: SalesFields.AdaptModel,
  },
  {
    label: <FormattedMessage id={SalesFieldsName.SaleNum} />,
    value: SalesFields.SaleNum,
    disabled: true,
  },
  {
    label: <FormattedMessage id={SalesFieldsName.UnitName} />,
    value: SalesFields.UnitName,
    disabled: true,
  },
  {
    label: <FormattedMessage id={SalesFieldsName.UnitPriceYuan} />,
    value: SalesFields.UnitPriceYuan,
    disabled: true,
  },
  {
    label: <FormattedMessage id={SalesFieldsName.ActualSellingTotalAmountYuan} />,
    value: SalesFields.ActualSellingTotalAmountYuan,
    disabled: true,
  },
  {
    label: <FormattedMessage id={SalesFieldsName.LocationCode} />,
    value: SalesFields.LocationCode,
    disabled: true,
  },
];

export const getDefaultSalesFields = () =>
  salesFieldsOptions.filter((item) => item.disabled).map((item) => item.value);
