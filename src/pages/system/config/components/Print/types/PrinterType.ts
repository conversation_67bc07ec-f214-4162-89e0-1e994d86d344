import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';

export enum PrinterType {
  'Order' = 'Order',
  'Tag' = 'Tag',
}

export enum PrinterTypeName {
  'Order' = '单据',
  'Tag' = '标签',
}

export const getPrinterType = (printType: PrintType) => {
  switch (printType) {
    case PrintType.salesOrder:
    case PrintType.salesReturnOrder:
    case PrintType.purchaseOrder:
    case PrintType.purchaseReturnOrder:
    case PrintType.inStockOrder:
    case PrintType.outStockOrder:
    case PrintType.checkOrder:
      return PrinterType.Order;
    case PrintType.goodTag:
    case PrintType.locationTag:
      return PrinterType.Tag;
  }
};

export const getPrinterTypeLocalStorageName = (printType: PrintType) => {
  return `default_printer_${getPrinterType(printType)}`;
};
