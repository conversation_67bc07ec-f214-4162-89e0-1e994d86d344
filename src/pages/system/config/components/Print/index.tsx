import LeftTitle from '@/components/LeftTitle';
import {
  PaperType,
  paperTypeOptions,
} from '@/pages/system/config/components/Print/types/PaperType';
import {
  getDefaultSalesFields,
  salesFieldsOptions,
} from '@/pages/system/config/components/Print/types/SalesFields';
import { ConfigType } from '@/pages/system/config/components/types/ConfigType';
import { querySysPropertyList, setSysProperty } from '@/services/systerm';
import {
  ProCard,
  ProForm,
  ProFormCheckbox,
  ProFormInstance,
  ProFormRadio,
  ProFormSwitch,
} from '@ant-design/pro-components';
import { ProFormText } from '@ant-design/pro-form';
import { useIntl } from '@umijs/max';
import { Button, message, Space } from 'antd';
import { useEffect, useRef, useState } from 'react';

const defaultFormData = {
  salesOrder: {
    paperType: PaperType.Third,
    fields: getDefaultSalesFields(),
  },
  salesReturnOrder: {
    paperType: PaperType.Third,
  },
  outStockOrder: {
    paperType: PaperType.Third,
  },
  inStockOrder: {
    paperType: PaperType.Third,
  },
  checkOrder: {
    paperType: PaperType.Third,
  },
  purchaseOrder: {
    paperType: PaperType.Third,
  },
  purchaseReturnOrder: {
    paperType: PaperType.Third,
  },
};

const Print = () => {
  const intl = useIntl();
  const formRef = useRef<ProFormInstance>();
  const [loading, setLoading] = useState(false);

  const handleSubmit = (values: any) => {
    setLoading(true);
    setSysProperty({ type: ConfigType.Print, value: values, propDimensions: 'ACCOUNT' })
      .then((result) => {
        if (result) {
          message.success(intl.formatMessage({ id: 'system.config.print.saveSuccess' }));
          queryFormData();
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    queryFormData();
  }, []);

  const queryFormData = () => {
    querySysPropertyList({ type: ConfigType.Print, propDimensions: 'ACCOUNT' }).then((result) => {
      if (result?.[0]?.value) {
        formRef.current?.setFieldsValue(JSON.parse(result?.[0].value));
      }
    });
  };

  return (
    <ProForm
      submitter={false}
      formRef={formRef}
      layout="horizontal"
      onFinish={handleSubmit}
      initialValues={defaultFormData}
    >
      <Space direction={'vertical'} className="w-full" size={'middle'}>
        {/*<ProCard title={<LeftTitle title="打印机设置" />} bordered={true}>*/}
        {/*  <ProFormText*/}
        {/*    label="打印机服务地址"*/}
        {/*    name="printIP"*/}
        {/*    width={500}*/}
        {/*    placeholder="例如: http://**************:8000"*/}
        {/*    rules={[REQUIRED_RULES]}*/}
        {/*  />*/}
        {/*  <p className="text-gray-400 -mt-[12px]">*/}
        {/*    安装打印机器的内网电脑的打印服务地址，例如：http://**************:8000*/}
        {/*  </p>*/}
        {/*</ProCard>*/}
        <ProCard title={<LeftTitle title={intl.formatMessage({ id: 'system.config.print.salesOrderSettings' })} />} bordered={true}>
          <ProFormRadio.Group
            label={intl.formatMessage({ id: 'system.config.print.paperType' })}
            name={['salesOrder', 'paperType']}
            options={paperTypeOptions}
          />
          <ProFormCheckbox.Group
            label={intl.formatMessage({ id: 'system.config.print.productFieldSettings' })}
            name={['salesOrder', 'fields']}
            options={salesFieldsOptions}
          />
          <ProFormSwitch label={intl.formatMessage({ id: 'system.config.print.printQrCode' })} name={['salesOrder', 'printQrCode']} />
          <ProFormText
            label={intl.formatMessage({ id: 'system.config.print.printRemark' })}
            name={['salesOrder', 'remark']}
            fieldProps={{ maxLength: 100 }}
          />
        </ProCard>
        <ProCard title={<LeftTitle title={intl.formatMessage({ id: 'system.config.print.salesReturnOrderSettings' })} />} bordered={true}>
          <ProFormRadio.Group
            label={intl.formatMessage({ id: 'system.config.print.paperType' })}
            name={['salesReturnOrder', 'paperType']}
            options={paperTypeOptions}
          />
          <ProFormSwitch label={intl.formatMessage({ id: 'system.config.print.printQrCode' })} name={['salesReturnOrder', 'printQrCode']} />
          <ProFormText
            label={intl.formatMessage({ id: 'system.config.print.printRemark' })}
            name={['salesReturnOrder', 'remark']}
            fieldProps={{ maxLength: 100 }}
          />
        </ProCard>
        <ProCard title={<LeftTitle title={intl.formatMessage({ id: 'system.config.print.outStockOrder' })} />} bordered={true}>
          <ProFormRadio.Group
            label={intl.formatMessage({ id: 'system.config.print.paperType' })}
            name={['outStockOrder', 'paperType']}
            options={paperTypeOptions}
          />
          <ProFormSwitch label={intl.formatMessage({ id: 'system.config.print.printQrCode' })} name={['outStockOrder', 'printQrCode']} />
          <ProFormText
            label={intl.formatMessage({ id: 'system.config.print.printRemark' })}
            name={['outStockOrder', 'remark']}
            fieldProps={{ maxLength: 100 }}
          />
        </ProCard>
        <ProCard title={<LeftTitle title={intl.formatMessage({ id: 'system.config.print.inStockOrder' })} />} bordered={true}>
          <ProFormRadio.Group
            label={intl.formatMessage({ id: 'system.config.print.paperType' })}
            name={['inStockOrder', 'paperType']}
            options={paperTypeOptions}
          />
          <ProFormSwitch label={intl.formatMessage({ id: 'system.config.print.printQrCode' })} name={['inStockOrder', 'printQrCode']} />
          <ProFormText
            label={intl.formatMessage({ id: 'system.config.print.printRemark' })}
            name={['inStockOrder', 'remark']}
            fieldProps={{ maxLength: 100 }}
          />
        </ProCard>
        <ProCard title={<LeftTitle title={intl.formatMessage({ id: 'system.config.print.checkOrder' })} />} bordered={true}>
          <ProFormRadio.Group
            label={intl.formatMessage({ id: 'system.config.print.paperType' })}
            name={['checkOrder', 'paperType']}
            options={paperTypeOptions}
          />
          <ProFormSwitch label={intl.formatMessage({ id: 'system.config.print.printQrCode' })} name={['checkOrder', 'printQrCode']} />
          <ProFormText
            label={intl.formatMessage({ id: 'system.config.print.printRemark' })}
            name={['checkOrder', 'remark']}
            fieldProps={{ maxLength: 100 }}
          />
        </ProCard>
        <ProCard title={<LeftTitle title={intl.formatMessage({ id: 'system.config.print.purchaseOrder' })} />} bordered={true}>
          <ProFormRadio.Group
            label={intl.formatMessage({ id: 'system.config.print.paperType' })}
            name={['purchaseOrder', 'paperType']}
            options={paperTypeOptions}
          />
          <ProFormSwitch label={intl.formatMessage({ id: 'system.config.print.printQrCode' })} name={['purchaseOrder', 'printQrCode']} />
          <ProFormText
            label={intl.formatMessage({ id: 'system.config.print.printRemark' })}
            name={['purchaseOrder', 'remark']}
            fieldProps={{ maxLength: 100 }}
          />
        </ProCard>
        <ProCard title={<LeftTitle title={intl.formatMessage({ id: 'system.config.print.purchaseReturnOrder' })} />} bordered={true}>
          <ProFormRadio.Group
            label={intl.formatMessage({ id: 'system.config.print.paperType' })}
            name={['purchaseReturnOrder', 'paperType']}
            options={paperTypeOptions}
          />
          <ProFormSwitch label={intl.formatMessage({ id: 'system.config.print.printQrCode' })} name={['purchaseReturnOrder', 'printQrCode']} />
          <ProFormText
            label={intl.formatMessage({ id: 'system.config.print.printRemark' })}
            name={['purchaseReturnOrder', 'remark']}
            fieldProps={{ maxLength: 100 }}
          />
        </ProCard>
        <Space>
          <Button htmlType="submit" type="primary" loading={loading}>
            {intl.formatMessage({ id: 'system.config.print.save' })}
          </Button>
        </Space>
      </Space>
    </ProForm>
  );
};

export default Print;
