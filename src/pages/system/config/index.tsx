import { ConfigType } from '@/pages/system/config/components/types/ConfigType';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import Print from './components/Print';
import System from './components/System';

const Config = () => {
  const intl = useIntl();
  const tabs = [
    {
      label: intl.formatMessage({ id: 'system.config.tabs.systemSettings' }),
      key: ConfigType.System,
      children: <System type={ConfigType.System} />,
    },
    {
      label: intl.formatMessage({ id: 'system.config.tabs.printSettings' }),
      key: ConfigType.Print,
      children: <Print />,
    },
  ];
  return (
    <PageContainer>
      <ProCard tabs={{ items: tabs, defaultActiveKey: ConfigType.System }} />
    </PageContainer>
  );
};

export default withKeepAlive(Config);
