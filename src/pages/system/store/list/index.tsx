import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { type CommonModelForm } from '@/types/CommonModelForm';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl, useModel } from '@umijs/max';
import { Space } from 'antd';
import { isEmpty } from 'lodash';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import { createPost, modifyPost, modifyStatusPost, queryStoreList } from '../services';
import FormModal from './components/modal';
import { PostListTableColumns } from './config/postListTableColumns';
import { type PostEntity } from './types/post.entity';

const StoreList = () => {
  const intl = useIntl();
  const { initialState, setInitialState } = useModel('@@initialState');

  const actionRef = useRef<ActionType>();
  const [storeModalProps, setStoreModalProps] = useState<CommonModelForm<string, PostEntity>>({
    visible: false,
    recordId: '0',
    readOnly: false,
    title: '',
  });

  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 禁用启用事件
   * @param ids
   */
  const handleDeleteItem = async (id: string, status: string) => {
    await modifyStatusPost({ id, status });
    actionRef.current?.reload(true);
  };

  const handleUpdateItem = async (id: string) => {
    setStoreModalProps({
      visible: true,
      recordId: id,
      readOnly: false,
      title: intl.formatMessage({ id: 'system.store.list.editStore' }),
    });
  };

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setStoreModalProps({ visible: false, recordId: '0', readOnly: false, title: '' });
  };

  /**
   * 新增或编辑
   * @param values
   */
  const handleSaveOrUpdate = async (values: PostEntity) => {
    try {
      const { id } = values;
      let result;
      if (id) {
        //编辑
        result = await modifyPost({ ...values });
      } else {
        //新增
        result = await createPost({ ...values });
      }
      const { code } = result;
      if (code == 0) {
        hideModal();
        actionRef.current?.reload(true);
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  return (
    <PageContainer>
      <FunProTable<PostEntity, any>
        rowKey="id"
        requestPage={queryStoreList}
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        columns={PostListTableColumns({
          handleUpdateItem,
          handleDeleteItem,
          etcMemberId: initialState?.currentUser?.etcMemberId,
          intl,
        })}
        headerTitle={
          <Space>
            {isEmpty(initialState?.currentUser?.etcMemberId) && (
              <AuthButton
                type="primary"
                key="primary"
                authority="addStore"
                onClick={() =>
                  setStoreModalProps({
                    visible: true,
                    recordId: '0',
                    readOnly: false,
                    title: intl.formatMessage({ id: 'system.store.list.addStore' }),
                  })
                }
              >
                {intl.formatMessage({ id: 'common.button.add' })}
              </AuthButton>
            )}
          </Space>
        }
      />
      <FormModal {...storeModalProps} onOk={handleSaveOrUpdate} onCancel={hideModal} />
    </PageContainer>
  );
};
export default withKeepAlive(StoreList);
