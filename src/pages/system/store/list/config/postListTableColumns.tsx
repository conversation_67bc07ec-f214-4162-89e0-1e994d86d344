import AuthButton from '@/components/common/AuthButton';
import type { ProColumns } from '@ant-design/pro-components';
import { Popconfirm, Space, Tag } from 'antd';
import { isEmpty } from 'lodash';
import { postStatusOptions, setStatusValue, statusAttribute } from '../types/PostStatus';
import type { PostEntity } from '../types/post.entity';
export interface PostListTableColumnsProps {
  handleDeleteItem: (id: string, status: string) => void;
  handleUpdateItem: (id: string) => void;
  etcMemberId?: string;
  intl: any;
}

export const PostListTableColumns = (props: PostListTableColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: props.intl.formatMessage({ id: 'system.store.list.storeId' }),
      dataIndex: 'id',
      key: 'id',
      search: false,
      width: 100,
    },
    {
      title: props.intl.formatMessage({ id: 'system.store.list.storeName' }),
      dataIndex: 'name',
      key: 'name',
      search: true,
      width: 160,
      render: (text, record) => {
        return (
          <Space>
            <span>{text}</span>
            {record.type == '0' && <Tag color="blue">{props.intl.formatMessage({ id: 'system.store.list.mainStore' })}</Tag>}
          </Space>
        );
      },
    },
    {
      title: props.intl.formatMessage({ id: 'system.store.list.contactPerson' }),
      dataIndex: 'contactPerson',
      key: 'contactPerson',
      search: false,
      width: 100,
    },
    {
      title: props.intl.formatMessage({ id: 'system.store.list.contactPhone' }),
      dataIndex: 'contactPhone',
      key: 'contactPhone',
      search: false,
      width: 120,
      ellipsis: true,
    },
    {
      title: props.intl.formatMessage({ id: 'system.store.list.storeAddress' }),
      dataIndex: 'storeAddress',
      key: 'storeAddress',
      width: 120,
      search: false,
      ellipsis: true,
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.createTime' }),
      dataIndex: 'createTime',
      key: 'createTime',
      search: false,
      width: 140,
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.status' }),
      dataIndex: 'status',
      key: 'status',
      search: false,
      width: 80,
      valueEnum: postStatusOptions,
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 100,
      fixed: 'right',
      render: (text, record) => (
        <Space>
          <AuthButton
            authority="editStore"
            isHref
            onClick={() => props.handleUpdateItem(record.id ?? '')}
          >
            {props.intl.formatMessage({ id: 'common.button.edit' })}
          </AuthButton>
          {isEmpty(props.etcMemberId) && (
            <Popconfirm
              title={props.intl.formatMessage({ id: 'system.store.list.confirm.enableDisable' }, { action: props.intl.formatMessage({ id: statusAttribute[record.status ?? ''] }) })}
              onConfirm={() =>
                props.handleDeleteItem(record.id ?? '', setStatusValue[record.status ?? ''])
              }
            >
              <AuthButton authority="deleteStore" isHref>
                {props.intl.formatMessage({ id: statusAttribute[record.status ?? ''] })}
              </AuthButton>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ] as ProColumns<PostEntity>[];
