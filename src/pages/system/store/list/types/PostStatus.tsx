import { FormattedMessage } from "@umijs/max";

export enum PostStatus {
  ENABLE = 'common.option.enable',
  NOT_ENABLE = 'common.option.disable',
}

export const postStatusOptions = {
  '1': { text: <FormattedMessage id={PostStatus.ENABLE} />, status: 'Success' },
  '0': { text: <FormattedMessage id={PostStatus.NOT_ENABLE} />, status: 'Error' },
};

export const statusAttribute: Record<string, string> = {
  '0': PostStatus.ENABLE,
  '1': PostStatus.NOT_ENABLE,
};

export const setStatusValue: Record<string, string> = {
  '0': '1',
  '1': '0',
};
