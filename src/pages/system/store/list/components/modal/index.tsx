import { queryDistrictAreaTree, queryPostDetail } from '@/pages/system/store/services';
import type { CommonModelForm } from '@/types/CommonModelForm';
import { ModalForm, ProFormCascader, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { useIntl, useModel } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Button } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { isEmpty } from 'lodash';
import type { PostEntity } from '../../types/post.entity';
export default (props: CommonModelForm<string, PostEntity>) => {
  const intl = useIntl();
  const [form] = useForm<PostEntity>();
  const { initialState, setInitialState } = useModel('@@initialState');
  useAsyncEffect(async () => {
    if (props.recordId == '0') {
      form.resetFields();
    } else {
      const data = await queryPostDetail({ id: props.recordId });
      form.setFieldsValue(data);
      if (data.provinceId) {
        form.setFieldValue('areaTree', [data.provinceId, data.cityId, data.districtId]);
      }
    }
  }, [props.visible]);

  const submitter = {
    render: () => (
      <Button key="cancel" size="middle" onClick={props.onCancel}>
        {intl.formatMessage({ id: 'common.button.cancel' })}
      </Button>
    ),
  };

  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };
  const rules = [{ required: !props.readOnly }];
  return (
    <ModalForm
      {...layout}
      form={form}
      layout="horizontal"
      title={props.title}
      open={props.visible}
      width="40%"
      modalProps={{
        centered: true,
        onCancel: props.onCancel,
        destroyOnClose: true,
      }}
      submitter={props.readOnly ? submitter : {}}
      onFinish={props.onOk}
    >
      <ProFormText name="id" disabled={props.readOnly} hidden={true} />
      <ProFormText
        rules={rules}
        name="name"
        disabled={props.readOnly || !isEmpty(initialState?.currentUser?.etcMemberId)}
        label={intl.formatMessage({ id: 'system.store.list.form.storeName' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.input' })}
      />
      <ProFormText
        name="contactPerson"
        disabled={props.readOnly}
        label={intl.formatMessage({ id: 'system.store.list.form.contactPerson' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.input' })}
      />
      <ProFormText
        rules={[
          {
            pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
            message: intl.formatMessage({ id: 'system.store.list.form.contactPhoneValidation' }),
          },
        ]}
        name="contactPhone"
        disabled={props.readOnly}
        label={intl.formatMessage({ id: 'system.store.list.form.contactPhone' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.input' })}
      />
      <ProFormCascader
        name="areaTree"
        disabled={props.readOnly}
        label={intl.formatMessage({ id: 'system.store.list.form.storeAddress' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.select' })}
        fieldProps={{ fieldNames: { label: 'areaName', value: 'areaId' } }}
        transform={(values: any) => {
          if (values) {
            return {
              provinceId: values[0], //省
              cityId: values[1], //市
              districtId: values[2] ?? values[1], //区
            };
          }
        }}
        request={queryDistrictAreaTree}
      />
      <ProFormText
        name="detailAddress"
        disabled={props.readOnly}
        label={intl.formatMessage({ id: 'system.store.list.form.detailAddress' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.input' })}
      />
      <ProFormTextArea
        name="remark"
        disabled={props.readOnly}
        label={intl.formatMessage({ id: 'common.label.remark' })}
        fieldProps={{ count: { max: 100, show: true }, maxLength: 100 }}
        placeholder={intl.formatMessage({ id: 'common.placeholder.input' })}
      />
    </ModalForm>
  );
};
