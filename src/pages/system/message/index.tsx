import FunProTable from '@/components/common/FunProTable';
import MessageDetailModal, {
  MessageDetailModalProps,
} from '@/pages/system/message/components/MessageDetailModal';
import { msgListColumns } from '@/pages/system/message/config/msgListColumns';
import { queryMsgList } from '@/pages/system/message/services';
import { MsgListItemEntity } from '@/pages/system/message/types/msg.list.item.entity';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { type ActionType, PageContainer } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';

export type ViewDetailModalProps = Pick<MessageDetailModalProps, 'id' | 'visible'>;

const MessageList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [viewDetailModalProps, setViewDetailModalProps] = useState<ViewDetailModalProps>({
    visible: false,
  });

  useActivate(() => {
    actionRef.current?.reload();
  });

  const handleView = (record: MsgListItemEntity) => {
    setViewDetailModalProps({
      id: record.id,
      visible: true,
    });
  };

  return (
    <PageContainer>
      <FunProTable<MsgListItemEntity>
        options={false}
        actionRef={actionRef}
        scroll={{ x: 1300 }}
        requestPage={(query) => queryMsgList({ ...query, isDelete: 0 })}
        columns={msgListColumns({ handleView, intl })}
      />
      <MessageDetailModal
        {...viewDetailModalProps}
        onClose={() => {
          setViewDetailModalProps({ id: undefined, visible: false });
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default withKeepAlive(MessageList);
