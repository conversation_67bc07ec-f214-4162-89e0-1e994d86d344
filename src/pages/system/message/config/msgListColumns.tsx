import ColumnRender from '@/components/ColumnRender';
import { msgBizEnum } from '@/pages/system/message/types/MsgBizType';
import { msgStatusEnum } from '@/pages/system/message/types/MsgStatus';
import type { MsgListItemEntity } from '@/pages/system/message/types/msg.list.item.entity';
import type { ProColumns } from '@ant-design/pro-components';

export interface MsgListColumnsProps {
  handleView: (record: MsgListItemEntity) => void;
  intl: any;
}

export const msgListColumns = (props: MsgListColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: props.intl.formatMessage({ id: 'system.message.list.messageTitle' }),
      dataIndex: 'title',
      search: false,
      width: 200,
    },
    {
      title: props.intl.formatMessage({ id: 'system.message.list.messageContent' }),
      dataIndex: 'content',
      ellipsis: true,
      search: false,
      renderText: ColumnRender.RichContentColumnRender,
    },
    {
      title: props.intl.formatMessage({ id: 'system.message.list.notificationTime' }),
      dataIndex: 'sendTime',
      search: false,
      width: 140,
    },
    {
      title: props.intl.formatMessage({ id: 'system.message.list.messageType' }),
      dataIndex: 'bizType',
      width: 100,
      valueEnum: msgBizEnum,
    },
    {
      title: props.intl.formatMessage({ id: 'system.message.list.status' }),
      dataIndex: 'msgStatus',
      width: 100,
      fixed: 'right',
      valueEnum: msgStatusEnum,
    },
    {
      title: props.intl.formatMessage({ id: 'system.message.list.operation' }),
      search: false,
      dataIndex: '',
      fixed: 'right',
      width: 60,
      renderText: (text, record) => <a onClick={() => props.handleView(record)}>{props.intl.formatMessage({ id: 'system.message.list.view' })}</a>,
    },
  ] as ProColumns<MsgListItemEntity>[];
