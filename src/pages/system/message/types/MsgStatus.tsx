import { FormattedMessage } from '@umijs/max';

export enum MsgStatus {
  NoRead,
  Read,
}

export const MsgStatusName = {
  NoRead: 'system.message.status.noRead',
  Read: 'system.message.status.read',
}

export const msgStatusEnum = {
  [MsgStatus.NoRead]: {
    text: <FormattedMessage id={MsgStatusName.NoRead} />,
    status: 'error',
  },
  [MsgStatus.Read]: {
    text: <FormattedMessage id={MsgStatusName.Read} />,
    status: 'success',
  },
};
