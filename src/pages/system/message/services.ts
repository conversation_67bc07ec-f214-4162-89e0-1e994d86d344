import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { request } from '@/utils/request';
import type { PageResponseDataType } from '@/types/PageResponseDataType';
import { QueryMsgListRequest } from '@/pages/system/message/types/query.msg.list.request';
import { MsgListItemEntity } from '@/pages/system/message/types/msg.list.item.entity';

/**
 * 查看消息列表
 * @param params
 */
export const queryMsgList = async (params: QueryMsgListRequest & PageRequestParamsType) => {
  return request<PageResponseDataType<MsgListItemEntity>>(
    `/ipmsmessagecenter/MessageServiceFacade/queryMsgList`,
    {
      data: params,
    },
  );
};

/**
 * 查看消息详情
 * @param id
 */
export const queryMsgDetail = async (id: number) => {
  return request<MsgListItemEntity>(`/ipmsmessagecenter/MessageServiceFacade/queryMsgDetail`, {
    data: { id },
  });
};

/**
 * 查看未读消息数目
 */
export const getUnReadCount = async () => {
  return request<number>(`/ipmsmessagecenter/MessageServiceFacade/getUnReadCount`, {
    data: {},
  });
};

/**
 * 设置消息已读
 */
export const setRead = async (idList: number[]) => {
  return request<boolean>(`/ipmsmessagecenter/MessageServiceFacade/setRead`, {
    data: { idList },
  });
};

/**
 * 创建采购退货单
 */
export const createReturnOrderByETC = async (afterSalesNo: string) => {
  return request<string>(
    `/ipmspurchase/refundsupply/PurchaseReturnOrderFacade/createReturnOrderByETC`,
    {
      data: { afterSalesNo },
    },
  );
};
