export interface AddRolePostEntity {
  /**
   * 数据权限列表
   */
  dataPermissions?: string[];
  /**
   * 功能权限列表
   */
  menuPermissions?: MenuPermission[];

  /**
   * 名称
   */
  name?: string;
  /**
   * 备注
   */
  remark?: string;

  /**
   * 主建
   */
  id?: string;
}

export interface DataPermission {
  /**
   * dataId
   */
  dataId?: string;
}

export interface MenuPermission {
  /**
   * checkType   0 ：checked  1：halfchecked
   */
  checkType?: string;
  /**
   * menuId
   */
  menuId?: string;
  /**
   * roleId
   */
  roleId?: string;
}
