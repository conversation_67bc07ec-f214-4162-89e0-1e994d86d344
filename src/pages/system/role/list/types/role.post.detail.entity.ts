export interface RolePostDetailEntity {
  /**
   * 数据权限列表
   */
  dataPermissions?: string[];
  /**
   * ID
   */
  id?: string;
  /**
   * memberId
   */
  memberId?: string;
  /**
   * 选中
   */
  checkedMenuPermissions?: string[];

  /**
   * halfChecked选择
   */
  halfCheckedMenuPermissions?: string[];
  /**
   * 名称
   */
  name?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 状态：0禁用，1启用
   */
  status?: string;
  /**
   * 类型：0系统，1自建
   */
  type?: string;
}
