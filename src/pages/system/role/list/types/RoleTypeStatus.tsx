import { FormattedMessage } from "@umijs/max";

export enum RoleTypeStatus {
  PRESET = '0',
  CREATE = '1',
  SYSTEM = '-1',
}

export enum RoleTypeStatusName {
  PRESET = 'system.role.list.roleType.preset',
  CREATE = 'system.role.list.roleType.create',
  SYSTEM = 'system.role.list.roleType.system',
}

export const roleTypeStatusOptions = {
  [RoleTypeStatus.PRESET]: { text: <FormattedMessage id={RoleTypeStatusName.PRESET} />, status: '' },
  [RoleTypeStatus.CREATE]: { text: <FormattedMessage id={RoleTypeStatusName.CREATE} />, status: '' },
  [RoleTypeStatus.SYSTEM]: { text: <FormattedMessage id={RoleTypeStatusName.SYSTEM} />, status: '' },
};