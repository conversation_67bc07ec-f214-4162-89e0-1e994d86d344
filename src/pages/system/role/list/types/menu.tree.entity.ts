export interface MenuTreeEntity {
  /**
   * 子节点列表
   */
  children?: MenuTreeEntity[];
  /**
   * ID
   */
  id?: string;
  /**
   * 名称
   */
  name?: string;
  /**
   * 路径
   */
  path?: string;
  /**
   * 父ID
   */
  pid?: string;
  /**
   * 权限标识
   */
  resourceKey?: string;
  /**
   * 排序
   */
  sort?: number;
  /**
   * 状态：0禁用，1启用
   */
  status?: string;
  /**
   * 类型：0系统，1菜单，2按钮
   */
  type?: string;
}
