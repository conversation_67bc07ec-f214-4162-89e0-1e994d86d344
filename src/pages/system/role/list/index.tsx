import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { type CommonModelForm } from '@/types/CommonModelForm';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { Space } from 'antd';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import {
  createRolePost,
  modifyRolePost,
  modifyStatusRolePost,
  queryRolePostPage,
} from '../services';
import RoleModal from './components/RoleModal';
import { PostListTableColumns } from './config/postListTableColumns';
import { AddRolePostEntity } from './types/add.role.post.entity';
import { RolePostEntity } from './types/role.post.entity';

const RoleList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();

  const [roleModalProps, setRoleModalProps] = useState<CommonModelForm<string, AddRolePostEntity>>({
    visible: false,
    recordId: '0',
    readOnly: false,
    title: '',
  });


  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 禁用事件
   * @param ids
   */
  const handleDeleteItem = async (id: string, status: string) => {
    await modifyStatusRolePost({ id, status });
    actionRef.current?.reload(true);
  };

  const handleUpdateItem = async (id: string) => {
    setRoleModalProps({
      visible: true,
      recordId: id,
      readOnly: false,
      title: intl.formatMessage({ id: 'system.role.list.editRole' }),
    });
  };

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setRoleModalProps({
      visible: false,
      recordId: '0',
      readOnly: false,
      title: '',
    });
  };

  /**
   * 新增或编辑
   * @param values
   */
  const handleSaveOrUpdate = async (values: AddRolePostEntity) => {
    try {
      const { id } = values;
      let result;
      if (id) {
        //编辑
        result = await modifyRolePost({ ...values });
      } else {
        //新增
        result = await createRolePost({ ...values });
      }
      const { code } = result;
      if (code == 0) {
        hideModal();
        actionRef.current?.reload(true);
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  return (
    <PageContainer>
      <FunProTable<RolePostEntity, any>
        rowKey="id"
        requestPage={queryRolePostPage}
        scroll={{ x: 1300 }}
        actionRef={actionRef}
        columns={PostListTableColumns({
          handleUpdateItem,
          handleDeleteItem,
          intl,
        })}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              key="primary"
              authority="addRole"
              onClick={() =>
                setRoleModalProps({
                  visible: true,
                  recordId: '0',
                  readOnly: false,
                  title: intl.formatMessage({ id: 'system.role.list.addRole' }),
                })
              }
            >
              {intl.formatMessage({ id: 'common.button.add' })}
            </AuthButton>
          </Space>
        }
      />
      <RoleModal {...roleModalProps} onOk={handleSaveOrUpdate} onCancel={hideModal} />
    </PageContainer>
  );
};
export default withKeepAlive(RoleList);
