import AuthButton from '@/components/common/AuthButton';
import { postStatusOptions } from '@/pages/purchase/supplier/list/types/PostStatus';
import { setStatusValue, statusAttribute } from '@/types/EnableDisableStatus';
import type { ProColumns } from '@ant-design/pro-components';
import { Popconfirm, Space } from 'antd';
import { RoleTypeStatus, roleTypeStatusOptions } from '../types/RoleTypeStatus';
import type { RolePostEntity } from '../types/role.post.entity';

export interface PostListTableColumnsProps {
  handleDeleteItem: (id: string, status: string) => void;
  handleUpdateItem: (id: string) => void;
  intl: any;
}

export const PostListTableColumns = (props: PostListTableColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: props.intl.formatMessage({ id: 'system.role.list.roleName' }),
      dataIndex: 'name',
      key: 'name',
      search: true,
      width: 200,
    },
    {
      title: props.intl.formatMessage({ id: 'system.role.list.type' }),
      dataIndex: 'type',
      key: 'type',
      search: false,
      width: 120,
      valueEnum: roleTypeStatusOptions,
    },
    {
      title: props.intl.formatMessage({ id: 'system.role.list.description' }),
      dataIndex: 'remark',
      key: 'remark',
      search: false,
      ellipsis: true,
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.status' }),
      dataIndex: 'status',
      key: 'status',
      search: false,
      width: 100,
      valueEnum: postStatusOptions,
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 100,
      fixed: 'right',
      render: (text, record: RolePostEntity) => (
        <Space>
          {RoleTypeStatus.SYSTEM != record.type && (
            <>
              <AuthButton
                authority="editRole"
                isHref
                onClick={() => props.handleUpdateItem(record.id ?? '')}
              >
                {props.intl.formatMessage({ id: 'common.button.edit' })}
              </AuthButton>
              <Popconfirm
                title={props.intl.formatMessage({ id: 'system.role.list.confirm.enableDisable' }, { action: props.intl.formatMessage({ id: statusAttribute[record.status ?? ''] }) })}
                onConfirm={() =>
                  props.handleDeleteItem(record.id, setStatusValue[record.status ?? ''])
                }
              >
                <AuthButton authority="deleteRole" isHref>
                  {props.intl.formatMessage({ id: statusAttribute[record.status ?? ''] })}
                </AuthButton>
              </Popconfirm>
            </>
          )}
        </Space>
      ),
    },
  ] as ProColumns<RolePostEntity>[];
