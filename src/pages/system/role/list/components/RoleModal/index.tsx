import SubTitle from '@/components/common/SubTitle';
import type { CommonModelForm } from '@/types/CommonModelForm';
import { DrawerForm, ProCard, ProFormCheckbox } from '@ant-design/pro-components';
import { ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { useIntl } from '@umijs/max';
import { useAsyncEffect, useBoolean } from 'ahooks';
import { Tree } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { useState } from 'react';
import { dataListQueryPost, menuTreeQueryPost, queryRolePostDetail } from '../../../services';
import type { AddRolePostEntity } from '../../types/add.role.post.entity';
export default (props: CommonModelForm<string, AddRolePostEntity>) => {
  const intl = useIntl();
  const [form] = useForm();
  const [state, { toggle, setTrue, setFalse }] = useBoolean(false); //是否显示绑定一体系
  const [isReadOnly, setIsReadOnly] = useState(false);
  const [defaultTreeData, setDefaultTreeData] = useState<any>([]);
  const [expandedKeys, setExpandedKeys] = useState<any>([]);
  const [checkedKeys, setCheckedKeys] = useState<any>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const onExpand = (expandedKeysValue: any) => {
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };
  const onCheck = (checkedKeysValue: any, { halfCheckedKeys }: any) => {
    setCheckedKeys({ checked: checkedKeysValue, halfChecked: halfCheckedKeys });
  };
  useAsyncEffect(async () => {
    if (props.visible) {
      const treeData = await menuTreeQueryPost({});
      setDefaultTreeData(treeData);
      if (props.recordId == '0') {
        form.resetFields();
        setIsReadOnly(false);
        setFalse();
      } else {
        const data = await queryRolePostDetail({ id: props.recordId });
        form.setFieldsValue(data);
        const { checkedMenuPermissions, halfCheckedMenuPermissions } = data;
        setCheckedKeys({
          checked: checkedMenuPermissions,
          halfChecked: halfCheckedMenuPermissions,
        });
      }
    } else {
      form.resetFields();
      setDefaultTreeData([]);
      setCheckedKeys([]);
    }
  }, [props.visible]);

  const rules = [{ required: !props.readOnly }];
  return (
    <DrawerForm<AddRolePostEntity>
      form={form}
      onFinish={async (value) => {
        const { checked = [], halfChecked = [] } = checkedKeys;
        const checkedArray = checked?.map((t: any) => {
          return { menuId: t, checkType: '0' };
        });
        const halfcheckedArray = halfChecked?.map((t: any) => {
          return { menuId: t, checkType: '1' };
        });
        return props.onOk!({ ...value, menuPermissions: [...checkedArray, ...halfcheckedArray] });
      }}
      drawerProps={{
        onClose: props.onCancel,
        styles: { body: { backgroundColor: '#F2F2F2' } },
        destroyOnClose: true,
        maskClosable: false,
      }}
      layout="horizontal"
      title={props.title}
      open={props.visible}
      width={1080}
      labelCol={{ span: 4 }}
    >
      <ProFormText name="id" disabled={props.readOnly} hidden={true} />
      <ProCard title={<SubTitle text={intl.formatMessage({ id: 'system.role.list.form.basicInfo' })} />}>
        <ProFormText
          label={intl.formatMessage({ id: 'system.role.list.form.roleName' })}
          rules={rules}
          name="name"
          width={'xl'}
          disabled={props.readOnly}
          placeholder={intl.formatMessage({ id: 'common.placeholder.input' })}
        />
        <ProFormTextArea width={'xl'} label={intl.formatMessage({ id: 'system.role.list.form.roleDescription' })} name="remark" />
      </ProCard>
      <ProCard title={<SubTitle text={intl.formatMessage({ id: 'system.role.list.form.functionPermissions' })} />} className="mt-4">
        <Tree
          checkable
          onExpand={onExpand}
          autoExpandParent={autoExpandParent}
          onCheck={onCheck}
          checkedKeys={checkedKeys}
          fieldNames={{ title: 'name', key: 'id' }}
          treeData={defaultTreeData}
        />
      </ProCard>
      <ProCard title={<SubTitle text={intl.formatMessage({ id: 'system.role.list.form.sensitiveData' })} />} className="mt-4">
        <ProFormCheckbox.Group
          name="dataPermissions"
          layout="horizontal"
          request={async () => {
            if (props.visible) {
              const data = await dataListQueryPost({});
              return data?.map(({ id, name }) => ({
                key: id,
                value: id,
                label: name,
              }));
            } else {
              return [];
            }
          }}
        />
      </ProCard>
    </DrawerForm>
  );
};
