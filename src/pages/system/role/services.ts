import { PageResponseDataType } from '@/types/PageResponseDataType';
import { ResponseDataType } from '@/types/ResponseDataType';
import { request } from '@/utils/request';
import { AddRolePostEntity } from './list/types/add.role.post.entity';
import { DataListEntity } from './list/types/data.list.entity';
import { MenuTreeEntity } from './list/types/menu.tree.entity';
import { QueryPostListRequest } from './list/types/query.post.list.request';
import { RolePostDetailEntity } from './list/types/role.post.detail.entity';
import { RolePostEntity } from './list/types/role.post.entity';

/**
 * 列表查询
 * @param params
 * @returns
 */
export const queryRolePostPage = async (params: Partial<QueryPostListRequest>) => {
  return request<PageResponseDataType<RolePostEntity>>(`/ipmsauthcenter/RoleFacade/pageQuery`, {
    data: params,
  });
};

/**
 * 详情查询
 * @param params
 * @returns
 */
export const queryRolePostDetail = async (params: { id?: string }) => {
  return request<RolePostDetailEntity>(`/ipmsauthcenter/RoleFacade/detail`, {
    data: params,
  });
};

/**
 * 新增
 * @param params
 * @returns
 */
export const createRolePost = async (params: Partial<AddRolePostEntity>) => {
  return request<ResponseDataType<boolean>>(`/ipmsauthcenter/RoleFacade/create`, {
    origin: true,
    data: params,
  });
};
/**
 * 编辑
 * @param params
 * @returns
 */
export const modifyRolePost = async (params: Partial<AddRolePostEntity>) => {
  return request<ResponseDataType<boolean>>(`/ipmsauthcenter/RoleFacade/modify`, {
    origin: true,
    data: params,
  });
};

/**
 * 修改状态
 * @param params
 * @returns
 */
export const modifyStatusRolePost = async (params: Partial<RolePostEntity>) => {
  return request<boolean>(`/ipmsauthcenter/RoleFacade/modifyStatus`, {
    data: params,
  });
};

/**
 * 查询菜单树数据列表
 * @param params
 * @returns
 */
export const menuTreeQueryPost = async (params: {}) => {
  return request<MenuTreeEntity[]>(`/ipmsauthcenter/ResourceFacade/menuTreeQuery`, {
    data: params,
  });
};
/**
 * 查询敏感数据列表
 * @param params
 * @returns
 */
export const dataListQueryPost = async (params: {}) => {
  return request<DataListEntity[]>(`/ipmsauthcenter/ResourceFacade/dataListQuery`, {
    data: params,
  });
};

/**
 * 查询有权限的菜单树
 * @param params
 */
export const fetchMenuDataPost = async (params: {}) => {
  return request<MenuTreeEntity[]>(`/ipmsauthcenter/PermissionFacade/menuTreeQuery`, {
    data: params,
  });
};
/**
 * 菜单按钮权限列表查询
 * @param params
 * 类型：1菜单，2按钮
 * @returns
 */
export const menuListQueryPost = async (params: { type?: string }) => {
  return request<string[]>(`/ipmsauthcenter/PermissionFacade/menuListQuery`, {
    data: params,
  });
};

/**
 * 零售商 角色列表
 * @param params
 * @returns
 */
export const queryRolListPost = async (params: {}) => {
  return request<RolePostEntity[]>(`/ipmsauthcenter/RoleFacade/listQuery`, {
    data: params,
  });
};
