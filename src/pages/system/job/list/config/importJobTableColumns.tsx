import type { ProColumns } from '@ant-design/pro-components';
import { type PostEntity } from '../types/post.entity';

export const getImportJobTableColumns = (intl: any) => [
  {
    title: intl.formatMessage({ id: 'common.column.index' }),
    valueType: 'index',
    width: 40,
  },
  {
    title: intl.formatMessage({ id: 'system.job.list.importTime' }),
    dataIndex: 'createTime',
    search: true,
    valueType: 'dateRange',
    hideInTable: true,
  },

  {
    title: intl.formatMessage({ id: 'system.job.list.functionModule' }),
    dataIndex: 'moduleId',
    search: true,
    valueType: 'select',
  },
  {
    title: intl.formatMessage({ id: 'system.job.list.taskDescription' }),
    dataIndex: 'taskDesc',
    search: false,
  },

  {
    title: intl.formatMessage({ id: 'system.job.list.readRecordCount' }),
    dataIndex: 'totalAmount',
    search: false,
  },

  {
    title: intl.formatMessage({ id: 'system.job.list.importSuccessCount' }),
    dataIndex: 'successAmount',
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'system.job.list.importFailCount' }),
    dataIndex: 'failAmount',
    search: false,
  },

  {
    title: intl.formatMessage({ id: 'system.job.list.operationTime' }),
    dataIndex: 'startTime',
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'system.job.list.completionTime' }),
    dataIndex: 'endTime',
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'system.job.list.operator' }),
    dataIndex: 'createPerson',
    search: false,
  },
] as ProColumns<PostEntity>[];
