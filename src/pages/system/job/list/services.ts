import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { type PostEntity } from './types/post.entity';
import { type QueryPostListRequest } from './types/query.post.list.request';

export const queryJobPage = (params: QueryPostListRequest & PageRequestParamsType, taskType) => {
  return request<PageResponseDataType<PostEntity>>(
    `/ipmsgie/SaasImportExportTaskFacade/queryTaskByPage`,
    {
      data: {
        taskType,
        moduleId: params.moduleId,
        systemId: 'ETC_SAAS_SYS',
        startTime: params.createTime?.[0],
        endTime: params.createTime?.[1],
      },
    },
  ).then((pageResponse) => {
    return {
      ...pageResponse,
      data: pageResponse.data?.map((record) => ({
        taskDesc: record.taskInfo?.taskDesc,
        moduleId: record.moduleInfo?.moduleName,
        totalAmount: record.taskResultInfo?.totalAmount,
        successAmount: record.taskResultInfo?.successAmount,
        failAmount: record.taskResultInfo?.failAmount,
        startTime: record.taskInfo?.startTime,
        endTime: record.taskInfo?.endTime,
        createPerson: record.taskInfo?.createPerson,
        fileUrl: record.taskResultInfo?.info.startsWith('https') ? record.taskResultInfo?.info : '',
      })),
    };
  });
};

export const querySystemModule = () => {
  return request<PageResponseDataType<PostEntity>>(`/ipmsgie/TaskConfigFacade/querySystemModule`, {
    data: { systemId: 'ETC_SAAS_SYS' },
  });
};
