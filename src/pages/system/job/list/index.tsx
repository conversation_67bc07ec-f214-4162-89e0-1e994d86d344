import FunProTable from '@/components/common/FunProTable';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { useSearchParams } from '@@/exports';
import type { ActionType } from '@ant-design/pro-components';
import { PageContainer, ProCard, type ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { TabsProps } from 'antd';
import { Button, Tabs } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import { getExportJobTableColumns } from './config/exportJobTableColumns';
import { getImportJobTableColumns } from './config/importJobTableColumns';
import { queryJobPage, querySystemModule } from './services';
import { type PostEntity } from './types/post.entity';

/**
 * 下载结果文件
 * @param record PostEntity
 */
const onResultFileDownload = (record: PostEntity) => {
  window.open(record.fileUrl);
};

const JobList = () => {
  const intl = useIntl();
  const importJobTableColumns = getImportJobTableColumns(intl);
  const exportJobTableColumns = getExportJobTableColumns(intl);
  const [columns, setColumns] = useState<ProColumns<PostEntity>[]>(importJobTableColumns);
  // 动态设置
  const [tabActiveKey, setTabActiveKey] = useState<string>('Import');
  const actionRef = useRef<ActionType>();

  const operatorColumn: ProColumns<PostEntity> = {
    title: intl.formatMessage({ id: 'system.job.list.operation' }),
    valueType: 'option',
    render: (_, record: any) => {
      if (record.fileUrl !== '') {
        return (
          <Button type="link" key="download" onClick={() => onResultFileDownload(record)}>
            {intl.formatMessage({ id: 'system.job.list.downloadResultFile' })}
          </Button>
        );
      }
      return null;
    },
  };

  let updatedExportTableColumns;
  let updatedImportTableColumns;
  const [updatedImportColumns, setUpdatedImportColumns] = useState<ProColumns<PostEntity>[]>();
  const [updatedExportColumns, setUpdatedExportColumns] = useState<ProColumns<PostEntity>[]>();

  const [searchParams] = useSearchParams();
  const jobType = searchParams.get('jobType') as 'Import' | 'Export';

  useEffect(() => {
    if (jobType) {
      setTabActiveKey(jobType);
    }
  }, [jobType]);

  useActivate(() => {
    actionRef.current?.reload();
  });

  // 页面加载时获取数据
  const fetchSysModuleOptionsInitialData = async () => {
    if (updatedImportColumns && updatedExportColumns) {
      updatedImportTableColumns = updatedImportColumns;
      updatedExportTableColumns = updatedExportColumns;
      return;
    }
    const response = await querySystemModule();
    if (response) {
      const formattedOptions = response.map((item) => ({
        value: item.moduleId,
        label: item.moduleName,
        taskType: item.taskType,
      }));
      // 导入/导出分组
      const taskTypeGroup = formattedOptions.reduce((group, currentItem) => {
        if (!group[currentItem.taskType]) {
          group[currentItem.taskType] = [];
        }
        group[currentItem.taskType].push(currentItem);
        return group;
      }, {});

      updatedImportTableColumns = importJobTableColumns.map((column) => {
        if (column.valueType === 'select') {
          const options = taskTypeGroup[1].map((item) => ({
            label: item.label,
            value: item.value,
          }));
          column.fieldProps = { options };
          return column;
        }
        return column;
      });
      setUpdatedImportColumns(updatedImportTableColumns);

      updatedExportTableColumns = exportJobTableColumns.map((column) => {
        if (column.valueType === 'select') {
          const options = taskTypeGroup[2].map((item) => ({
            label: item.label,
            value: item.value,
          }));
          column.fieldProps = { options };
          return column;
        }
        return column;
      });
      setUpdatedExportColumns(updatedExportTableColumns);
    } else {
      console.error('Invalid response or missing data');
    }
  };

  useEffect(() => {
    // 页面加载时获取数据
    fetchSysModuleOptionsInitialData().then(() => {
      if (tabActiveKey == 'Import') {
        setColumns(() => [...updatedImportTableColumns, operatorColumn]);
      } else {
        setColumns(() => [...updatedExportTableColumns, operatorColumn]);
      }
    });
  }, [tabActiveKey]);

  const items: TabsProps['items'] = [
    {
      key: 'Import',
      label: intl.formatMessage({ id: 'system.job.list.importQuery' }),
    },
    {
      label: intl.formatMessage({ id: 'system.job.list.exportQuery' }),
      key: 'Export',
    },
  ];
  const onChange = (key: string) => {
    actionRef.current?.reload(true);
    setTabActiveKey(key);
  };

  return (
    <PageContainer>
      <ProCard bodyStyle={{ paddingLeft: 24, paddingTop: 0, paddingBottom: 0 }}>
        <Tabs
          activeKey={tabActiveKey}
          tabBarStyle={{ marginBottom: 0 }}
          items={items}
          onChange={onChange}
        />
      </ProCard>
      <FunProTable<PostEntity, any>
        editable={{
          type: 'single',
          actionRender: (row, config, defaultDom) => {
            return [defaultDom.save, defaultDom.cancel];
          },
        }}
        options={false}
        actionRef={actionRef}
        requestPage={(params) => queryJobPage(params, tabActiveKey == 'Import' ? 1 : 2)}
        scroll={{ x: 'max-content' }}
        columns={columns}
      />
    </PageContainer>
  );
};

export default withKeepAlive(JobList);