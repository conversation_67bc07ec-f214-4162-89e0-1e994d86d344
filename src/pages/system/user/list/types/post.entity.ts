export interface PostEntity {
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 一体系账户ID
   */
  etcAccountId?: string;
  /**
   * 一体系零售商ID
   */
  etcMemberId?: string;

  /**
   * 一体系绑定状态：0未绑定，1已绑定
   */
  etcBindStatus?: string;
  /**
   * 账户ID
   */
  id?: string;
  /**
   * 零售商ID
   */
  memberId?: string;
  /**
   * 手机号
   */
  phone?: string;
  /**
   * 角色ID列表
   */
  roleIds?: string[];

  /**
   * 角色列表
   */
  roleList?: string;

  /**
   * 门店列表
   */
  storeList?: string;
  /**
   * 状态：0启用，1禁用，-1注销
   */
  status?: string;
  /**
   * 门店ID列表
   */
  storeIds?: string[];
  /**
   * 类型：0主账户，1子账户
   */
  type?: string;
  /**
   * 账号
   */
  username?: string;
  /**
   * 备注
   */
  remark?: string;
}
