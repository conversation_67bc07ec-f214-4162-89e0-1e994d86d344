import { queryRolListPost } from '@/pages/system/role/services';
import {
  allStoreSimpleQuery,
  queryPostDetail,
} from '@/pages/system/user/services';
import { CommonModelForm } from '@/types/CommonModelForm';
import { ModalForm, ProFormSelect, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Alert, Button } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { useState } from 'react';
import { PostEntity } from '../../types/post.entity';
export default (props: CommonModelForm<string, PostEntity>) => {
  const intl = useIntl();
  const [form] = useForm();
  const [isReadOnly, setIsReadOnly] = useState(false);
  useAsyncEffect(async () => {
    if (props.recordId == '0') {
      form.resetFields();
      setIsReadOnly(false);
    } else {
      const data = await queryPostDetail({ id: props.recordId });
      form.setFieldsValue(data);
    }
  }, [props.visible]);


  const submitter = {
    render: () => (
      <Button key="cancel" size="middle" onClick={props.onCancel}>
        {intl.formatMessage({ id: 'common.button.cancel' })}
      </Button>
    ),
  };

  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };
  const rules = [{ required: !props.readOnly }];
  return (
    <ModalForm
      {...layout}
      form={form}
      layout="horizontal"
      title={props.title}
      open={props.visible}
      width="40%"
      modalProps={{
        centered: true,
        onCancel: props.onCancel,
        destroyOnClose: true,
      }}
      submitter={props.readOnly ? submitter : {}}
      onFinish={props.onOk}
    >
      <ProFormText name="id" disabled={props.readOnly} hidden={true} />
      <ProFormText name="etcAccountId" hidden={true} />
      {props.recordId == '0' && (
        <Alert
          banner
          showIcon={false}
          className="my-4 px-7 text-sm -mx-[24px]"
          message={intl.formatMessage({ id: 'system.user.list.createAccountTip' })}
          type="error"
        />
      )}
      <ProFormText
        rules={rules}
        name="name"
        disabled={props.readOnly}
        label={intl.formatMessage({ id: 'system.user.list.form.employeeName' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.input' })}
      />
      <ProFormText
        rules={[
          {
            required: !props.readOnly,
            pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
            message: intl.formatMessage({ id: 'system.user.list.form.phoneValidation' }),
          },
        ]}
        name="phone"
        disabled={props.readOnly || isReadOnly}
        label={intl.formatMessage({ id: 'system.user.list.form.phoneNumber' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.input' })}
      />
      <ProFormSelect
        rules={rules}
        name="storeIds"
        disabled={props.readOnly || isReadOnly}
        label={intl.formatMessage({ id: 'system.user.list.form.storePermission' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.select' })}
        mode="multiple"
        fieldProps={{ fieldNames: { label: 'name', value: 'id' } }}
        request={allStoreSimpleQuery}
      />
      <ProFormSelect
        rules={rules}
        name="roleIds"
        disabled={props.readOnly}
        label={intl.formatMessage({ id: 'system.user.list.form.employeeRole' })}
        mode="multiple"
        fieldProps={{
          fieldNames: { label: 'name', value: 'id' },
        }}
        request={queryRolListPost}
        placeholder={intl.formatMessage({ id: 'common.placeholder.select' })}
      />
      <ProFormTextArea
        name="remark"
        disabled={props.readOnly}
        label={intl.formatMessage({ id: 'common.label.remark' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.inputWithMaxLength' }, { maxLength: 100 })}
        fieldProps={{ count: { max: 100, show: true }, maxLength: 100 }}
        rules={[{ max: 100 }]}
      />
    </ModalForm>
  );
};
