import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { type CommonModelForm } from '@/types/CommonModelForm';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { Space } from 'antd';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import { createPost, modifyPost, modifyStatusPost, queryPostList } from '../services';
import FormModal from './components/modal';
import { PostListTableColumns } from './config/postListTableColumns';
import { type PostEntity } from './types/post.entity';

const UserList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();

  const [userModalProps, setUserModalProps] = useState<CommonModelForm<string, PostEntity>>({
    visible: false,
    recordId: '0',
    readOnly: false,
    title: '',
  });


  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 禁用事件
   * @param ids
   */
  const handleDeleteItem = async (id: string, status: string) => {
    await modifyStatusPost({ id, status });
    actionRef.current?.reload(true);
  };

  const handleUpdateItem = async (id: string) => {
    setUserModalProps({
      visible: true,
      recordId: id,
      readOnly: false,
      title: intl.formatMessage({ id: 'system.user.list.editEmployee' }),
    });
  };

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setUserModalProps({
      visible: false,
      recordId: '0',
      readOnly: false,
      title: '',
    });
  };

  /**
   * 新增或编辑
   * @param values
   */
  const handleSaveOrUpdate = async (values: PostEntity) => {
    try {
      const { id } = values;
      let result;
      if (id) {
        //编辑
        result = await modifyPost({ ...values });
      } else {
        //新增
        result = await createPost({ ...values });
      }
      const { code } = result;
      if (code == 0) {
        hideModal();
        actionRef.current?.reload(true);
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  return (
    <PageContainer>
      <FunProTable<PostEntity, any>
        rowKey="id"
        requestPage={queryPostList}
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        columns={PostListTableColumns({
          handleUpdateItem,
          handleDeleteItem,
          intl,
        })}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              key="primary"
              authority="addUser"
              onClick={() =>
                setUserModalProps({
                  visible: true,
                  recordId: '0',
                  readOnly: false,
                  title: intl.formatMessage({ id: 'system.user.list.addEmployee' }),
                })
              }
            >
              {intl.formatMessage({ id: 'common.button.add' })}
            </AuthButton>
          </Space>
        }
      />
      <FormModal {...userModalProps} onOk={handleSaveOrUpdate} onCancel={hideModal} />
    </PageContainer>
  );
};
export default withKeepAlive(UserList);
