import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { type PropertyModalFromType } from '@/types/PropertyModalFromType';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer, ProCard, type ProColumns } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import type { TabsProps } from 'antd';
import { Space, Tabs } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import CustomerPropertyFormModal from './components/CustomerPropertyFormModal';
import { getCustomerTagColumns } from './config/CustomerTagColumns';
import { addTag, editTag, queryCustomerPropertyPage } from './services';
import type { CustomerPropertyTagEntity } from './types/CustomerPropertyTagEntity';

const inputFieldNameMap: Record<string, string> = {
  customerTag: 'tagName',
};
const CustomerProperty = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();

  const getInputFieldLabel = (key: string) => {
    if (key === 'customerTag') {
      return intl.formatMessage({ id: 'customer.customerProperty.label.customerTag' });
    }
    return '';
  };

  const [columns, setColumns] =
    useState<ProColumns<CustomerPropertyTagEntity>[]>(getCustomerTagColumns(intl));
  // 动态设置
  const [tabActiveKey, setTabActiveKey] = useState<string>('customerTag');

  useActivate(() => {
    actionRef.current?.reload();
  });

  useEffect(() => {
    const operatorColumn: ProColumns<CustomerPropertyTagEntity> = {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      valueType: 'option',
      render: (_text, record, _, action) => {
        const { source } = record;
        if (source == 0) {
          return null;
        }
        return (
          <AuthButton
            isHref
            authority="editCustomerTag"
            key="create"
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          >
            {intl.formatMessage({ id: 'common.button.edit' })}
          </AuthButton>
        );
      },
    };
    let newColumns: ProColumns<CustomerPropertyTagEntity>[] = [];
    if (tabActiveKey == 'customerTag') {
      newColumns = getCustomerTagColumns(intl);
    }
    setColumns(() => [...newColumns, operatorColumn]);
  }, [tabActiveKey, intl]);

  const [createModalProps, setCreateModalProps] = useState<PropertyModalFromType<number>>({
    inputFieldName: inputFieldNameMap[tabActiveKey],
    inputFieldLabel: getInputFieldLabel(tabActiveKey),
    visible: false,
    recordId: 0,
    readOnly: false,
    title: intl.formatMessage({ id: 'customer.customerProperty.createModal.titleAddTag' }),
  });
  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setCreateModalProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      recordId: 0,
      readOnly: false,
    }));
  };

  /**
   * 新增或编辑
   * @param values
   */
  const handleSaveOrUpdate = async (values: any) => {
    try {
      const result = await addTag({ ...values, tagType: 1 });
      if (result) {
        hideModal();
        actionRef.current?.reset?.();
        actionRef.current?.reload(true);
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  const items: TabsProps['items'] = [
    {
      key: 'customerTag',
      label: intl.formatMessage({ id: 'customer.customerProperty.tab.customerTag' }),
    },
  ];
  const onChange = (key: string) => {
    actionRef.current?.reload(true);
    setTabActiveKey(key);
  };
  // 编辑-保存
  const onEditSave = async (_key: React.Key | React.Key[], record: CustomerPropertyTagEntity) => {
    const result = await editTag(record);
    if (!result) {
      return Promise.reject();
    }
    return true;
  };
  return (
    <PageContainer>
      <ProCard bodyStyle={{ paddingLeft: 24, paddingTop: 0, paddingBottom: 0 }}>
        <Tabs
          defaultActiveKey="customerTag"
          tabBarStyle={{ marginBottom: 0 }}
          items={items}
          onChange={onChange}
        />
      </ProCard>
      <FunProTable<CustomerPropertyTagEntity, any>
        scroll={{ x: 800 }}
        editable={{
          type: 'single',
          onSave: onEditSave,
          actionRender: (_row, _config, defaultDom) => {
            return [defaultDom.save, defaultDom.cancel];
          },
        }}
        rowKey="id"
        requestPage={(params) => queryCustomerPropertyPage({ ...params, tagType: 1 })}
        actionRef={actionRef}
        columns={columns}
        headerTitle={
          <Space>
            <AuthButton
              authority="addCustomerTag"
              type="primary"
              key="create"
              onClick={() => {
                setCreateModalProps((preModalProps) => ({
                  ...preModalProps,
                  inputFieldName: inputFieldNameMap[tabActiveKey],
                  inputFieldLabel: getInputFieldLabel(tabActiveKey),
                  visible: true,
                  readOnly: false,
                  title: intl.formatMessage({ id: 'customer.customerProperty.createModal.titleAddTagSuffix' }),
                }));
              }}
            >
              {intl.formatMessage({ id: 'customer.customerProperty.button.addTag' })}
            </AuthButton>
          </Space>
        }
      />
      <CustomerPropertyFormModal
        {...createModalProps}
        onCancel={hideModal}
        onOk={handleSaveOrUpdate}
      />
    </PageContainer>
  );
};

export default withKeepAlive(CustomerProperty);