import { CommonStatusValueEnum } from '@/types/CommonStatus';
import { type ProColumns } from '@ant-design/pro-components';

export const getCustomerPriceLevelColumns = (intl: any): ProColumns<any>[] => [
  {
    title: intl.formatMessage({ id: 'common.column.index' }),
    valueType: 'index',
    width: 40,
  },

  {
    title: intl.formatMessage({ id: 'customerProperty.priceLevel.column.name' }),
    dataIndex: 'categoryName',
    search: true,
    width: 100,
    editable: () => true,
  },
  {
    title: intl.formatMessage({ id: 'customerProperty.priceLevel.column.source' }),
    dataIndex: 'nature',
    search: false,
    width: 100,
  },
  {
    title: intl.formatMessage({ id: 'common.column.status' }),
    dataIndex: 'categoryStatus',
    search: false,
    width: 80,
    valueType: 'select',
    valueEnum: CommonStatusValueEnum,
  },
];

export default getCustomerPriceLevelColumns;
