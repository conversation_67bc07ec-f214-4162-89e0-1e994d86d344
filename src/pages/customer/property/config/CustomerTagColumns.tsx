import { ReverseCommonStatusValueEnum } from '@/types/CommonStatus';
import { REG_LENGTH_RULE, REQUIRED_RULES } from '@/utils/RuleUtils';
import { type ProColumns } from '@ant-design/pro-components';
import type { CustomerPropertyTagEntity } from '../types/CustomerPropertyTagEntity';

export const getCustomerTagColumns = (intl: any): ProColumns<CustomerPropertyTagEntity>[] => [
  {
    title: intl.formatMessage({ id: 'common.column.index' }),
    valueType: 'index',
    editable: false,
    width: 40,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerProperty.tagTable.search.tagName' }),
    dataIndex: 'tagNameWord',
    search: true,
    hideInTable: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerProperty.tagTable.column.tagName' }),
    dataIndex: 'tagName',
    search: false,
    valueType: 'text',
    ellipsis: true,
    formItemProps: {
      rules: [REQUIRED_RULES, REG_LENGTH_RULE],
    },
  },
  {
    title: intl.formatMessage({ id: 'customer.customerProperty.tagTable.column.source' }),
    dataIndex: 'sourceName',
    search: false,
    // editable: false,
  },
  {
    title: intl.formatMessage({ id: 'common.column.status' }),
    dataIndex: 'tagStatus',
    search: false,
    valueType: 'select',
    valueEnum: ReverseCommonStatusValueEnum,
    fieldProps: {
      allowClear: false,
    },
  },
];

export default getCustomerTagColumns;
