import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { CustomerPropertyTagEntity } from './types/CustomerPropertyTagEntity';
/**
 * 用户管理-分页查询客户标签
 *
 * @param params
 * @returns
 */
export const queryCustomerPropertyPage = async (params: CustomerPropertyTagEntity) => {
  const result = await request<PageResponseDataType<CustomerPropertyTagEntity>>(
    `/ipmscst/CstTagManageFacade/getTagPaged`,
    {
      data: params,
    },
  );

  if (result) {
    return {
      ...result,
      data: result.data.map((t) => ({ ...t, tagStatus: `${t.tagStatus}` })),
    };
  }
  return { data: [] };
};
/**
 * 用户管理-新增属性标签
 *
 * @param params
 * @returns
 */
export const addTag = async (params: CustomerPropertyTagEntity) => {
  return request<boolean>(`/ipmscst/CstTagManageFacade/addTag`, {
    data: params,
  });
};

/**
 * 用户管理-修改属性标签
 *
 * @param params
 * @returns
 */
export const editTag = async (params: CustomerPropertyTagEntity) => {
  return request<boolean>(`/ipmscst/CstTagManageFacade/editTag`, {
    data: params,
  });
};
