import { type PropertyModalFromType } from '@/types/PropertyModalFromType';
import { REG_LENGTH_RULE, REQUIRED_RULES, REQUIRED_RULES_NOT_ALLOW_EMPTY } from '@/utils/RuleUtils';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { useForm } from 'antd/lib/form/Form';
export default ({
  title,
  recordId,
  visible,
  onCancel,
  onOk,
  readOnly,
  inputFieldName,
  inputFieldLabel,
}: PropertyModalFromType<number>) => {
  const intl = useIntl();
  const [form] = useForm();
  useAsyncEffect(async () => {
    if (recordId == 0) {
      form.resetFields();
    } else {
      //const { data } = await get({ id: recordId });
      //form.setFieldsValue(data);
    }
  }, [visible]);

  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };
  return (
    <ModalForm
      {...layout}
      form={form}
      layout="horizontal"
      title={title}
      open={visible}
      width="40%"
      modalProps={{
        maskClosable: false,
        centered: true,
        onCancel: onCancel,
      }}
      submitTimeout={2000}
      onFinish={onOk}
    >
      <ProFormText
        rules={[REQUIRED_RULES, REQUIRED_RULES_NOT_ALLOW_EMPTY, REG_LENGTH_RULE]}
        name={inputFieldName}
        label={inputFieldLabel}
        placeholder={intl.formatMessage({ id: 'common.placeholder.input' })}
      />
      <ProFormText name="id" hidden={true} />
    </ModalForm>
  );
};
