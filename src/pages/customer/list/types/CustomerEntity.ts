import type { YesOrNoType } from '@/types/CommonStatus';
import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import type { CustomerAddressEntity } from './CustomerAddressEntity';
import type { CustomerContactEntity } from './CustomerContactEntity';
import type { CustomerTagEntity } from './CustomerTagEntity';

export interface CustomerEntity extends PageRequestParamsType {
  /**是否允许挂账 */
  credit: boolean;
  /** 客户编码名称简称支持模糊查找 */
  cstWord: string;
  /** 客户ID */
  cstId: string;
  /** 客户编码 */
  cstSn: string;
  /** 客户名称 */
  cstName: string;
  /** 客户简称 */
  nickName: string;
  /** 归属门店ID */
  storeId: string;
  /** 归属门店名称 */
  storeName: string;
  /** 业务员ID */
  salesmanId: string;
  /** 业务员名 */
  salesmanName: string;
  /** 客户状态: 0=启用 1=禁用 */
  cstStatus: YesOrNoType;
  /** 客户状态名称 */
  cstStatusName: string;
  /** 联系人一个 */
  contacts: CustomerContactEntity[];
  /** 客户标签 */
  tags: CustomerTagEntity[];
  /** 客户地址一个 */
  addresses: CustomerAddressEntity[];
  /** 客户创建时间 */
  createTime: string;
  /** 客户备注 */
  remark: string;
  /**可用额度 */
  availableAmount?: number;
  usedAmount?: number;
  /**剩余账期 */
  remainTerms?: number;
}
