export interface Contact {
  /** QQ号码 */
  qq: string;
  /** 邮箱地址 */
  email: string;
  /** 微信号 */
  wechat: string;
  /** 1:默认联系人 0:非默认联系人 */
  isDefault: number;
  /** 联系人电话 */
  phone: string;
  /** 职务 */
  position: string;
  /** 客户备注 */
  remark: string;
  /** 联系人姓名 */
  name: string;
  /** 编辑传入id */
  id?: string;
}

export interface Tag {
  /** 标签ID */
  tagId: string;
  /** 标签名称 */
  tagName?: string;
  /** 主键 */
  id?: string;
}

export interface Address {
  /** 省编码 */
  provinceCode: string;
  /** 省名称 */
  provinceName?: string;
  /** 市编码 */
  cityCode: string;
  /** 市名称 */
  cityName?: string;
  /** 区县编码 */
  prefectureCode: string;
  /** 区县名称 */
  prefectureName?: string;
  /** 1:默认地址 0:非默认地址 */
  isDefault: number;
  /** 联系人电话 */
  phone: string;
  /** 详细地址 */
  address: string;
  /** 联系人姓名 */
  name: string;
  /** 编辑传入id */
  id?: string;
  addressCode?: string[];
}

export interface Image {
  /** 图片地址 */
  url: string;
  /** 编辑传入id */
  id?: string;
  uid?: string;
}

export interface Settle {
  /** 期初应收 */
  orgReceivableAmount?: string;
  /** 信用额度 */
  totalAmount: number;
  /** 信用账期（天） */
  creditTerms: number;
  /** 可用额度 */
  availableAmount: number;
  /** 冻结额度 */
  freezeAmount: number;
  usedAmount: number;
  remainTerms?: number;
  /** 是否已被首次使用 */
  isFirstUse: number;
  /** 账期起始日期 */
  creditDate: Date;
  /** 客户ID */
  cstId: string;
  /** 是否可用 0=启用 1=逾期 2=停用 */
  status: number;
  /** ID */
  id: string;
  /** 是否挂账 */
  credit: boolean;
  /** 状态名 */
  statusName: string;
}

export interface Billing {
  /** 开票单位 */
  billingUnit: string;
  /** 纳税识别号 */
  taxNo: string;
  /** 开户行名称 */
  bankName: string;
  /** 开户行账号 */
  accountNo: string;
  /** 电话 */
  phone: string;
  /** 地址 */
  address: string;
  /** 编辑传入id */
  id: string;
}
export interface Base {
  /** 客户ID */
  id: string;
  /** 客户编码 */
  cstSn: string;
  /** 客户名称 */
  cstName: string;
  /** 客户简称 */
  nickName: string;
  /** 归属门店ID */
  storeId: string;
  /** 归属门店名称 */
  storeName: string;
  /** 业务员ID */
  salesmanId: string;
  /** 业务员名 */
  salesmanName: string;
  /** 客户状态: 0=启用 1=禁用 */
  cstStatus: number;
  /** 客户备注 */
  remark: string;
  createTime: string;
}

export interface CustomerSaveEntity {
  base: Base;
  /** 联系人列表 */
  contacts?: Contact[];
  /** 客户标签列表 */
  tags?: Tag[];
  tagIdList?: string[];
  /** 客户地址列表 */
  addresses?: Address[];
  /** 客户图片列表 */
  images?: Image[];
  /** 挂账信息 */
  settle?: Settle;
}
