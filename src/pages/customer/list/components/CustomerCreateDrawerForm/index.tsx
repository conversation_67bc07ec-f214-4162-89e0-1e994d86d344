import FunProFormUploadButton from '@/components/FunProFormUploadButton';
import LeftTitle from '@/components/LeftTitle';
import { queryDistrictAreaTree } from '@/pages/system/store/services';
import { queryAccountSelectByStoreId, queryStoreByAccount } from '@/pages/system/user/services';
import { requiredProps } from '@/types/validateRules';
import { MAX_AMOUNT, MAX_COUNT } from '@/utils/Constants';
import {
  REG_LENGTH_REMARK_RULE,
  REG_LENGTH_RULE,
  REG_ONLY_ALPHA_AND_DIGIT_RULE,
  REQUIRED_RULES,
} from '@/utils/RuleUtils';
import type { EditableFormInstance } from '@ant-design/pro-components';
import {
  DrawerForm,
  EditableProTable,
  ProFormDependency,
  ProFormDigit,
  ProFormGroup,
  ProFormMoney,
  ProFormSwitch,
} from '@ant-design/pro-components';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { useIntl, useModel } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import type { FormInstance, UploadFile } from 'antd';
import { ConfigProvider, Flex, Radio } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { defaultTo, find, isEmpty, uniq, uniqueId } from 'lodash';
import { useRef, useState } from 'react';
import CustomerAddressTableColumns from '../../config/CustomerAddressTableColumns';
import CustomerContactTableColumns from '../../config/CustomerContactTableColumns';
import { getCstDetail, getTagList } from '../../services';
import type { CustomerAddressEntity } from '../../types/CustomerAddressEntity';
import type { CustomerContactEntity } from '../../types/CustomerContactEntity';
import { type CustomerCreateDrawerFormType } from '../../types/CustomerCreateDrawerFormType';
import type { CustomerSaveEntity, Settle } from '../../types/CustomerSaveEntity';
const labelCol = { span: 6 };
const colProps = { span: 8 };
/**
 * 基本信息
 * @param props CommonModelForm
 * @returns
 */
const renderBaseInfoFormItems = (form: FormInstance, recordId: string, intl: ReturnType<typeof useIntl>) => { // 接收 intl
  return (
    <ProFormGroup
      style={{
        backgroundColor: 'white',
        padding: 24,
        marginTop: 16,
        borderRadius: 8,
      }}
      title={<LeftTitle title={intl.formatMessage({ id: 'customer.customerList.createForm.group.baseInfo' })} />}
    >
      <ProFormText name={['base', 'id']} hidden />
      <ProFormText
        rules={[REQUIRED_RULES, REG_LENGTH_RULE]}
        name={['base', 'cstName']}
        labelCol={labelCol}
        colProps={colProps}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.customerName' })}
      />
      <ProFormText
        name={['base', 'cstSn']}
        labelCol={labelCol}
        colProps={colProps}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.customerSn' })}
        rules={[REG_ONLY_ALPHA_AND_DIGIT_RULE, { required: !isEmpty(recordId) }]}
      />
      <ProFormText
        labelCol={labelCol}
        colProps={colProps}
        name={['base', 'nickName']}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.nickName' })}
        rules={[REG_LENGTH_RULE]}
      />
      <ProFormSelect
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.customerTags' })}
        name="tagIdList"
        labelCol={labelCol}
        colProps={colProps}
        fieldProps={{
          mode: 'multiple',
          maxTagCount: 3,
          allowClear: true,
          fieldNames: { label: 'tagName', value: 'id' },
        }}
        request={() => getTagList({ tagStatus: 0, tagType: 1 })}
      />
      <ProFormSelect
        labelCol={labelCol}
        colProps={colProps}
        name={['base', 'storeId']}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.store' })}
        onChange={() => {
          form.setFieldValue(['base', 'salesmanId'], undefined);
        }}
        fieldProps={{
          showSearch: true,
          fieldNames: { label: 'name', value: 'id' },
        }}
        request={() => queryStoreByAccount({ status: 1 })}
      />

      <ProFormSelect
        name={['base', 'salesmanId']}
        labelCol={labelCol}
        colProps={colProps}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.salesman' })}
        placeholder={intl.formatMessage({ id: 'common.placeholder.select' })}
        dependencies={['base', 'storeId']}
        fieldProps={{
          showSearch: true,
          fieldNames: { label: 'name', value: 'id' },
        }}
        request={async (params) => {
          const {
            base: { storeId },
          } = params;
          if (isEmpty(storeId)) return [];
          return queryAccountSelectByStoreId({ storeId });
        }}
      />
      <ProFormText
        labelCol={{ span: 2 }}
        colProps={{ span: 24 }}
        name={['base', 'remark']}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.remark' })}
        rules={[REG_LENGTH_REMARK_RULE]}
      />
      <FunProFormUploadButton
        name="images"
        labelCol={labelCol}
        colProps={colProps}
        max={1}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.customerPhoto' })}
        transform={(value: UploadFile[]) => {
          if (!isEmpty(value)) {
            return {
              images: value.map((file) => {
                const f: { id?: string; url?: string } = { id: file.uid, url: file.url };
                if (file.uid.startsWith('rc-upload-')) {
                  delete f.id;
                }
                return f;
              }),
            };
          }
        }}
      />
    </ProFormGroup>
  );
};
/**
 * 结算信息
 * @param props CommonModelForm
 * @returns
 */
// 修改参数顺序：intl 移到 settleInfo 可选参数之前
const renderSettlementInfoFormItems = (form: FormInstance, intl: ReturnType<typeof useIntl>, settleInfo?: Settle) => {
  return (
    <ProFormGroup
      title={<LeftTitle title={intl.formatMessage({ id: 'customer.customerList.createForm.group.settlementInfo' })} />}
      style={{
        backgroundColor: 'white',
        padding: 24,
        marginTop: 16,
        borderRadius: 8,
      }}
    >
      <ProFormText name={['settle', 'id']} hidden />
      <ProFormMoney
        name={['settle', 'orgReceivableAmount']}
        labelCol={{ span: 6 }}
        colProps={{ span: 8 }}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.initialReceivable' })}
        fieldProps={{
          addonAfter: intl.formatMessage({ id: 'common.unit.yuan' }),
          precision: 2,
          min: 0,
          max: MAX_AMOUNT,
        }}
      />
      <ProFormSwitch
        labelCol={{ span: 2 }}
        colProps={{ span: 24 }}
        name={['settle', 'credit']}
        label={intl.formatMessage({ id: 'customer.customerList.createForm.label.allowCredit' })}
        fieldProps={{
          onChange: (value) => {
            if (!value) {
              form.resetFields([
                ['settle', 'totalAmount'],
                ['settle', 'creditTerms'],
              ]);
            }
          },
        }}
        disabled={settleInfo?.status == 2}
        addonAfter={
          settleInfo?.status == 2 && <span className="text-primary">{settleInfo?.statusName}</span>
        }
      />
      <ProFormDependency name={['settle', 'credit']}>
        {({ settle }) => {
          const credit = defaultTo(settle?.credit, false);
          return (
            <>
              <ProFormMoney
                name={['settle', 'totalAmount']}
                labelCol={labelCol}
                colProps={colProps}
                label={intl.formatMessage({ id: 'customer.customerList.createForm.label.creditLimit' })}
                tooltip={intl.formatMessage({ id: 'customer.customerList.createForm.tooltip.creditLimit' })}
                disabled={!credit}
                fieldProps={{
                  addonAfter: intl.formatMessage({ id: 'common.unit.yuan' }),
                  precision: 2,
                  min: 0,
                  max: MAX_AMOUNT,
                }}
              />

              <ProFormDigit
                label={intl.formatMessage({ id: 'customer.customerList.createForm.label.creditTerms' })}
                name={['settle', 'creditTerms']}
                labelCol={labelCol}
                colProps={colProps}
                tooltip={intl.formatMessage({ id: 'customer.customerList.createForm.tooltip.creditTerms' })}
                disabled={!credit}
                fieldProps={{
                  min: 1,
                  addonAfter: intl.formatMessage({ id: 'common.unit.day' }),
                  precision: 0,
                  max: MAX_COUNT,
                }}
              />
            </>
          );
        }}
      </ProFormDependency>
    </ProFormGroup>
  );
};
/**
 * 联系人信息
 */
const RenderContact = () => {
  const intl = useIntl();
  const editorFormRef = useRef<EditableFormInstance<CustomerContactEntity>>();
  return (
    <ProFormGroup
      title={<LeftTitle title={intl.formatMessage({ id: 'customer.customerList.createForm.group.contactInfo' })} />}
      style={{
        backgroundColor: 'white',
        padding: 24,
        marginTop: 16,
        borderRadius: 8,
      }}
    >
      <ConfigProvider
        theme={{
          components: {
            InputNumber: {
              controlWidth: 80,
            },
          },
        }}
      >
        <EditableProTable<CustomerContactEntity>
          className="mt-4"
          search={false}
          name="contacts"
          rowKey="id"
          editableFormRef={editorFormRef}
          scroll={{ x: 'max-content' }}
          recordCreatorProps={{
            record: (index) => ({ id: uniqueId('contact_'), isDefault: index == 0 ? 1 : 0 }),
            creatorButtonText: intl.formatMessage({ id: 'customer.customerList.createForm.button.addContact' }),
          }}
          pagination={false}
          editable={{
            type: 'multiple',
            actionRender: (_, __, defaultDom) => [defaultDom.delete],
          }}
          columns={[
            {
              title: intl.formatMessage({ id: 'common.column.index' }),
              valueType: 'index',
              editable: false,
              width: 40,
              fixed: 'left',
            },
            {
              title: intl.formatMessage({ id: 'customer.customerList.createForm.contactTable.column.isDefaultContact' }),
              align: 'center',
              width: 100,
              dataIndex: 'isDefault',
              fixed: 'left',
              render: (_, record) => {
                const rows = editorFormRef.current?.getRowsData?.();
                return (
                  <Radio
                    checked={record.isDefault === 1}
                    onChange={() => {
                      rows?.forEach((t) => {
                        const { id } = t;
                        editorFormRef.current?.setRowData?.(id, {
                          isDefault: id == record?.id ? 1 : 0,
                        });
                      });
                    }}
                  />
                );
              },
              renderFormItem: (_, { record }) => {
                const rows = editorFormRef.current?.getRowsData?.();
                if (rows) {
                  const checkRow = find(rows, (t) => t.isDefault === 1);
                  if (isEmpty(checkRow)) {
                    editorFormRef.current?.setRowData?.(0, {
                      isDefault: 1,
                    });
                  }
                }
                return (
                  <Radio
                    checked={record?.isDefault === 1}
                    onChange={() => {
                      rows?.forEach((t) => {
                        const { id } = t;
                        editorFormRef.current?.setRowData?.(id, {
                          isDefault: id == record?.id ? 1 : 0,
                        });
                      });
                    }}
                  />
                );
              },
            },
            {
              title: (
                <Flex align="center">
                  <span className="text-[#FF7621]">*</span>
                  <span>{intl.formatMessage({ id: 'customer.customerList.createForm.contactTable.column.contactName' })}</span>
                </Flex>
              ),
              formItemProps: () => {
                return {
                  rules: [requiredProps],
                };
              },
              dataIndex: 'name',
              search: false,
              width: 100,
              fixed: 'left',
            },
            ...CustomerContactTableColumns(intl),
            {
              title: intl.formatMessage({ id: 'common.column.operation' }),
              valueType: 'option',
              align: 'center',
              width: 100,
              fixed: 'right',
              render: (text, record, _, action) => (
                <a
                  key="address_editable"
                  onClick={() => {
                    action?.startEditable?.(record.id);
                  }}
                >
                  {intl.formatMessage({ id: 'common.button.edit' })}
                </a>
              ),
            },
          ]}
        />
      </ConfigProvider>
    </ProFormGroup>
  );
};
/**
 * 地址信息
 */
const RenderAddress = () => {
  const intl = useIntl();
  const { set } = useModel('dictModel');
  const editorFormRef = useRef<EditableFormInstance<CustomerAddressEntity>>();
  return (
    <ProFormGroup
      title={<LeftTitle title={intl.formatMessage({ id: 'customer.customerList.createForm.group.addressInfo' })} />}
      style={{
        backgroundColor: 'white',
        padding: 24,
        marginTop: 16,
        borderRadius: 8,
      }}
    >
      <EditableProTable<CustomerAddressEntity>
        className="mt-4"
        editableFormRef={editorFormRef}
        name="addresses"
        search={false}
        rowKey="id"
        columnEmptyText={false}
        scroll={{ x: 'max-content' }}
        recordCreatorProps={{
          record: (index) => ({ id: uniqueId('address_'), isDefault: index == 0 ? 1 : 0 }),
          creatorButtonText: intl.formatMessage({ id: 'customer.customerList.createForm.button.addAddress' }),
        }}
        editable={{
          type: 'multiple',
          actionRender: (_, __, defaultDom) => [defaultDom.delete],
        }}
        columns={[
          {
            title: intl.formatMessage({ id: 'common.column.index' }),
            valueType: 'index',
            fixed: 'left',
            editable: false,
            width: 40,
          },
          {
            title: intl.formatMessage({ id: 'customer.customerList.createForm.addressTable.column.isDefaultAddress' }),
            align: 'center',
            width: 100,
            dataIndex: 'isDefault',
            fixed: 'left',
            render: (_, record) => {
              const rows = editorFormRef.current?.getRowsData?.();
              return (
                <Radio
                  checked={record.isDefault === 1}
                  onChange={() => {
                    rows?.forEach((t) => {
                      const { id } = t;
                      editorFormRef.current?.setRowData?.(id, {
                        isDefault: id == record?.id ? 1 : 0,
                      });
                    });
                  }}
                />
              );
            },
            renderFormItem: (_, { record }) => {
              const rows = editorFormRef.current?.getRowsData?.();
              if (rows) {
                const checkRow = find(rows, (t) => t.isDefault === 1);
                if (isEmpty(checkRow)) {
                  editorFormRef.current?.setRowData?.(0, {
                    isDefault: 1,
                  });
                }
              }
              return (
                <Radio
                  checked={record?.isDefault === 1}
                  onChange={() => {
                    rows?.forEach((t) => {
                      const { id } = t;
                      editorFormRef.current?.setRowData?.(id, {
                        isDefault: id == record?.id ? 1 : 0,
                      });
                    });
                  }}
                />
              );
            },
          },
          {
            title: (
              <Flex align="center">
                <span className="text-[#FF7621]">*</span>
                <span>{intl.formatMessage({ id: 'customer.customerList.createForm.addressTable.column.provinceCityDistrict' })}</span>
              </Flex>
            ),
            dataIndex: 'addressCode',
            search: false,
            fixed: 'left',
            valueType: 'cascader',
            debounceTime: 100,
            formItemProps: () => {
              return {
                rules: [requiredProps],
              };
            },
            fieldProps: {
              onChange: (_value: any, options: any[]) => {
                if (!isEmpty(options)) {
                  options.forEach((option: any) => {
                    const { areaId, areaName } = option;
                    set(areaId, areaName);
                  });
                }
              },
              fieldNames: { label: 'areaName', value: 'areaId' },
            },
            request: queryDistrictAreaTree,
          },
          ...CustomerAddressTableColumns(intl),
          {
            title: intl.formatMessage({ id: 'common.column.operation' }),
            valueType: 'option',
            align: 'center',
            fixed: 'right',
            render: (text, record, _, action) => (
              <a
                onClick={() => {
                  action?.startEditable?.(record.id);
                }}
              >
                {intl.formatMessage({ id: 'common.button.edit' })}
              </a>
            ),
          },
        ]}
      />
    </ProFormGroup>
  );
};
export default (props: CustomerCreateDrawerFormType) => {
  const intl = useIntl();
  const [form] = useForm();
  const [settleInfo, setSettleInfo] = useState<Settle>();
  const { get } = useModel('dictModel');
  useAsyncEffect(async () => {
    if (isEmpty(props.recordId)) {
      form.resetFields();
      setSettleInfo(undefined);
    } else {
      const data = await getCstDetail({ cstId: props.recordId });
      if (!isEmpty(data)) {
        const { addresses, images, tags, settle, billings, contacts } = data;
        setSettleInfo(settle);
        if (isEmpty(settle)) {
          delete data.settle;
        }
        if (isEmpty(contacts)) {
          delete data.contacts;
        }
        if (!isEmpty(addresses)) {
          data.addresses = addresses?.map((t) => ({
            ...t,
            addressCode: [t.provinceCode, t.cityCode, t.prefectureCode],
          }));
        } else {
          delete data.addresses;
        }
        if (!isEmpty(images)) {
          data.images = images?.map((t) => ({ uid: t.id, url: t.url }));
        } else {
          delete data.images;
        }
        if (!isEmpty(tags)) {
          data.tagIdList = uniq(tags?.map((t) => t.tagId));
        }
        delete data.tags;
        console.log(data);

        form.setFieldsValue(data);
      }
    }
  }, [props.visible]);

  return (
    <DrawerForm<CustomerSaveEntity>
      form={form}
      grid
      validateTrigger="onBlur"
      layout="horizontal"
      title={props.title}
      open={props.visible}
      width={1300}
      drawerProps={{
        maskClosable: false,
        styles: { body: { backgroundColor: '#F2F2F2' } },
        onClose: props.onCancel,
      }}
      onFinish={async (values) => {
        const { addresses, billings, tagIdList, contacts } = values;
        if (!isEmpty(addresses)) {
          const newAddress = addresses?.map((t) => {
            const { addressCode } = t;
            if (t?.id?.startsWith('address_')) {
              delete t.id;
            }
            if (addressCode) {
              const [provinceCode, cityCode, prefectureCode] = addressCode;
              delete t.addressCode;
              t.provinceCode = provinceCode;
              t.cityCode = cityCode;
              t.prefectureCode = prefectureCode;
              t.provinceName = get(provinceCode);
              t.cityName = get(cityCode);
              t.prefectureName = get(prefectureCode);
            }
            return t;
          });
          values.addresses = newAddress;
        } else {
          delete values.addresses;
        }
        if (!isEmpty(tagIdList)) {
          values.tags = tagIdList?.map((t) => ({ tagId: t }));
        }
        delete values.tagIdList;
        if (isEmpty(billings?.[0])) {
          delete values.billings;
        }
        if (!isEmpty(contacts)) {
          values.contacts = contacts?.map((t) => {
            const { id } = t;
            if (id?.startsWith('contact_')) {
              delete t.id;
            }
            return t;
          });
        }
        return props?.onOk?.(values);
      }}
    >
      {/* 基本信息 */}
      {renderBaseInfoFormItems(form, props.recordId, intl)}
      {/* 联系人信息 */}
      {RenderContact()}
      {/* 地址信息信息 */}
      {RenderAddress()}
      {/* 结算信息 */}
      {renderSettlementInfoFormItems(form, intl, settleInfo)}
    </DrawerForm>
  );
};
