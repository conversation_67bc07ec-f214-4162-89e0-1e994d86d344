import iconCstDef from '@/assets/icons/icon_cst_def.png';
import LeftTitle from '@/components/LeftTitle';
import { RightOutlined } from '@ant-design/icons';
import { DrawerForm, ProCard } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Button, Col, Empty, Flex, Image, Row, Space, Spin } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import classNames from 'classnames';
import { defaultTo, isEmpty } from 'lodash';
import { useState } from 'react';
import { getCstDetail } from '../../services';
import type { CustomerAmountHistoryModalType } from '../../types/CustomerAmountHistoryModalType';
import type { CustomerDetailDrawerFormType } from '../../types/CustomerDetailDrawerFormType';
import type { CustomerSaveEntity } from '../../types/CustomerSaveEntity';
import CustomerAmountHistoryModal from '../CustomerAmountHistoryModal';

/**
 * 基本信息
 * @param props CommonModelForm
 * @returns
 */
const renderBaseInfo = (data: CustomerSaveEntity, intl: any) => {
  const { base, images, tags } = data;
  const imageUrl = images?.[0]?.url;
  return (
    <ProCard className="py-2 rounded-lg">
      <Row className="text-[#000000D9]" justify="space-between" align="bottom">
        <Col span={4}>
          <Image
            className="rounded"
            preview={!isEmpty(imageUrl)}
            width={160}
            height={120}
            fallback={iconCstDef}
            src={imageUrl}
          />
        </Col>
        <Col span={20}>
          <Flex vertical={false} align="center">
            <span className="text-xl font-semibold">{base.cstName}</span>
            <span
              className={classNames(' text-xs px-2 py-1 ml-2', {
                'bg-[#EAF9ECFF]': base.cstStatus == 0,
                'text-[#33CC47FF] ': base.cstStatus == 0,
                'bg-[#FFEDEDFF]': base.cstStatus == 1,
                'text-[#F83331FF] ': base.cstStatus == 1,
              })}
            >
              {base.cstStatus == 0 ? intl.formatMessage({ id: 'customer.customerList.detailForm.status.enabled' }) : intl.formatMessage({ id: 'customer.customerList.detailForm.status.disabled' })}
            </span>
          </Flex>
          <Row className="mt-[12px]">
            <Col span={8}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.label.customerSn' })}{base.cstSn}</span>
            </Col>
            <Col span={8}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.label.nickName' })}{base.nickName}</span>
            </Col>
            <Col span={8}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.label.customerTags' })} {tags?.map((t) => t.tagName).join()}</span>
            </Col>
          </Row>
          <Row className="mt-[8px]">
            <Col span={8}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.label.store' })}{base.storeName}</span>
            </Col>
            <Col span={8}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.label.salesman' })}{base.salesmanName}</span>
            </Col>
            <Col span={8}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.label.createTime' })}{base.createTime}</span>
            </Col>
          </Row>
          <Row className="mt-[8px]">
            <Col span={24}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.label.remark' })}{base.remark}</span>
            </Col>
          </Row>
        </Col>
      </Row>
    </ProCard>
  );
};

/**
 * 联系人信息
 */
const renderContact = (data: CustomerSaveEntity, intl: any) => {
  const { contacts } = data;
  return (
    <ProCard
      title={<LeftTitle title={intl.formatMessage({ id: 'customer.customerList.detailForm.group.contactInfo' })} />}
      className="py-2 rounded-lg mt-4 cust-ant-empty-normal"
    >
      {!isEmpty(contacts) ? (
        contacts?.map((contact) => (
          <Row
            key={contact.id}
            className="p-4 mb-4 border-solid border border-black/[0.08] rounded"
          >
            <Col span={24} className="mb-3">
              <Flex vertical={false} align="center">
                <span className="text-xl font-semibold">{contact.name}</span>
                {contact.isDefault == 1 && (
                  <span className="bg-[#E7F0FFFF] text-[#176EFFFF] text-xs px-2 py-1 ml-2">
                    {intl.formatMessage({ id: 'customer.customerList.detailForm.tag.defaultContact' })}
                  </span>
                )}
              </Flex>
            </Col>
            <Col span={4}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.contact.label.position' })}{contact.position}</span>
            </Col>
            <Col span={5}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.contact.label.phone' })}{contact.phone}</span>
            </Col>
            <Col span={5}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.contact.label.qq' })}{contact.qq}</span>
            </Col>
            <Col span={5}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.contact.label.wechat' })}{contact.wechat}</span>
            </Col>
            <Col span={5}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.contact.label.email' })}{contact.email}</span>
            </Col>
            <Col span={24}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.contact.label.remark' })}{contact.remark}</span>
            </Col>
          </Row>
        ))
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </ProCard>
  );
};
/**
 * 地址信息
 */
const renderAddress = (data: CustomerSaveEntity, intl: any) => {
  const { addresses } = data;
  return (
    <ProCard
      title={<LeftTitle title={intl.formatMessage({ id: 'customer.customerList.detailForm.group.addressInfo' })} />}
      className="py-2 rounded-lg mt-4 cust-ant-empty-normal"
    >
      {!isEmpty(addresses) ? (
        addresses?.map((address) => (
          <Row
            key={address.id}
            className="p-4 mb-4 border-solid border border-black/[0.08] rounded"
          >
            <Col span={24} className="mb-3">
              <Flex vertical={false} align="center">
                {/* <span className="text-xl font-semibold">{address.name}</span> */}
                {address.isDefault == 1 && (
                  <span className="bg-[#E7F0FFFF] text-[#176EFFFF] text-xs px-2 py-1">
                    {intl.formatMessage({ id: 'customer.customerList.detailForm.tag.defaultAddress' })}
                  </span>
                )}
              </Flex>
            </Col>
            <Col span={6}>
              <span>
                {intl.formatMessage({ id: 'customer.customerList.detailForm.address.label.area' })}{address.provinceName}
                {address.cityName}
                {address.prefectureName}
              </span>
            </Col>
            <Col span={10}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.address.label.detailAddress' })}{address.address}</span>
            </Col>
            <Col span={4}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.address.label.contactName' })}{address.name}</span>
            </Col>
            <Col span={4}>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.address.label.phone' })}{address.phone}</span>
            </Col>
          </Row>
        ))
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </ProCard>
  );
};

/**
 * 结算信息
 * @param props CommonModelForm
 * @returns
 */
const renderSettlementInfo = (
  data: CustomerSaveEntity,
  showHistoryModal: (recordId: string) => void,
  intl: any,
) => {
  const { settle, base } = data;
  return (
    <ProCard
      extra={
        settle && (
          <Space size={8} className="cursor-pointer" onClick={() => showHistoryModal(base.id)}>
            <span className="text-[#00000099]">{intl.formatMessage({ id: 'customer.customerList.detailForm.link.viewAmountHistory' })}</span>
            <RightOutlined style={{ color: '#00000099' }} />
          </Space>
        )
      }
      title={<LeftTitle title={intl.formatMessage({ id: 'customer.customerList.detailForm.group.settlementInfo' })} />}
      className="py-2 rounded-lg mt-4 cust-ant-empty-normal"
    >
      {settle ? (
        <Row>
          <Col span={4}>
            <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.settlement.label.initialReceivable' })}{defaultTo(settle?.orgReceivableAmount, '-')}</span>
          </Col>
          <Col span={4}>
            <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.settlement.label.allowCredit' })}{settle?.credit ? intl.formatMessage({ id: 'option.yes' }) : intl.formatMessage({ id: 'option.no' })} </span>
          </Col>
          <Col span={10}>
            <Flex>
              {intl.formatMessage({ id: 'customer.customerList.detailForm.settlement.label.creditLimit' })}{defaultTo(settle?.totalAmount, '-')}
              <span>（</span>
              <span>
                {intl.formatMessage({ id: 'customer.customerList.detailForm.settlement.label.used' })}
                {settle?.credit ? defaultTo(settle.usedAmount, '-') : '-'}
              </span>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.settlement.label.frozen' })}{defaultTo(settle?.freezeAmount, '-')}</span>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.settlement.label.available' })}{defaultTo(settle?.availableAmount, '-')}</span>
              <span>）</span>
            </Flex>
          </Col>
          <Col span={6}>
            <Flex>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.settlement.label.creditTerms' })}{defaultTo(settle?.creditTerms, '-')}{intl.formatMessage({ id: 'unit.day' })}</span>
              <span>{intl.formatMessage({ id: 'customer.customerList.detailForm.settlement.label.remainingStart' })}{defaultTo(settle?.remainTerms, settle?.creditTerms)}{intl.formatMessage({ id: 'customer.customerList.detailForm.settlement.label.remainingEnd' })}</span>
            </Flex>
          </Col>
        </Row>
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </ProCard>
  );
};

export default (props: CustomerDetailDrawerFormType) => {
  const intl = useIntl();
  const [form] = useForm();
  const [data, setData] = useState<CustomerSaveEntity>();
  // 导入客户
  const [historyModalProps, setHistoryModalProps] = useState<CustomerAmountHistoryModalType>({
    visible: false,
    recordId: '',
    onCancel: () => {
      setHistoryModalProps((pre) => ({ ...pre, visible: false, recordId: '' }));
    },
  });
  useAsyncEffect(async () => {
    if (isEmpty(props.recordId)) {
      form.resetFields();
    } else {
      const detail = await getCstDetail({ cstId: props.recordId });
      setData(detail);
    }
  }, [props.visible]);

  const submitter = {
    render: () => (
      <Button key="cancel" size="middle" onClick={props.onCancel}>
        {intl.formatMessage({ id: 'common.button.cancel' })}
      </Button>
    ),
  };

  const showHistoryModal = (recordId: string) => {
    setHistoryModalProps((pre) => ({ ...pre, visible: true, recordId }));
  };

  return (
    <DrawerForm
      form={form}
      grid
      layout="horizontal"
      title={props.title}
      open={props.visible}
      width={1300}
      drawerProps={{
        maskClosable: true,
        styles: { body: { backgroundColor: '#F2F2F2' } },
        onClose: props.onCancel,
      }}
      submitter={submitter}
    >
      {data ? (
        <>
          {/* 基本信息 */}
          {renderBaseInfo(data, intl)}
          {/* 联系人信息 */}
          {renderContact(data, intl)}
          {/* 地址信息信息 */}
          {renderAddress(data, intl)}
          {/* 结算信息 */}
          {renderSettlementInfo(data, showHistoryModal, intl)}
        </>
      ) : (
        <Spin />
      )}
      <CustomerAmountHistoryModal {...historyModalProps} />
    </DrawerForm>
  );
};
