import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { ReverseCommonStatusValueEnum } from '@/types/CommonStatus';
import ColumnUtils from '@/utils/ColumnUtils';
import { exportData } from '@/utils/exportData';
import { importData } from '@/utils/importData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { Popconfirm, Space } from 'antd';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import CustomerCreateDrawerForm from './components/CustomerCreateDrawerForm';
import CustomerDetailDrawerForm from './components/CustomerDetailDrawerForm';
import { CustomerTableColumns } from './config/CustomerTableColumns';
import { changeStatus, queryCustomerPage, saveCst } from './services';
import { type CustomerCreateDrawerFormType } from './types/CustomerCreateDrawerFormType';
import type { CustomerDetailDrawerFormType } from './types/CustomerDetailDrawerFormType';
import { type CustomerEntity } from './types/CustomerEntity';
import type { CustomerSaveEntity } from './types/CustomerSaveEntity';

const CustomerList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();


  useActivate(() => {
    actionRef.current?.reload();
  });

  // 新增客户
  const [createModalProps, setCreateModalProps] = useState<CustomerCreateDrawerFormType>({
    visible: false,
    recordId: '',
    title: `新增客户`,
    onOk: () => Promise.resolve(true),
  });
  // 客户详情
  const [detailModalProps, setDetailModalProps] = useState<CustomerDetailDrawerFormType>({
    visible: false,
    recordId: '',
    onCancel: () => {
      setDetailModalProps((preModalProps) => ({
        ...preModalProps,
        visible: false,
        recordId: '',
      }));
    },
    title: `客户详情`,
  });

  /**
   * 启用/禁用
   * @param id
   */
  const handleUpOrDownItem = async (cstId: string, status: number) => {
    const result = await changeStatus({ cstId, status });
    if (result) {
      actionRef.current?.reset?.();
      actionRef.current?.reload(true);
    }
  };

  /**
   * 编辑
   * @param id
   */
  const handleUpdateItem = async (id: string) => {
    setCreateModalProps((preModalProps) => ({
      ...preModalProps,
      visible: true,
      readOnly: false,
      recordId: id,
      title: '客户编辑',
    }));
  };

  const operatorColumn: ProColumns<CustomerEntity> = {
    title: intl.formatMessage({ id: 'common.column.operation' }),
    valueType: 'option',
    width: 80,
    fixed: 'right',
    render: (_text, record: CustomerEntity) => {
      const { cstId, cstStatus } = record;
      const status = cstStatus ^ 1;
      const operType: string = ColumnUtils.getText(ReverseCommonStatusValueEnum, status);
      return (
        <Space>
          <AuthButton isHref authority="editCustomer" onClick={() => handleUpdateItem(cstId)}>
            {intl.formatMessage({ id: 'common.button.edit' })}
          </AuthButton>
          <Popconfirm
            title={intl.formatMessage({ id: 'customer.customerList.table.popconfirm.enableDisable' }, { operType })}
            onConfirm={() => {
              handleUpOrDownItem(cstId, status);
            }}
          >
            <AuthButton isHref authority="enableOrDisableCustomer">
              {operType}
            </AuthButton>
          </Popconfirm>
        </Space>
      );
    },
  };

  /**
   * 关闭【新增】对话框
   */
  const hideCreateModal = () => {
    setCreateModalProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      recordId: '',
      readOnly: false,
    }));
  };

  /**
   * 新增客户
   * @param values
   */
  const handleCustomerCreate = async (values: CustomerSaveEntity) => {
    try {
      const result = await saveCst(values);
      if (result) {
        hideCreateModal();
        actionRef.current?.reset?.();
        actionRef.current?.reload(true);
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  /**
   * 查看详情
   * @param id
   */
  const openDetailModal = (id: string) => {
    setDetailModalProps((preModalProps) => ({
      ...preModalProps,
      visible: true,
      recordId: id,
    }));
  };

  return (
    <PageContainer>
      <FunProTable<CustomerEntity, any>
        requestPage={queryCustomerPage}
        actionRef={actionRef}
        formRef={formRef}
        columns={[
          {
            title: intl.formatMessage({ id: 'common.column.index' }),
            valueType: 'index',
            width: 40,
            fixed: 'left',
          },
          {
            title: intl.formatMessage({ id: 'customer.customerList.table.column.customerSn' }),
            dataIndex: 'cstSn',
            width: 80,
            search: false,
            render: (text, record) => {
              return <a onClick={() => openDetailModal(record.cstId)}>{text}</a>;
            },
          },
          {
            title: intl.formatMessage({ id: 'customer.customerList.table.column.customerName' }),
            dataIndex: 'cstName',
            width: 120,
            hideInSearch: true,
          },
          ...CustomerTableColumns(intl),
          operatorColumn,
        ]}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              authority="addCustomer"
              onClick={() => {
                setCreateModalProps((preModalProps) => ({
                  ...preModalProps,
                  visible: true,
                  readOnly: false,
                  title: intl.formatMessage({ id: 'customer.customerList.createModal.titleAdd' }),
                }));
              }}
            >
              {intl.formatMessage({ id: 'customer.customerList.button.addCustomer' })}
            </AuthButton>
            <AuthButton
              danger
              authority="importCustomer"
              onClick={() => {
                importData({
                  systemId: 'ETC_SAAS_SYS',
                  taskDesc: intl.formatMessage({ id: 'customer.customerList.import.taskDesc' }),
                  moduleId: 'MEMBER_CST_IMPORT',
                  downloadFileName:
                    'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E5%AE%A2%E6%88%B7%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
                });
              }}
            >
              {intl.formatMessage({ id: 'common.button.batchImport' })}
            </AuthButton>
            <AuthButton
              danger
              authority="exportCustomer"
              onClick={() => {
                exportData({
                  systemId: 'ETC_SAAS_SYS',
                  taskDesc: intl.formatMessage({ id: 'customer.customerList.export.taskDesc' }),
                  moduleId: 'MEMBER_CST_EXPRORT',
                  params: formRef.current?.getFieldsFormatValue?.(),
                });
              }}
            >
              {intl.formatMessage({ id: 'common.button.export' })}
            </AuthButton>
          </Space>
        }
      />

      <CustomerCreateDrawerForm
        {...createModalProps}
        onCancel={hideCreateModal}
        onOk={handleCustomerCreate}
      />
      <CustomerDetailDrawerForm {...detailModalProps} />
    </PageContainer>
  );
};

export default withKeepAlive(CustomerList);