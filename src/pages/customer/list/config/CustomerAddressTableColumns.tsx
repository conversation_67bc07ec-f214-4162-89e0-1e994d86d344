import type { ProColumns } from '@ant-design/pro-components';
import type { CustomerAddressEntity } from '../types/CustomerAddressEntity';

export default (intl: any): ProColumns<CustomerAddressEntity>[] => [
  {
    title: intl.formatMessage({ id: 'customer.customerList.createForm.addressTable.column.detailAddress' }),
    dataIndex: 'address',
    width: 200,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.createForm.addressTable.column.contactName' }),
    dataIndex: 'name',
    width: 200,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.createForm.addressTable.column.contactPhone' }),
    dataIndex: 'phone',
    width: 200,
  },
];
