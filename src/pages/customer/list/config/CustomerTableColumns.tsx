import { queryStoreByAccount } from '@/pages/system/user/services';
import { ReverseCommonStatusValueEnum } from '@/types/CommonStatus';
import type { ProColumns } from '@ant-design/pro-components';
import { Badge } from 'antd';
import { defaultTo, isEmpty } from 'lodash';
import { getTagList } from '../services';
import type { CustomerEntity } from '../types/CustomerEntity';
import type { CustomerTagEntity } from '../types/CustomerTagEntity';

export const CustomerTableColumns = (intl: any): ProColumns<CustomerEntity>[] => [
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.nickName' }),
    dataIndex: 'nickName',
    search: false,
    width: 100,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.customerTags' }),
    dataIndex: 'tags',
    hideInSearch: true,
    width: 100,
    ellipsis: true,
    renderText: (text: CustomerTagEntity[]) => {
      return isEmpty(text) ? '' : text.map((t) => t.tagName).join(',');
    },
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.storeName' }),
    dataIndex: 'storeName',
    width: 120,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.salesmanName' }),
    dataIndex: 'salesmanName',
    width: 60,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.allowCredit' }),
    dataIndex: 'credit',
    width: 60,
    hideInSearch: true,
    ellipsis: true,
    renderText: (credit) => {
      const status = credit ? 'success' : 'default';
      const text = credit ? intl.formatMessage({ id: 'common.yes' }) : intl.formatMessage({ id: 'common.no' });
      return <Badge status={status} text={text} />;
    },
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.creditLimit' }),
    dataIndex: 'totalAmount',
    width: 140,
    hideInSearch: true,
    ellipsis: true,
    renderText: (totalAmount, record) => {
      if (record?.credit) {
        return `${intl.formatMessage({ id: 'customer.customerList.table.column.creditLimit.used' })}${defaultTo(record?.usedAmount, '-')}${intl.formatMessage({ id: 'customer.customerList.table.column.creditLimit.available' })}${defaultTo(
          record?.availableAmount,
          '-',
        )}`;
      }
    },
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.creditTerms' }),
    dataIndex: 'creditTerms',
    width: 60,
    hideInSearch: true,
    ellipsis: true,
    renderText: (creditTerms, record) => {
      if (record?.credit) {
        return `${defaultTo(record?.remainTerms, '-')}/${defaultTo(creditTerms, '-')}`;
      }
    },
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.receivableAmount' }),
    dataIndex: 'receivableAmount',
    width: 80,
    hideInSearch: true,
    ellipsis: true,
    valueType: 'money',
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.defaultContactName' }),
    dataIndex: ['contacts', 0, 'name'],
    width: 80,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.search.customerInfo' }),
    dataIndex: 'cstWord',
    ellipsis: true,
    hideInTable: true,
    fieldProps: {
      placeholder: intl.formatMessage({ id: 'customer.customerList.table.search.customerInfo.placeholder' }),
    },
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.search.contactName' }),
    dataIndex: 'contact',
    width: 120,
    hideInTable: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.defaultContactPhone' }),
    dataIndex: ['contacts', 0, 'phone'],
    width: 100,
    hideInSearch: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.search.contactPhone' }),
    dataIndex: 'phone',
    hideInTable: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.defaultAddress' }),
    dataIndex: ['addresses', 0, 'fullAddress'],
    width: 180,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.search.customerTags' }),
    dataIndex: 'tagId',
    width: 120,
    hideInTable: true,
    valueType: 'select',
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 3,
    },
    search: {
      transform: (value) => ({
        tagIds: value,
      }),
    },
    request: async () => {
      const tagList = await getTagList({ tagStatus: 0, tagType: 1 });
      return tagList?.data?.map((t: any) => ({ label: t.tagName, value: t.id })) || [];
    },
  },

  {
    title: intl.formatMessage({ id: 'customer.customerList.table.search.store' }),
    dataIndex: 'storeIds',
    hideInTable: true,
    valueType: 'select',
    fieldProps: {
      mode: 'multiple',
      showSearch: true,
      maxTagCount: 3,
      fieldNames: { label: 'name', value: 'id' },
    },
    request: () => queryStoreByAccount({ status: 1 }),
  },
  {
    title: intl.formatMessage({ id: 'common.column.status' }),
    dataIndex: 'cstStatus',
    hideInSearch: true,
    width: 60,
    valueEnum: ReverseCommonStatusValueEnum,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.createTime' }),
    width: 140,
    dataIndex: 'createTime',
    hideInSearch: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.search.createTime' }),
    dataIndex: 'createTime',
    valueType: 'dateRange',
    hideInTable: true,
    search: {
      transform: ([beginTime, endTime]) => ({ beginTime, endTime }),
    },
    // "fieldProps": {
    //   "showTime": {
    //     "defaultValue": [
    //       dayjs("00:00:00", 'HH:mm:ss'),
    //       dayjs("23:59:59", 'HH:mm:ss'),
    //     ]
    //   }
    // }
  },
];
