import type { ProColumns } from '@ant-design/pro-components';
import type { CustomerContactEntity } from '../types/CustomerContactEntity.ts';

export default (intl: any): ProColumns<CustomerContactEntity>[] => [
  {
    title: intl.formatMessage({ id: 'customer.customerList.contactTable.column.phone' }),
    dataIndex: 'phone',
    search: true,
    //valueType: 'digit',
    width: 140,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.contactTable.column.position' }),
    dataIndex: 'position',
    search: false,
    width: 200,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.contactTable.column.qq' }),
    dataIndex: 'qq',
    search: false,
    width: 200,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.contactTable.column.wechat' }),
    dataIndex: 'wechat',
    width: 200,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.contactTable.column.email' }),
    dataIndex: 'email',
    width: 200,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.contactTable.column.remark' }),
    dataIndex: 'remark',
    width: 200,
    search: false,
    ellipsis: true,
  },
];
