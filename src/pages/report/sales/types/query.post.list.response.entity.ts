export interface QueryPostListEntity {
    /**
     * 业务时间（订单提交时间）
     */
    bizTime?: string;
    /**
     * 品牌销售毛利金额占比
     */
    brandGrossProfitRate?: number;
    /**
     * 品牌id
     */
    brandId?: string;
    /**
     * 品牌名称
     */
    brandName?: string;
    /**
     * 品牌销售数量（正向）占比
     */
    brandSaleNumRate?: number;
    /**
     * 品类销售毛利金额占比
     */
    categoryGrossProfitRate?: number;
    /**
     * 品类id
     */
    categoryId?: string;
    /**
     * 品类名称
     */
    categoryName?: string;
    /**
     * 品类销售数量（正向）占比
     */
    categorySaleNumRate?: number;
    /**
     * 销售成本金额
     */
    costAmount?: number;
    /**
     * 客户id
     */
    customerId?: string;
    /**
     * 客户名称
     */
    customerName?: string;
    /**
     * 员工id
     */
    employeeId?: string;
    /**
     * 员工名称
     */
    employeeName?: string;
    /**
     * 销售毛利率
     */
    grossProfitRate?: number;
    /**
     * 商品名称
     */
    itemName?: string;
    /**
     * 商品编码
     */
    itemSn?: string;
    /**
     * 订单号
     */
    orderNo?: string;
    /**
     * 销售金额（正向）
     */
    orderSaleAmount?: number;
    /**
     * 订单方向1-正向2-逆向
     */
    orderType?: number;
    /**
     * 销售金额
     */
    saleAmount?: number;
    /**
     * 销售客户数（正向)
     */
    saleCustomerNum?: number;
    /**
     * 销售毛利金额
     */
    saleGrossProfit?: number;
    /**
     * 销售数量
     */
    saleSaleNum?: number;
    /**
     * 销售单数（正向）
     */
    saleOrderNum?: number;
    /**
     * 门店id
     */
    storeId?: string;
    /**
     * 门店名称
     */
    storeName?: string;
    /**
     * 数据更新时间
     */
    updateTime?: string;
    /**
     * 客单价
     */
    perCstTrans?: number;
    /**
     * 销售均价
     */
    avgSaleAmount?: number;
}