/**
 * 销售商品统计-品牌占比（环形图）
 */
export interface SalesGroupByBrand {
    /**
     * 销售商品统计-品牌占比（环形图）,销售金额排序
     */
    orderBySaleAmountList?: OrderBySaleAmountList[];
    /**
     * 销售商品统计-品牌占比（环形图）,销售毛利金额排序
     */
    orderBySaleGrossProfitList?: OrderBySaleGrossProfitList[];
    /**
     * 销售商品统计-品牌占比（环形图）,销售数量排序
     */
    orderBySaleNumList?: OrderBySaleNumList[];
}

export interface OrderBySaleAmountList {
    /**
     * 销售均价
     */
    avgSaleAmount?: number;
    /**
     * 业务时间（订单提交时间）
     */
    bizTime?: string;
    /**
     * 品牌销售毛利金额占比
     */
    brandGrossProfitRate?: number;
    /**
     * 品牌id
     */
    brandId?: string;
    /**
     * 品牌名称
     */
    brandName?: string;
    /**
     * 品牌销售金额
     */
    brandSaleAmount?: number;
    /**
     * 品牌销售金额占比
     */
    brandSaleAmountRate?: number;
    /**
     * 品牌销售毛利金额
     */
    brandSaleGrossProfit?: number;
    /**
     * 品牌销售数量（正向）
     */
    brandSaleNum?: number;
    /**
     * 品牌销售数量（正向）占比
     */
    brandSaleNumRate?: number;
    /**
     * 品类销售毛利金额占比
     */
    categoryGrossProfitRate?: number;
    /**
     * 品类id
     */
    categoryId?: string;
    /**
     * 品类名称
     */
    categoryName?: string;
    /**
     * 品类销售金额
     */
    categorySaleAmount?: number;
    /**
     * 品类销售金额占比
     */
    categorySaleAmountRate?: number;
    /**
     * 品类销售毛利金额
     */
    categorySaleGrossProfit?: number;
    /**
     * 品类销售数量（正向）
     */
    categorySaleNum?: number;
    /**
     * 品类销售数量（正向）占比
     */
    categorySaleNumRate?: number;
    /**
     * 销售成本金额
     */
    costAmount?: number;
    /**
     * 客户id
     */
    customerId?: string;
    /**
     * 客户名称
     */
    customerName?: string;
    /**
     * 员工id
     */
    employeeId?: string;
    /**
     * 员工名称
     */
    employeeName?: string;
    /**
     * 销售毛利率
     */
    grossProfitRate?: number;
    /**
     * 商品名称
     */
    itemName?: string;
    /**
     * 商品编码
     */
    itemSn?: string;
    /**
     * 订单号
     */
    orderNo?: string;
    /**
     * 销售金额（正向）
     */
    orderSaleAmount?: number;
    /**
     * 订单方向1-正向2-逆向
     */
    orderType?: number;
    /**
     * 客单价（销售金额（正向）销售订单数（正向））
     */
    perCstTrans?: number;
    /**
     * 销售金额
     */
    saleAmount?: number;
    /**
     * 销售客户数（正向)
     */
    saleCustomerNum?: number;
    /**
     * 销售毛利金额
     */
    saleGrossProfit?: number;
    /**
     * 销售数量
     */
    saleNum?: number;
    /**
     * 销售单数（正向）
     */
    saleOrderNum?: number;
    /**
     * 销售商品数（正向）
     */
    saleSaleNum?: number;
    /**
     * 门店id
     */
    storeId?: string;
    /**
     * 门店名称
     */
    storeName?: string;
    /**
     * 数据更新时间
     */
    updateTime?: string;
}

export interface OrderBySaleGrossProfitList {
    /**
     * 销售均价
     */
    avgSaleAmount?: number;
    /**
     * 业务时间（订单提交时间）
     */
    bizTime?: string;
    /**
     * 品牌销售毛利金额占比
     */
    brandGrossProfitRate?: number;
    /**
     * 品牌id
     */
    brandId?: string;
    /**
     * 品牌名称
     */
    brandName?: string;
    /**
     * 品牌销售金额
     */
    brandSaleAmount?: number;
    /**
     * 品牌销售金额占比
     */
    brandSaleAmountRate?: number;
    /**
     * 品牌销售毛利金额
     */
    brandSaleGrossProfit?: number;
    /**
     * 品牌销售数量（正向）
     */
    brandSaleNum?: number;
    /**
     * 品牌销售数量（正向）占比
     */
    brandSaleNumRate?: number;
    /**
     * 品类销售毛利金额占比
     */
    categoryGrossProfitRate?: number;
    /**
     * 品类id
     */
    categoryId?: string;
    /**
     * 品类名称
     */
    categoryName?: string;
    /**
     * 品类销售金额
     */
    categorySaleAmount?: number;
    /**
     * 品类销售金额占比
     */
    categorySaleAmountRate?: number;
    /**
     * 品类销售毛利金额
     */
    categorySaleGrossProfit?: number;
    /**
     * 品类销售数量（正向）
     */
    categorySaleNum?: number;
    /**
     * 品类销售数量（正向）占比
     */
    categorySaleNumRate?: number;
    /**
     * 销售成本金额
     */
    costAmount?: number;
    /**
     * 客户id
     */
    customerId?: string;
    /**
     * 客户名称
     */
    customerName?: string;
    /**
     * 员工id
     */
    employeeId?: string;
    /**
     * 员工名称
     */
    employeeName?: string;
    /**
     * 销售毛利率
     */
    grossProfitRate?: number;
    /**
     * 商品名称
     */
    itemName?: string;
    /**
     * 商品编码
     */
    itemSn?: string;
    /**
     * 订单号
     */
    orderNo?: string;
    /**
     * 销售金额（正向）
     */
    orderSaleAmount?: number;
    /**
     * 订单方向1-正向2-逆向
     */
    orderType?: number;
    /**
     * 客单价（销售金额（正向）销售订单数（正向））
     */
    perCstTrans?: number;
    /**
     * 销售金额
     */
    saleAmount?: number;
    /**
     * 销售客户数（正向)
     */
    saleCustomerNum?: number;
    /**
     * 销售毛利金额
     */
    saleGrossProfit?: number;
    /**
     * 销售数量
     */
    saleNum?: number;
    /**
     * 销售单数（正向）
     */
    saleOrderNum?: number;
    /**
     * 销售商品数（正向）
     */
    saleSaleNum?: number;
    /**
     * 门店id
     */
    storeId?: string;
    /**
     * 门店名称
     */
    storeName?: string;
    /**
     * 数据更新时间
     */
    updateTime?: string;
}

export interface OrderBySaleNumList {
    /**
     * 销售均价
     */
    avgSaleAmount?: number;
    /**
     * 业务时间（订单提交时间）
     */
    bizTime?: string;
    /**
     * 品牌销售毛利金额占比
     */
    brandGrossProfitRate?: number;
    /**
     * 品牌id
     */
    brandId?: string;
    /**
     * 品牌名称
     */
    brandName?: string;
    /**
     * 品牌销售金额
     */
    brandSaleAmount?: number;
    /**
     * 品牌销售金额占比
     */
    brandSaleAmountRate?: number;
    /**
     * 品牌销售毛利金额
     */
    brandSaleGrossProfit?: number;
    /**
     * 品牌销售数量（正向）
     */
    brandSaleNum?: number;
    /**
     * 品牌销售数量（正向）占比
     */
    brandSaleNumRate?: number;
    /**
     * 品类销售毛利金额占比
     */
    categoryGrossProfitRate?: number;
    /**
     * 品类id
     */
    categoryId?: string;
    /**
     * 品类名称
     */
    categoryName?: string;
    /**
     * 品类销售金额
     */
    categorySaleAmount?: number;
    /**
     * 品类销售金额占比
     */
    categorySaleAmountRate?: number;
    /**
     * 品类销售毛利金额
     */
    categorySaleGrossProfit?: number;
    /**
     * 品类销售数量（正向）
     */
    categorySaleNum?: number;
    /**
     * 品类销售数量（正向）占比
     */
    categorySaleNumRate?: number;
    /**
     * 销售成本金额
     */
    costAmount?: number;
    /**
     * 客户id
     */
    customerId?: string;
    /**
     * 客户名称
     */
    customerName?: string;
    /**
     * 员工id
     */
    employeeId?: string;
    /**
     * 员工名称
     */
    employeeName?: string;
    /**
     * 销售毛利率
     */
    grossProfitRate?: number;
    /**
     * 商品名称
     */
    itemName?: string;
    /**
     * 商品编码
     */
    itemSn?: string;
    /**
     * 订单号
     */
    orderNo?: string;
    /**
     * 销售金额（正向）
     */
    orderSaleAmount?: number;
    /**
     * 订单方向1-正向2-逆向
     */
    orderType?: number;
    /**
     * 客单价（销售金额（正向）销售订单数（正向））
     */
    perCstTrans?: number;
    /**
     * 销售金额
     */
    saleAmount?: number;
    /**
     * 销售客户数（正向)
     */
    saleCustomerNum?: number;
    /**
     * 销售毛利金额
     */
    saleGrossProfit?: number;
    /**
     * 销售数量
     */
    saleNum?: number;
    /**
     * 销售单数（正向）
     */
    saleOrderNum?: number;
    /**
     * 销售商品数（正向）
     */
    saleSaleNum?: number;
    /**
     * 门店id
     */
    storeId?: string;
    /**
     * 门店名称
     */
    storeName?: string;
    /**
     * 数据更新时间
     */
    updateTime?: string;
}

/**
 * 销售商品统计-品类占比（环形图）
 */
export interface SalesGroupByCategory {
    /**
     * 销售商品统计-品类占比（环形图）,销售金额排序
     */
    orderBySaleAmountList?: OrderBySaleAmountList[];
    /**
     * 销售商品统计-品类占比（环形图）,销售毛利金额排序
     */
    orderBySaleGrossProfitList?: OrderBySaleGrossProfitList[];
    /**
     * 销售商品统计-品类占比（环形图）,销售数量排序
     */
    orderBySaleNumList?: OrderBySaleNumList[];
}

export interface OrderBySaleAmountList {
    /**
     * 销售均价
     */
    avgSaleAmount?: number;
    /**
     * 业务时间（订单提交时间）
     */
    bizTime?: string;
    /**
     * 品牌销售毛利金额占比
     */
    brandGrossProfitRate?: number;
    /**
     * 品牌id
     */
    brandId?: string;
    /**
     * 品牌名称
     */
    brandName?: string;
    /**
     * 品牌销售金额
     */
    brandSaleAmount?: number;
    /**
     * 品牌销售金额占比
     */
    brandSaleAmountRate?: number;
    /**
     * 品牌销售毛利金额
     */
    brandSaleGrossProfit?: number;
    /**
     * 品牌销售数量（正向）
     */
    brandSaleNum?: number;
    /**
     * 品牌销售数量（正向）占比
     */
    brandSaleNumRate?: number;
    /**
     * 品类销售毛利金额占比
     */
    categoryGrossProfitRate?: number;
    /**
     * 品类id
     */
    categoryId?: string;
    /**
     * 品类名称
     */
    categoryName?: string;
    /**
     * 品类销售金额
     */
    categorySaleAmount?: number;
    /**
     * 品类销售金额占比
     */
    categorySaleAmountRate?: number;
    /**
     * 品类销售毛利金额
     */
    categorySaleGrossProfit?: number;
    /**
     * 品类销售数量（正向）
     */
    categorySaleNum?: number;
    /**
     * 品类销售数量（正向）占比
     */
    categorySaleNumRate?: number;
    /**
     * 销售成本金额
     */
    costAmount?: number;
    /**
     * 客户id
     */
    customerId?: string;
    /**
     * 客户名称
     */
    customerName?: string;
    /**
     * 员工id
     */
    employeeId?: string;
    /**
     * 员工名称
     */
    employeeName?: string;
    /**
     * 销售毛利率
     */
    grossProfitRate?: number;
    /**
     * 商品名称
     */
    itemName?: string;
    /**
     * 商品编码
     */
    itemSn?: string;
    /**
     * 订单号
     */
    orderNo?: string;
    /**
     * 销售金额（正向）
     */
    orderSaleAmount?: number;
    /**
     * 订单方向1-正向2-逆向
     */
    orderType?: number;
    /**
     * 客单价（销售金额（正向）销售订单数（正向））
     */
    perCstTrans?: number;
    /**
     * 销售金额
     */
    saleAmount?: number;
    /**
     * 销售客户数（正向)
     */
    saleCustomerNum?: number;
    /**
     * 销售毛利金额
     */
    saleGrossProfit?: number;
    /**
     * 销售数量
     */
    saleNum?: number;
    /**
     * 销售单数（正向）
     */
    saleOrderNum?: number;
    /**
     * 销售商品数（正向）
     */
    saleSaleNum?: number;
    /**
     * 门店id
     */
    storeId?: string;
    /**
     * 门店名称
     */
    storeName?: string;
    /**
     * 数据更新时间
     */
    updateTime?: string;
}

export interface OrderBySaleGrossProfitList {
    /**
     * 销售均价
     */
    avgSaleAmount?: number;
    /**
     * 业务时间（订单提交时间）
     */
    bizTime?: string;
    /**
     * 品牌销售毛利金额占比
     */
    brandGrossProfitRate?: number;
    /**
     * 品牌id
     */
    brandId?: string;
    /**
     * 品牌名称
     */
    brandName?: string;
    /**
     * 品牌销售金额
     */
    brandSaleAmount?: number;
    /**
     * 品牌销售金额占比
     */
    brandSaleAmountRate?: number;
    /**
     * 品牌销售毛利金额
     */
    brandSaleGrossProfit?: number;
    /**
     * 品牌销售数量（正向）
     */
    brandSaleNum?: number;
    /**
     * 品牌销售数量（正向）占比
     */
    brandSaleNumRate?: number;
    /**
     * 品类销售毛利金额占比
     */
    categoryGrossProfitRate?: number;
    /**
     * 品类id
     */
    categoryId?: string;
    /**
     * 品类名称
     */
    categoryName?: string;
    /**
     * 品类销售金额
     */
    categorySaleAmount?: number;
    /**
     * 品类销售金额占比
     */
    categorySaleAmountRate?: number;
    /**
     * 品类销售毛利金额
     */
    categorySaleGrossProfit?: number;
    /**
     * 品类销售数量（正向）
     */
    categorySaleNum?: number;
    /**
     * 品类销售数量（正向）占比
     */
    categorySaleNumRate?: number;
    /**
     * 销售成本金额
     */
    costAmount?: number;
    /**
     * 客户id
     */
    customerId?: string;
    /**
     * 客户名称
     */
    customerName?: string;
    /**
     * 员工id
     */
    employeeId?: string;
    /**
     * 员工名称
     */
    employeeName?: string;
    /**
     * 销售毛利率
     */
    grossProfitRate?: number;
    /**
     * 商品名称
     */
    itemName?: string;
    /**
     * 商品编码
     */
    itemSn?: string;
    /**
     * 订单号
     */
    orderNo?: string;
    /**
     * 销售金额（正向）
     */
    orderSaleAmount?: number;
    /**
     * 订单方向1-正向2-逆向
     */
    orderType?: number;
    /**
     * 客单价（销售金额（正向）销售订单数（正向））
     */
    perCstTrans?: number;
    /**
     * 销售金额
     */
    saleAmount?: number;
    /**
     * 销售客户数（正向)
     */
    saleCustomerNum?: number;
    /**
     * 销售毛利金额
     */
    saleGrossProfit?: number;
    /**
     * 销售数量
     */
    saleNum?: number;
    /**
     * 销售单数（正向）
     */
    saleOrderNum?: number;
    /**
     * 销售商品数（正向）
     */
    saleSaleNum?: number;
    /**
     * 门店id
     */
    storeId?: string;
    /**
     * 门店名称
     */
    storeName?: string;
    /**
     * 数据更新时间
     */
    updateTime?: string;
}

export interface OrderBySaleNumList {
    /**
     * 销售均价
     */
    avgSaleAmount?: number;
    /**
     * 业务时间（订单提交时间）
     */
    bizTime?: string;
    /**
     * 品牌销售毛利金额占比
     */
    brandGrossProfitRate?: number;
    /**
     * 品牌id
     */
    brandId?: string;
    /**
     * 品牌名称
     */
    brandName?: string;
    /**
     * 品牌销售金额
     */
    brandSaleAmount?: number;
    /**
     * 品牌销售金额占比
     */
    brandSaleAmountRate?: number;
    /**
     * 品牌销售毛利金额
     */
    brandSaleGrossProfit?: number;
    /**
     * 品牌销售数量（正向）
     */
    brandSaleNum?: number;
    /**
     * 品牌销售数量（正向）占比
     */
    brandSaleNumRate?: number;
    /**
     * 品类销售毛利金额占比
     */
    categoryGrossProfitRate?: number;
    /**
     * 品类id
     */
    categoryId?: string;
    /**
     * 品类名称
     */
    categoryName?: string;
    /**
     * 品类销售金额
     */
    categorySaleAmount?: number;
    /**
     * 品类销售金额占比
     */
    categorySaleAmountRate?: number;
    /**
     * 品类销售毛利金额
     */
    categorySaleGrossProfit?: number;
    /**
     * 品类销售数量（正向）
     */
    categorySaleNum?: number;
    /**
     * 品类销售数量（正向）占比
     */
    categorySaleNumRate?: number;
    /**
     * 销售成本金额
     */
    costAmount?: number;
    /**
     * 客户id
     */
    customerId?: string;
    /**
     * 客户名称
     */
    customerName?: string;
    /**
     * 员工id
     */
    employeeId?: string;
    /**
     * 员工名称
     */
    employeeName?: string;
    /**
     * 销售毛利率
     */
    grossProfitRate?: number;
    /**
     * 商品名称
     */
    itemName?: string;
    /**
     * 商品编码
     */
    itemSn?: string;
    /**
     * 订单号
     */
    orderNo?: string;
    /**
     * 销售金额（正向）
     */
    orderSaleAmount?: number;
    /**
     * 订单方向1-正向2-逆向
     */
    orderType?: number;
    /**
     * 客单价（销售金额（正向）销售订单数（正向））
     */
    perCstTrans?: number;
    /**
     * 销售金额
     */
    saleAmount?: number;
    /**
     * 销售客户数（正向)
     */
    saleCustomerNum?: number;
    /**
     * 销售毛利金额
     */
    saleGrossProfit?: number;
    /**
     * 销售数量
     */
    saleNum?: number;
    /**
     * 销售单数（正向）
     */
    saleOrderNum?: number;
    /**
     * 销售商品数（正向）
     */
    saleSaleNum?: number;
    /**
     * 门店id
     */
    storeId?: string;
    /**
     * 门店名称
     */
    storeName?: string;
    /**
     * 数据更新时间
     */
    updateTime?: string;
}

/**
 * 销售报表概览
 */
export interface SalesOverview {
    /**
      * 销售均价
      */
    avgSaleAmount?: number;
    /**
     * 业务时间（订单提交时间）
     */
    bizTime?: string;
    /**
     * 品牌销售毛利金额占比
     */
    brandGrossProfitRate?: number;
    /**
     * 品牌id
     */
    brandId?: string;
    /**
     * 品牌名称
     */
    brandName?: string;
    /**
     * 品牌销售金额
     */
    brandSaleAmount?: number;
    /**
     * 品牌销售金额占比
     */
    brandSaleAmountRate?: number;
    /**
     * 品牌销售毛利金额
     */
    brandSaleGrossProfit?: number;
    /**
     * 品牌销售数量（正向）
     */
    brandSaleNum?: number;
    /**
     * 品牌销售数量（正向）占比
     */
    brandSaleNumRate?: number;
    /**
     * 品类销售毛利金额占比
     */
    categoryGrossProfitRate?: number;
    /**
     * 品类id
     */
    categoryId?: string;
    /**
     * 品类名称
     */
    categoryName?: string;
    /**
     * 品类销售金额
     */
    categorySaleAmount?: number;
    /**
     * 品类销售金额占比
     */
    categorySaleAmountRate?: number;
    /**
     * 品类销售毛利金额
     */
    categorySaleGrossProfit?: number;
    /**
     * 品类销售数量（正向）
     */
    categorySaleNum?: number;
    /**
     * 品类销售数量（正向）占比
     */
    categorySaleNumRate?: number;
    /**
     * 销售成本金额
     */
    costAmount?: number;
    /**
     * 客户id
     */
    customerId?: string;
    /**
     * 客户名称
     */
    customerName?: string;
    /**
     * 员工id
     */
    employeeId?: string;
    /**
     * 员工名称
     */
    employeeName?: string;
    /**
     * 销售毛利率
     */
    grossProfitRate?: number;
    /**
     * 商品名称
     */
    itemName?: string;
    /**
     * 商品编码
     */
    itemSn?: string;
    /**
     * 订单号
     */
    orderNo?: string;
    /**
     * 销售金额（正向）
     */
    orderSaleAmount?: number;
    /**
     * 订单方向1-正向2-逆向
     */
    orderType?: number;
    /**
     * 客单价（销售金额（正向）销售订单数（正向））
     */
    perCstTrans?: number;
    /**
     * 销售金额
     */
    saleAmount?: number;
    /**
     * 销售客户数（正向)
     */
    saleCustomerNum?: number;
    /**
     * 销售毛利金额
     */
    saleGrossProfit?: number;
    /**
     * 销售数量
     */
    saleNum?: number;
    /**
     * 销售单数（正向）
     */
    saleOrderNum?: number;
    /**
     * 销售商品数（正向）
     */
    saleSaleNum?: number;
    /**
     * 门店id
     */
    storeId?: string;
    /**
     * 门店名称
     */
    storeName?: string;
    /**
     * 数据更新时间
     */
    updateTime?: string;
}

export interface SalesTrendList {
    /**
     * 销售均价
     */
    avgSaleAmount?: number;
    /**
     * 业务时间（订单提交时间）
     */
    bizTime?: string;
    /**
     * 品牌销售毛利金额占比
     */
    brandGrossProfitRate?: number;
    /**
     * 品牌id
     */
    brandId?: string;
    /**
     * 品牌名称
     */
    brandName?: string;
    /**
     * 品牌销售金额
     */
    brandSaleAmount?: number;
    /**
     * 品牌销售金额占比
     */
    brandSaleAmountRate?: number;
    /**
     * 品牌销售毛利金额
     */
    brandSaleGrossProfit?: number;
    /**
     * 品牌销售数量（正向）
     */
    brandSaleNum?: number;
    /**
     * 品牌销售数量（正向）占比
     */
    brandSaleNumRate?: number;
    /**
     * 品类销售毛利金额占比
     */
    categoryGrossProfitRate?: number;
    /**
     * 品类id
     */
    categoryId?: string;
    /**
     * 品类名称
     */
    categoryName?: string;
    /**
     * 品类销售金额
     */
    categorySaleAmount?: number;
    /**
     * 品类销售金额占比
     */
    categorySaleAmountRate?: number;
    /**
     * 品类销售毛利金额
     */
    categorySaleGrossProfit?: number;
    /**
     * 品类销售数量（正向）
     */
    categorySaleNum?: number;
    /**
     * 品类销售数量（正向）占比
     */
    categorySaleNumRate?: number;
    /**
     * 销售成本金额
     */
    costAmount?: number;
    /**
     * 客户id
     */
    customerId?: string;
    /**
     * 客户名称
     */
    customerName?: string;
    /**
     * 员工id
     */
    employeeId?: string;
    /**
     * 员工名称
     */
    employeeName?: string;
    /**
     * 销售毛利率
     */
    grossProfitRate?: number;
    /**
     * 商品名称
     */
    itemName?: string;
    /**
     * 商品编码
     */
    itemSn?: string;
    /**
     * 订单号
     */
    orderNo?: string;
    /**
     * 销售金额（正向）
     */
    orderSaleAmount?: number;
    /**
     * 订单方向1-正向2-逆向
     */
    orderType?: number;
    /**
     * 客单价（销售金额（正向）销售订单数（正向））
     */
    perCstTrans?: number;
    /**
     * 销售金额
     */
    saleAmount?: number;
    /**
     * 销售客户数（正向)
     */
    saleCustomerNum?: number;
    /**
     * 销售毛利金额
     */
    saleGrossProfit?: number;
    /**
     * 销售数量
     */
    saleNum?: number;
    /**
     * 销售单数（正向）
     */
    saleOrderNum?: number;
    /**
     * 销售商品数（正向）
     */
    saleSaleNum?: number;
    /**
     * 门店id
     */
    storeId?: string;
    /**
     * 门店名称
     */
    storeName?: string;
    /**
     * 数据更新时间
     */
    updateTime?: string;
}
