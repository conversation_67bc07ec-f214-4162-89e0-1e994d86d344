import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { QueryPostListEntity } from '../types/query.post.list.response.entity';

export interface PostListForGoodsTableColumnsProps { }

export const PostListForGoodsTableColumns = () => {
  const intl = useIntl();

  return [
    {
      title: intl.formatMessage({ id: 'report.sales.table.goods.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.goods.itemName' }),
      dataIndex: 'itemName',
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.goods.brandName' }),
      dataIndex: 'brandName',
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.goods.categoryName' }),
      dataIndex: 'categoryName',
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.goods.saleAmount' }),
      dataIndex: 'saleAmount',
      order: 1,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.goods.costAmount' }),
      dataIndex: 'costAmount',
      order: 2,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.goods.saleGrossProfit' }),
      dataIndex: 'saleGrossProfit',
      order: 3,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.goods.grossProfitRate' }),
      dataIndex: 'grossProfitRate',
      order: 4,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.goods.saleSaleNum' }),
      dataIndex: 'saleSaleNum',
      order: 5,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.goods.avgSaleAmount' }),
      dataIndex: 'avgSaleAmount',
      order: 6,
      sorter: true,
    },
  ] as ProColumns<QueryPostListEntity>[];
};
