import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { QueryPostListEntity } from '../types/query.post.list.response.entity';

export interface PostListForCustomTableColumnsProps { }

export const PostListForCustomTableColumns = () => {
  const intl = useIntl();

  return [
    {
      title: intl.formatMessage({ id: 'report.sales.table.customer.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.customer.customerName' }),
      dataIndex: 'customerName',
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.customer.saleAmount' }),
      dataIndex: 'saleAmount',
      order: 1,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.customer.costAmount' }),
      dataIndex: 'costAmount',
      order: 2,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.customer.saleGrossProfit' }),
      dataIndex: 'saleGrossProfit',
      order: 3,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.customer.grossProfitRate' }),
      dataIndex: 'grossProfitRate',
      order: 4,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.customer.saleOrderNum' }),
      dataIndex: 'saleOrderNum',
      order: 5,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.customer.perCstTrans' }),
      dataIndex: 'perCstTrans',
      order: 6,
      sorter: true,
    },
  ] as ProColumns<QueryPostListEntity>[];
};
