import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { QueryPostListEntity } from '../types/query.post.list.response.entity';

export interface PostListForStaffTableColumnsProps { }

export const PostListForStaffTableColumns = () => {
  const intl = useIntl();

  return [
    {
      title: intl.formatMessage({ id: 'report.sales.table.staff.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.staff.employeeName' }),
      dataIndex: 'employeeName',
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.staff.saleAmount' }),
      dataIndex: 'saleAmount',
      order: 1,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.staff.costAmount' }),
      dataIndex: 'costAmount',
      order: 2,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.staff.saleGrossProfit' }),
      dataIndex: 'saleGrossProfit',
      order: 3,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.staff.grossProfitRate' }),
      dataIndex: 'grossProfitRate',
      order: 4,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.staff.saleOrderNum' }),
      dataIndex: 'saleOrderNum',
      order: 5,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.sales.table.staff.perCstTrans' }),
      dataIndex: 'perCstTrans',
      order: 6,
      sorter: true,
    },
  ] as ProColumns<QueryPostListEntity>[];
};
