import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import type { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { QueryPostListRequest } from './types/query.post.list.request.entity';
import type { QueryPostListEntity } from './types/query.post.list.response.entity';
import type {
  SalesGroupByBrand,
  SalesGroupByCategory,
  SalesOverview,
  SalesTrendList,
} from './types/query.post.response.entity';

export const querySalesReportGroupPage = async (
  params: Partial<QueryPostListRequest> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<QueryPostListEntity>>(
    `/ipmsconsole/console/ReportFacade/querySalesReportGroupPage`,
    {
      data: params,
    },
  );
};

/**
 * 销售趋势
 * @param params
 * @returns
 */
export const querySalesTrendList = async (params: Partial<QueryPostListRequest>) => {
  return request<SalesTrendList>(`/ipmsconsole/console/ReportFacade/querySalesTrendList`, {
    data: params,
  });
};

/**
 *销售概览
 * @param params
 * @returns
 */
export const getSalesOverview = async (params: Partial<QueryPostListRequest>) => {
  return request<SalesOverview>(`/ipmsconsole/console/ReportFacade/getSalesOverview`, {
    data: params,
  });
};

/**
 *品牌占比
 * @param params
 * @returns
 */
export const getSalesGroupByBrand = async (params: Partial<QueryPostListRequest>) => {
  return request<SalesGroupByBrand>(`/ipmsconsole/console/ReportFacade/getSalesGroupByBrand`, {
    data: params,
  });
};

/**
 *品类占比
 * @param params
 * @returns
 */
export const getSalesGroupByCategory = async (params: Partial<QueryPostListRequest>) => {
  return request<SalesGroupByCategory>(
    `/ipmsconsole/console/ReportFacade/getSalesGroupByCategory`,
    {
      data: params,
    },
  );
};
