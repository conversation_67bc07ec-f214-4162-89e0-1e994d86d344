import { PageContainerProps } from '@ant-design/pro-components';
import { history } from '@umijs/max';

const config = (hasButtonPerms: any, intl: any) => {
  const tabList = [];

  if (hasButtonPerms('/report/sales/view')) {
    tabList.push({
      tab: intl.formatMessage({ id: 'report.sales.title' }),
      key: '/report/sales',
    });
  }

  if (hasButtonPerms('/report/purchase/view')) {
    tabList.push({
      tab: intl.formatMessage({ id: 'report.purchase.title' }),
      key: '/report/purchase',
    });
  }

  if (hasButtonPerms('/report/inventory/view')) {
    tabList.push({
      tab: intl.formatMessage({ id: 'report.inventory.title' }),
      key: '/report/inventory',
    });
  }

  if (hasButtonPerms('/report/finance/view')) {
    tabList.push({
      tab: intl.formatMessage({ id: 'report.finance.title' }),
      key: '/report/finance',
    });
  }

  return {
    fixedHeader: true,
    onTabChange: (key: string) => {
      history.push(key);
    },
    tabList,
    token: {
      paddingInlinePageContainerContent: 20,
      paddingBlockPageContainerContent: 0,
    },
  } as PageContainerProps;
};

export default config;
