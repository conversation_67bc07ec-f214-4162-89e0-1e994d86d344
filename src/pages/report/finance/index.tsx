import NumberFormatText from '@/components/common/NumberFormatText';
import SubTitle from '@/components/common/SubTitle';
import PieCharts from '@/components/pieCharts';
import { miniMpAntdTheme } from '@/pages/report/config/miniMpAntdTheme';
import { queryStoreByAccount } from '@/pages/system/user/services';
import isFramed from '@/utils/isFramed';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { useAccess } from '@@/exports';
import type { DualAxesConfig } from '@ant-design/charts';
import { DualAxes } from '@ant-design/charts';
import {
  PageContainer,
  PageContainerProps,
  ProCard,
  ProFormDatePicker,
  ProFormInstance,
  ProFormSelect,
  QueryFilter,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { But<PERSON>, ConfigProvider, Flex } from 'antd';
import dayjs from 'dayjs';
import { defaultTo, forEach, forIn, isEmpty } from 'lodash';
import { useRef, useState } from 'react';
import PageConfig from '../config/PageConfig';
import type { PieType } from '../sales/types/ChartsType';
import {
  getFinanceReportOverview,
  queryFinanceInList,
  queryFinanceOutList,
  queryIncomeExpendituresTrendList,
  queryReceivablePayableList,
} from './services';
import type { QueryPostRequest } from './types/query.post.request.entity';

const getTitleMap = (intl: any): Record<string, string> => ({
  fundIncomeExpenditures: intl.formatMessage({ id: 'report.finance.overview.fundIncome' }),
  fundExpenditures: intl.formatMessage({ id: 'report.finance.overview.fundExpenditures' }),
  earnedProfit: intl.formatMessage({ id: 'report.finance.overview.earnedProfit' }),
});
type ViewType = { key: string; title: string; value: string };
type TrendType = { date: string; value: number | null; type: string };

const FinanceReport = () => {
  const intl = useIntl();
  const formRef = useRef<ProFormInstance>();
  const [viewList, setViewList] = useState<ViewType[]>([
    {
      key: 'fundIncomeExpenditures',
      title: intl.formatMessage({ id: 'report.finance.overview.fundIncome' }),
      value: '-',
    },
    {
      key: 'fundExpenditures',
      title: intl.formatMessage({ id: 'report.finance.overview.fundExpenditures' }),
      value: '-',
    },
    {
      key: 'earnedProfit',
      title: intl.formatMessage({ id: 'report.finance.overview.earnedProfit' }),
      value: '-',
    },
  ]);

  const { hasButtonPerms } = useAccess();

  const [fundIncomeExpendituresData, setFundIncomeExpendituresData] = useState<TrendType[]>([]);
  const [fundExpendituresData, setFundExpendituresData] = useState<TrendType[]>([]);
  const [receivableAmountData, setReceivableAmountData] = useState<TrendType[]>([]);
  const [payableAmountData, setPayableAmountData] = useState<TrendType[]>([]);
  const [financeInList, setFinanceInList] = useState<PieType[]>([]);
  const [financeOutList, setFinanceOutList] = useState<PieType[]>([]);

  useAsyncEffect(async () => {
    formRef?.current?.submit();
  }, []);

  const queryFinanceReportOverview = async (values: QueryPostRequest) => {
    const overview = await getFinanceReportOverview({ ...values });
    if (!isEmpty(overview)) {
      //财务概览
      const viewObjList: ViewType[] = [];
      const titleMap = getTitleMap(intl);
      forIn(titleMap, (value, key) => {
        if (key === 'fundIncomeExpenditures') {
          viewObjList.push({
            key,
            title: value,
            value: defaultTo(overview?.['fundIncome'], '-'),
          });
        } else {
          viewObjList.push({
            key,
            title: value,
            value: defaultTo(overview?.[key], '-'),
          });
        }
      });
      setViewList(viewObjList);
    }
  };

  const queryIncomeExpendituresTrendListData = async (values: QueryPostRequest) => {
    const incomeExpendituresTrendList = await queryIncomeExpendituresTrendList({ ...values });
    //收支趋势
    const fundIncomeExpendituresDataList: TrendType[] = [];
    const fundExpendituresDataList: TrendType[] = [];
    if (incomeExpendituresTrendList && incomeExpendituresTrendList.length > 0) {
      forEach(incomeExpendituresTrendList, (item) => {
        const { bizTime, fundIncome, fundExpenditures } = item;
        fundIncomeExpendituresDataList.push({
          type: intl.formatMessage({ id: 'report.finance.trends.fundIncome' }),
          value: fundIncome ?? 0,
          date: bizTime,
        });
        fundExpendituresDataList.push({
          type: intl.formatMessage({ id: 'report.finance.trends.fundExpenditures' }),
          value: fundExpenditures ?? 0,
          date: bizTime,
        });
      });
    }
    setFundIncomeExpendituresData(fundIncomeExpendituresDataList);
    setFundExpendituresData(fundExpendituresDataList);
  };

  const queryReceivablePayableListData = async (values: QueryPostRequest) => {
    const receivablePayableList = await queryReceivablePayableList({ ...values });
    //应收应付
    const receivableAmountDataList: TrendType[] = [];
    const payableAmountDataList: TrendType[] = [];
    if (receivablePayableList && receivablePayableList.length > 0) {
      forEach(receivablePayableList, (item) => {
        const { bizTime, receivableAmount, payableAmount } = item;
        receivableAmountDataList.push({
          type: intl.formatMessage({ id: 'report.finance.trends.receivableAmount' }),
          value: receivableAmount ?? 0,
          date: bizTime,
        });
        payableAmountDataList.push({
          type: intl.formatMessage({ id: 'report.finance.trends.payableAmount' }),
          value: payableAmount ?? 0,
          date: bizTime,
        });
      });
    }
    setReceivableAmountData(receivableAmountDataList);
    setPayableAmountData(payableAmountDataList);
  };

  const queryFinanceInListData = async (values: QueryPostRequest) => {
    const res = await queryFinanceInList({ ...values });
    let financeInListData: PieType[] = [];
    if (res && res.length > 0) {
      financeInListData = res.map((item) => {
        return {
          type: item.orderTypeDesc,
          value: item.fundIncomeExpenditures,
          percent: item.fundIncomeExpendituresTypeRate,
        };
      });
    }
    setFinanceInList(financeInListData);
  };

  const queryFinanceOutListData = async (values: QueryPostRequest) => {
    const res = await queryFinanceOutList({ ...values });
    let financeOutListData: PieType[] = [];
    if (res && res.length > 0) {
      financeOutListData = res.map((item) => {
        return {
          type: item.orderTypeDesc,
          value: item.fundIncomeExpenditures,
          percent: item.fundIncomeExpendituresTypeRate,
        };
      });
    }
    setFinanceOutList(financeOutListData);
  };
  const queryFinanceReport = (values: QueryPostRequest) => {
    queryFinanceReportOverview(values);
    queryIncomeExpendituresTrendListData(values);
    queryReceivablePayableListData(values);
    queryFinanceInListData(values);
    queryFinanceOutListData(values);
  };

  //查询
  const onSearch = async (values: QueryPostRequest) => {
    await queryFinanceReport(values);
  };

  const config: DualAxesConfig = {
    xField: 'date',
    yField: 'value',
    legend: {
      color: {
        layout: {
          justifyContent: 'center',
          alignItems: 'center',
        },
      },
    },
    children: [
      {
        seriesField: 'type',
        colorField: 'type',
        data: [...fundIncomeExpendituresData, ...fundExpendituresData],
        type: 'line',
        shapeField: 'smooth',
        axis: {
          x: {
            size: fundIncomeExpendituresData.length > 20 ? 0 : 120,
          },
          y: { position: 'left', title: intl.formatMessage({ id: 'report.finance.axis.yuan' }), titlePosition: 'top' },
        },
      },
    ],
  };

  const receivablePayableConfig: DualAxesConfig = {
    xField: 'date',
    yField: 'value',
    legend: {
      color: {
        layout: {
          justifyContent: 'center',
          alignItems: 'center',
        },
      },
    },
    children: [
      {
        seriesField: 'type',
        colorField: 'type',
        data: [...receivableAmountData, ...payableAmountData],
        type: 'line',
        shapeField: 'smooth',
        axis: {
          x: {
            size: receivableAmountData.length > 20 ? 0 : 120,
          },
          y: { position: 'left', title: intl.formatMessage({ id: 'report.finance.axis.yuan' }), titlePosition: 'top' },
        },
      },
    ],
  };
  let pConfig: Partial<PageContainerProps> = {};
  //@ts-ignore
  if (isFramed()) {
    pConfig = PageConfig(hasButtonPerms, intl);
  }

  return (
    <ConfigProvider
      //@ts-ignore
      theme={isFramed() ? miniMpAntdTheme : null}
    >
      <PageContainer {...pConfig} tabActiveKey={location.pathname}>
        <Flex vertical={true} wrap="wrap" gap={16}>
          <ProCard headerBordered>
            <QueryFilter
              onFinish={onSearch}
              submitter={false}
              formRef={formRef}
              className="!p-0"
              defaultColsNumber={4}
            >
              <ProFormSelect
                name="storeIdList"
                label={intl.formatMessage({ id: 'report.finance.form.store' })}
                mode={'multiple'}
                fieldProps={{ fieldNames: { label: 'name', value: 'id' } }}
                request={() => queryStoreByAccount({})}
              />
              <ProFormDatePicker
                label={intl.formatMessage({ id: 'report.finance.form.startTime' })}
                initialValue={dayjs().subtract(29, 'days').startOf('day')}
                allowClear={false}
                name="startTime"
              />
              <ProFormDatePicker
                label={intl.formatMessage({ id: 'report.finance.form.endTime' })}
                initialValue={dayjs().endOf('day')}
                allowClear={false}
                name="endTime"
              />
              <Flex align="center" gap={8}>
                <Button type="primary" htmlType="submit">
                  {intl.formatMessage({ id: 'report.finance.form.query' })}
                </Button>
                <Button type="primary" ghost htmlType="reset">
                  {intl.formatMessage({ id: 'report.finance.form.reset' })}
                </Button>
              </Flex>
            </QueryFilter>
          </ProCard>
          <ProCard headerBordered wrap>
            <Flex wrap="wrap" className="mt-6 px-6" gap={16} justify="space-between">
              {viewList &&
                viewList.map((viewItem) => (
                  <div key={viewItem.title} className="flex flex-col">
                    <span className="text-[#00000073]">
                      <span className="mr-2">{viewItem.title}</span>
                    </span>
                    <span className="text-[#000000D9] text-[24px] font-semibold mt-1 mb-2 w-[200px]">
                      <NumberFormatText precision={2} numberText={viewItem.value} />
                    </span>
                  </div>
                ))}
            </Flex>
          </ProCard>
          <ProCard title={<SubTitle text={intl.formatMessage({ id: 'report.finance.charts.incomeExpendituresTrend' })} />}>
            <DualAxes {...config} />
          </ProCard>
          <ProCard wrap gutter={[8, 8]} title={<SubTitle text={intl.formatMessage({ id: 'report.finance.charts.incomeExpendituresRatio' })} />}>
            <ProCard ghost={true} colSpan={{ xs: 24, md: 12 }} title={intl.formatMessage({ id: 'report.finance.charts.fundIncomeChart' })}>
              <PieCharts data={financeInList} />
            </ProCard>
            <ProCard ghost={true} colSpan={{ xs: 24, md: 12 }} title={intl.formatMessage({ id: 'report.finance.charts.fundExpendituresChart' })}>
              <PieCharts data={financeOutList} />
            </ProCard>
          </ProCard>
          <ProCard title={<SubTitle text={intl.formatMessage({ id: 'report.finance.charts.receivablePayableTrend' })} />}>
            <DualAxes {...receivablePayableConfig} />
          </ProCard>
        </Flex>
      </PageContainer>
    </ConfigProvider>
  );
};

export default withKeepAlive(FinanceReport);
