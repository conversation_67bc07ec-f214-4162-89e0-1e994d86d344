import type { PaginationRequest } from "@/types/PaginationRequest";

export interface QueryPostRequest extends PaginationRequest {
  /**
    * 当前登录人账号id
    */
  accountId?: string;
  /**
   * 结束时间
   */
  endTime?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * None
   */
  operatorName?: string;
  /**
   * None
   */
  operatorNo?: string;
  /**
   * None
   */
  startRow?: number;
  /**
   * 开始时间
   */
  startTime?: string;
  /**
   * 门店列表
   */
  storeIdList?: string[];
}