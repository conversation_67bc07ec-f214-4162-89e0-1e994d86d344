import { request } from '@/utils/request';
import type { QueryPostRequest } from './types/query.post.request.entity';
import type {
  FinanceInList,
  FinanceOutList,
  IncomeExpendituresTrendList,
  Overview,
  ReceivablePayableList,
} from './types/query.post.response.entity';

export const getFinanceReportOverview = async (params: Partial<QueryPostRequest>) => {
  return request<Overview>(`/ipmsconsole/console/ReportFacade/getFinanceReportOverview`, {
    data: params,
  });
};

export const queryIncomeExpendituresTrendList = async (params: Partial<QueryPostRequest>) => {
  return request<IncomeExpendituresTrendList>(
    `/ipmsconsole/console/ReportFacade/queryIncomeExpendituresTrendList`,
    {
      data: params,
    },
  );
};

export const queryFinanceInList = async (params: Partial<QueryPostRequest>) => {
  return request<FinanceInList>(`/ipmsconsole/console/ReportFacade/queryFinanceInList`, {
    data: params,
  });
};

export const queryFinanceOutList = async (params: Partial<QueryPostRequest>) => {
  return request<FinanceOutList>(`/ipmsconsole/console/ReportFacade/queryFinanceOutList`, {
    data: params,
  });
};

export const queryReceivablePayableList = async (params: Partial<QueryPostRequest>) => {
  return request<ReceivablePayableList>(
    `/ipmsconsole/console/ReportFacade/queryReceivablePayableList`,
    {
      data: params,
    },
  );
};
