import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { CheckPostEntity } from '../types/transfer.post.entity';

export interface PostListTableColumnsProps { }

export const PostListTableColumns = () => {
  const intl = useIntl();

  return [
    {
      title: intl.formatMessage({ id: 'report.inventory.table.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.itemName' }),
      dataIndex: 'itemName',
      width: 140,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.brandName' }),
      dataIndex: 'brandName',
      width: 160,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.categoryName' }),
      dataIndex: 'categoryName',
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.stockTurnDays' }),
      dataIndex: 'stockTurnDays',
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.saleOutNum' }),
      dataIndex: 'saleOutNum',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.saleInNum' }),
      dataIndex: 'saleInNum',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.purchaseInNum' }),
      dataIndex: 'purchaseInNum',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.purchaseOutNum' }),
      dataIndex: 'purchaseOutNum',
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.checkGainNum' }),
      dataIndex: 'checkGainNum',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.checkLossNum' }),
      dataIndex: 'checkLossNum',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.saleOutAmount' }),
      dataIndex: 'saleOutAmount',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.saleInAmount' }),
      dataIndex: 'saleInAmount',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.purchaseInAmount' }),
      dataIndex: 'purchaseInAmount',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.purchaseOutAmount' }),
      dataIndex: 'purchaseOutAmount',
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.checkGainAmount' }),
      dataIndex: 'checkGainAmount',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'report.inventory.table.checkLossAmount' }),
      dataIndex: 'checkLossAmount',
      width: 80,
    },
  ] as ProColumns<CheckPostEntity>[];
};
