export interface QueryPostListEntity {
  /**
   * 业务时间（订单提交时间）
   */
  bizTime?: string;
  /**
   * 品牌id
   */
  brandId?: string;
  /**
   * 品牌名称
   */
  brandName?: string;
  /**
   * 品类id
   */
  categoryId?: string;
  /**
   * 品类名称
   */
  categoryName?: string;
  /**
   * 盘盈金额
   */
  checkGainAmount?: number;
  /**
   * 盘盈数量
   */
  checkGainNum?: number;
  /**
   * 盘亏金额
   */
  checkLossAmount?: number;
  /**
   * 盘亏数量
   */
  checkLossNum?: number;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 采购入库金额
   */
  purchaseInAmount?: number;
  /**
   * 采购入库数量
   */
  purchaseInNum?: number;
  /**
   * 采购出库金额
   */
  purchaseOutAmount?: number;
  /**
   * 采购出库数量
   */
  purchaseOutNum?: number;
  /**
   * 入库金额
   */
  saleInAmount?: number;
  /**
   * 入库数量
   */
  saleInNum?: number;
  /**
   * 出库金额
   */
  saleOutAmount?: number;
  /**
   * 出库数量
   */
  saleOutNum?: number;
  /**
   * 库存周转天数范围
   */
  stockTurnDays?: string;
  /**
   * 门店id
   */
  storeId?: string;
  /**
   * 门店名称
   */
  storeName?: string;
  /**
   * 数据更新时间
   */
  updateTime?: string;
}
