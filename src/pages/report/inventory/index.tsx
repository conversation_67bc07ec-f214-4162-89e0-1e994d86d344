import FunProTable from '@/components/common/FunProTable';
import NumberFormatText from '@/components/common/NumberFormatText';
import SubTitle from '@/components/common/SubTitle';
import { queryGoodsPropertyPage } from '@/pages/goods/property/services';
import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import { miniMpAntdTheme } from '@/pages/report/config/miniMpAntdTheme';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import isFramed from '@/utils/isFramed';
import { transformCategoryTree } from '@/utils/transformCategoryTree';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ActionType, PageContainerProps, ProFormInstance } from '@ant-design/pro-components';
import {
  PageContainer,
  ProCard,
  ProFormDatePicker,
  ProFormSelect,
  ProFormText,
  ProFormTreeSelect,
  QueryFilter,
} from '@ant-design/pro-components';
import { useAccess, useIntl, useModel } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Button, ConfigProvider, Flex, Space } from 'antd';
import dayjs from 'dayjs';
import { defaultTo, forEach, forIn, isEmpty } from 'lodash';
import { useRef, useState } from 'react';
import PieCharts from '../../../components/pieCharts';
import PageConfig from '../config/PageConfig';
import type { PieType } from '../sales/types/ChartsType';
import { PostListTableColumns } from './config/postListTableColumns';
import {
  getInvReportOverview,
  queryInvReportPage,
  queryInvStockTurnDaysReportList,
} from './services';
import type { QueryPostListRequest } from './types/query.post.list.request.entity';
import type { QueryPostListEntity } from './types/query.post.list.response.entity';
const getTitleMap = (intl: any): Record<string, string> => ({
  inNum: intl.formatMessage({ id: 'report.inventory.overview.inNum' }),
  inAmount: intl.formatMessage({ id: 'report.inventory.overview.inAmount' }),
  outNum: intl.formatMessage({ id: 'report.inventory.overview.outNum' }),
  outAmount: intl.formatMessage({ id: 'report.inventory.overview.outAmount' }),
  stockTurnDays: intl.formatMessage({ id: 'report.inventory.overview.stockTurnDays' }),
});
type ViewType = { key: string; title: string; value: string };
const InventoryReport = () => {
  const intl = useIntl();
  const { initialState } = useModel('@@initialState');
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [viewList, setViewList] = useState<ViewType[]>([
    {
      key: 'inNum',
      title: intl.formatMessage({ id: 'report.inventory.overview.inNum' }),
      value: '-',
    },
    {
      key: 'inAmount',
      title: intl.formatMessage({ id: 'report.inventory.overview.inAmount' }),
      value: '-',
    },
    {
      key: 'outNum',
      title: intl.formatMessage({ id: 'report.inventory.overview.outNum' }),
      value: '-',
    },
    {
      key: 'outAmount',
      title: intl.formatMessage({ id: 'report.inventory.overview.outAmount' }),
      value: '-',
    },
    {
      key: 'stockTurnDays',
      title: intl.formatMessage({ id: 'report.inventory.overview.stockTurnDays' }),
      value: '-',
    },
  ]);
  const [pieData, setPieData] = useState<PieType[]>([]);

  const { hasButtonPerms } = useAccess();

  const [queryParams, setQueryParams] = useState<QueryPostListRequest>({
    startTime: dayjs().subtract(29, 'days').startOf('day').format('YYYY-MM-DD'),
    endTime: dayjs().endOf('day').format('YYYY-MM-DD'),
  });

  useAsyncEffect(async () => {
    await queryInventoryReport(queryParams);
  }, []);

  const queryInvReportOverview = async (values: QueryPostListRequest) => {
    const overview = await getInvReportOverview({ ...values });
    if (!isEmpty(overview)) {
      //库存概览
      const viewObjList: ViewType[] = [];
      const titleMap = getTitleMap(intl);
      forIn(titleMap, (value, key) => {
        viewObjList.push({
          key,
          title: value,
          value: defaultTo(overview?.[key], '-'),
        });
      });
      setViewList(viewObjList);
    }
  };
  const queryInvStockTurnDaysReportListData = async (values: QueryPostListRequest) => {
    const stockTurnDaysReportList = await queryInvStockTurnDaysReportList({ ...values });
    //库存天数分布
    const pieDataList: PieType[] = [];
    if (stockTurnDaysReportList && stockTurnDaysReportList.length > 0) {
      forEach(stockTurnDaysReportList, (item) => {
        const { stockTurnDistribute, stockTurnCnt, stockTurnRate } = item;
        pieDataList.push({
          type: stockTurnDistribute,
          value: stockTurnCnt,
          percent: stockTurnRate,
        });
      });
    }
    console.log(1111, pieDataList);
    setPieData(pieDataList);
  };

  const queryInventoryReport = (values: QueryPostListRequest) => {
    queryInvReportOverview(values);
    queryInvStockTurnDaysReportListData(values);
  };

  //查询
  const onSearch = async (values: QueryPostListRequest) => {
    setQueryParams(values);
    await queryInventoryReport(values);
    actionRef.current?.reload(true);
  };
  let pConfig: Partial<PageContainerProps> = {};
  //@ts-ignore
  if (isFramed()) {
    pConfig = PageConfig(hasButtonPerms, intl);
  }
  return (
    <ConfigProvider
      //@ts-ignore
      theme={isFramed() ? miniMpAntdTheme : null}
    >
      <PageContainer {...pConfig} tabActiveKey={location.pathname}>
        <Flex vertical={true} wrap="wrap" gap={16}>
          <ProCard headerBordered>
            <QueryFilter
              onFinish={onSearch}
              submitter={false}
              formRef={formRef}
              className="!p-0"
              defaultColsNumber={4}
            >
              <ProFormSelect
                name="warehouseIdList"
                label={intl.formatMessage({ id: 'report.inventory.form.warehouse' })}
                fieldProps={{ fieldNames: { label: 'warehouseName', value: 'warehouseId' } }}
                mode={'multiple'}
                request={() => {
                  return warehouseList({ state: YesNoStatus.YES }).then((s) => {
                    return s.warehouseStoreRelationRoList ?? [];
                  });
                }}
              />
              <ProFormText
                label={intl.formatMessage({ id: 'report.inventory.form.goodsInfo' })}
                name="itemFuzzy"
                placeholder={intl.formatMessage({ id: 'report.inventory.form.goodsInfoPlaceholder' })}
              />
              <ProFormSelect
                label={intl.formatMessage({ id: 'report.inventory.form.brand' })}
                name="brandIdList"
                showSearch={true}
                debounceTime={300}
                mode={'multiple'}
                fieldProps={{
                  filterOption: false,
                  maxTagCount: 3,
                  optionRender: (option) => (
                    <Space>
                      {option.data.label}
                    </Space>
                  ),
                }}
                request={(query) => {
                  const params: any = {
                    brandStatus: '1',
                    pageSize: 99,
                    pageNo: 1,
                    brandName: query.keyWords,
                  };
                  return queryGoodsPropertyPage(params, 'brand').then((result) =>
                    result.data?.map((item) => ({
                      label: item.brandName,
                      dataType: item.dataType,
                      value: item.brandId,
                    })),
                  );
                }}
              />
              <ProFormTreeSelect
                label={intl.formatMessage({ id: 'report.inventory.form.category' })}
                name="categoryIdList"
                fieldProps={{
                  maxTagCount: 3,
                  treeCheckable: true,
                  filterTreeNode: (text, treeNode) => treeNode.text?.includes(text),
                }}
                request={() => {
                  const params: any = {
                    categoryStatus: 1,
                    pageSize: 999,
                    pageNo: 1,
                    isReturnTree: true,
                  };
                  return queryGoodsPropertyPage(params, 'category').then((result) =>
                    transformCategoryTree(result.data),
                  );
                }}
              />
              <ProFormDatePicker
                label={intl.formatMessage({ id: 'report.inventory.form.startTime' })}
                initialValue={dayjs().subtract(29, 'days').startOf('day')}
                allowClear={false}
                name="startTime"
              />
              <ProFormDatePicker
                label={intl.formatMessage({ id: 'report.inventory.form.endTime' })}
                initialValue={dayjs().endOf('day')}
                allowClear={false}
                name="endTime"
              />
              <Flex align="center" gap={8}>
                <Button type="primary" htmlType="submit">
                  {intl.formatMessage({ id: 'report.inventory.form.query' })}
                </Button>
                <Button type="primary" ghost htmlType="reset">
                  {intl.formatMessage({ id: 'report.inventory.form.reset' })}
                </Button>
              </Flex>
            </QueryFilter>
          </ProCard>
          <ProCard headerBordered>
            <Flex className="mt-6 px-6" justify="space-between" gap={8} wrap="wrap">
              {viewList &&
                viewList.map((viewItem) => (
                  <div key={viewItem.title} className="flex flex-col">
                    <span className="text-[#00000073]">
                      <span className="mr-2">{viewItem.title}</span>
                    </span>
                    <span className="text-[#000000D9] text-[24px] font-semibold mt-1 mb-2 w-[200px]">
                      <NumberFormatText
                        precision={
                          viewItem.key == 'inAmount' || viewItem.key == 'outAmount' ? 2 : 0
                        }
                        numberText={viewItem.value}
                      />
                    </span>
                  </div>
                ))}
            </Flex>
          </ProCard>
          <ProCard
            title={
              <SubTitle
                text={
                  <div>
                    {intl.formatMessage({ id: 'report.inventory.charts.stockTurnDaysDistribution' })}
                    <span className="text-[14px] ml-[20px] font-normal text-gray-500">
                      {intl.formatMessage({ id: 'report.inventory.charts.stockTurnDaysFormula' })}
                    </span>
                  </div>
                }
              />
            }
          >
            <PieCharts data={pieData} />
          </ProCard>
          <ProCard title={<SubTitle text={intl.formatMessage({ id: 'report.inventory.charts.inventoryChangeStatistics' })} />}>
            <FunProTable<QueryPostListEntity, any>
              ghost={true}
              rowKey="itemSn"
              actionRef={actionRef}
              options={false}
              search={false}
              scroll={{ x: 'max-content' }}
              params={{
                ...queryParams,
              }}
              requestPage={async (params, sort) => {
                if (params.startTime && params.endTime) {
                  const sortField = Object.values(sort)[0];
                  let sortType;
                  if (sortField) {
                    sortType = sortField === 'ascend' ? 'ASC' : 'DESC';
                  } else {
                    sortType = undefined;
                  }
                  return await queryInvReportPage({
                    ...params,
                    sortBy: Object.keys(sort)[0] ?? undefined,
                    sortType,
                  });
                }
                return { data: [], total: 0 };
              }}
              columns={PostListTableColumns()}
            />
          </ProCard>
        </Flex>
      </PageContainer>
    </ConfigProvider>
  );
};

export default withKeepAlive(InventoryReport);
