import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import type { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { QueryPostListRequest } from './types/query.post.list.request.entity';
import type { QueryPostListEntity } from './types/query.post.list.response.entity';
import type { Overview, StockTurnDaysReportList } from './types/query.post.response.entity';

export const queryInvReportPage = async (
  params: Partial<QueryPostListRequest> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<QueryPostListEntity>>(
    `/ipmsconsole/console/ReportFacade/queryInvReportPage`,
    {
      data: params,
    },
  );
};

export const getInvReportOverview = async (params: Partial<QueryPostListRequest>) => {
  return request<Overview>(`/ipmsconsole/console/ReportFacade/getInvReportOverview`, {
    data: params,
  });
};

export const queryInvStockTurnDaysReportList = async (params: Partial<QueryPostListRequest>) => {
  return request<StockTurnDaysReportList>(
    `/ipmsconsole/console/ReportFacade/queryInvStockTurnDaysReportList`,
    {
      data: params,
    },
  );
};
