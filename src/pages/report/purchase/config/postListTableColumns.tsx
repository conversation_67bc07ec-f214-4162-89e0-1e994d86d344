import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { CheckPostEntity } from '../types/transfer.post.entity';

export interface PostListTableColumnsProps { }

export const PostListTableColumns = () => {
  const intl = useIntl();

  return [
    {
      title: intl.formatMessage({ id: 'report.purchase.table.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'report.purchase.table.supplierName' }),
      dataIndex: 'supplierName',
      width: 220,
    },
    {
      title: intl.formatMessage({ id: 'report.purchase.table.purchaseAmount' }),
      dataIndex: 'purchaseAmount',
      order: 1,
      sorter: true,
      width: 120,
    },
    {
      title: intl.formatMessage({ id: 'report.purchase.table.purchaseOrderNum' }),
      dataIndex: 'purchaseOrderNum',
      order: 2,
      sorter: true,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'report.purchase.table.purchaseNum' }),
      dataIndex: 'purchaseNum',
      order: 3,
      sorter: true,
      width: 100,
    },
  ] as ProColumns<CheckPostEntity>[];
};
