import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { CheckPostEntity } from '../types/transfer.post.entity';

export interface PostListForGoodsTableColumnsProps { }

export const PostListForGoodsTableColumns = () => {
  const intl = useIntl();

  return [
    {
      title: intl.formatMessage({ id: 'report.purchase.table.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'report.purchase.table.itemName' }),
      dataIndex: 'itemName',
    },
    {
      title: intl.formatMessage({ id: 'report.purchase.table.brandName' }),
      dataIndex: 'brandName',
    },
    {
      title: intl.formatMessage({ id: 'report.purchase.table.categoryName' }),
      dataIndex: 'categoryName',
    },
    {
      title: intl.formatMessage({ id: 'report.purchase.table.purchaseAmount' }),
      dataIndex: 'purchaseAmount',
      order: 1,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.purchase.table.purchaseNum' }),
      dataIndex: 'purchaseNum',
      order: 2,
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'report.purchase.table.avgPurchaseAmount' }),
      dataIndex: 'avgPurchaseAmount',
      order: 3,
      sorter: true,
    },
  ] as ProColumns<CheckPostEntity>[];
};
