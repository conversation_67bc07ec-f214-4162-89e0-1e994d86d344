import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import type { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { QueryPostListRequest } from './types/query.post.list.request.entity';
import type { QueryPostListEntity } from './types/query.post.list.response.entity';
import type {
  PurchaseGroupByBrand,
  PurchaseGroupByCategory,
  PurchaseOverview,
  PurchaseTrendList,
} from './types/query.post.response.entity';

export const queryPurchaseReportGroupPage = async (
  params: Partial<QueryPostListRequest> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<QueryPostListEntity>>(
    `/ipmsconsole/console/ReportFacade/queryPurchaseReportGroupPage`,
    {
      data: params,
    },
  );
};

/**
 * 采购报表-概览
 */
export const getPurchaseOverview = async (params: Partial<QueryPostListRequest>) => {
  return request<PurchaseOverview>(`/ipmsconsole/console/ReportFacade/getPurchaseOverview`, {
    data: params,
  });
};

/**
 * 采购报表-采购趋势
 */
export const queryPurchaseTrendList = async (params: Partial<QueryPostListRequest>) => {
  return request<PurchaseTrendList>(`/ipmsconsole/console/ReportFacade/queryPurchaseTrendList`, {
    data: params,
  });
};

/**
 * 采购报表-品牌占比
 */
export const getPurchaseGroupByBrand = async (params: Partial<QueryPostListRequest>) => {
  return request<PurchaseGroupByBrand>(
    `/ipmsconsole/console/ReportFacade/getPurchaseGroupByBrand`,
    {
      data: params,
    },
  );
};

/**
 * 采购报表-品类占比
 */
export const getPurchaseGroupByCategory = async (params: Partial<QueryPostListRequest>) => {
  return request<PurchaseGroupByCategory>(
    `/ipmsconsole/console/ReportFacade/getPurchaseGroupByCategory`,
    {
      data: params,
    },
  );
};
