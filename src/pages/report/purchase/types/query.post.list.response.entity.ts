export interface QueryPostListEntity {
    /**
     * 采购均价
     */
    avgPurchaseAmount?: number;
    /**
     * 业务时间（订单提交时间）
     */
    bizTime?: string;
    /**
     * 品牌id
     */
    brandId?: string;
    /**
     * 品牌名称
     */
    brandName?: string;
    /**
     * 品牌采购金额
     */
    brandPurchaseAmount?: number;
    /**
     * 品牌采购金额占比
     */
    brandPurchaseAmountRate?: number;
    /**
     * 品牌采购数量
     */
    brandPurchaseNum?: number;
    /**
     * 品牌采购数量占比
     */
    brandPurchaseNumRate?: number;
    /**
     * 品类id
     */
    categoryId?: string;
    /**
     * 品类名称
     */
    categoryName?: string;
    /**
     * 品类采购金额
     */
    categoryPurchaseAmount?: number;
    /**
     * 品类采购金额占比
     */
    categoryPurchaseAmountRate?: number;
    /**
     * 品类采购数量
     */
    categoryPurchaseNum?: number;
    /**
     * 品类采购数量占比
     */
    categoryPurchaseNumRate?: number;
    /**
     * 商品名称
     */
    itemName?: string;
    /**
     * 商品编码
     */
    itemSn?: string;
    /**
     * 采购金额
     */
    purchaseAmount?: number;
    /**
     * 采购单号
     */
    purchaseNo?: string;
    /**
     * 采购数量
     */
    purchaseNum?: number;
    /**
     * 采购订单数
     */
    purchaseOrderNum?: number;
    /**
     * 门店id
     */
    storeId?: string;
    /**
     * 门店名称
     */
    storeName?: string;
    /**
     * 供应商id
     */
    supplierId?: string;
    /**
     * 供应商名称
     */
    supplierName?: string;
    /**
     * 数据更新时间
     */
    updateTime?: string;
}