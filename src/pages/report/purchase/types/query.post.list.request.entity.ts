import type { PaginationRequest } from "@/types/PaginationRequest";

export interface QueryPostListRequest extends PaginationRequest {
  /**
      * 当前登录人账号id
      */
  accountId?: string;
  /**
   * 品牌列表
   */
  brandIdList?: string[];
  /**
   * 品类列表
   */
  categoryIdList?: string[];
  /**
   * 结束时间
   */
  endTime?: string;
  /**
   * 汇总类型
   */
  groupType?: string;
  /**
   * 商品模糊查询，支持商品名称、商品编码
   */
  itemFuzzy?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * None
   */
  operatorName?: string;
  /**
   * None
   */
  operatorNo?: string;
  /**
   * 排序字段
   */
  sortBy?: string;
  /**
   * 排序类型
   */
  sortType?: string;
  /**
   * None
   */
  startRow?: number;
  /**
   * 开始时间
   */
  startTime?: string;
  /**
   * 门店列表
   */
  storeIdList?: string[];
  /**
   * 供应商列表
   */
  supplierIdList?: string[];
}