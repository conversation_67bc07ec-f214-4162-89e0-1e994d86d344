import GoodsSearch from '@/components/GoodsSearch';
import { GoodsSearchBizType } from '@/components/GoodsSearch/types/BizType';
import PaymentForm from '@/components/PaymentForm';
import { PayChannel } from '@/components/PaymentForm/types/PayChannel';
import { PayKind } from '@/components/PaymentForm/types/PayKind';
import { getCstDetail } from '@/pages/customer/list/services';
import type { Address, CustomerSaveEntity } from '@/pages/customer/list/types/CustomerSaveEntity';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import CstDetail from '@/pages/sales/order/edit/compoments/CstDetail';
import DeliveryForm from '@/pages/sales/order/edit/compoments/DeliveryForm';
import DiscountForm from '@/pages/sales/order/edit/compoments/DiscountForm';
import { DeliveryMethod } from '@/pages/sales/order/edit/types/DeliveryMethod';
import { DiscountType } from '@/pages/sales/order/edit/types/DiscountType';
import type { ConfirmPayRequest } from '@/pages/sales/order/edit/types/confirm.pay.request';
import type { UpdateOrderDiscountRequest } from '@/pages/sales/order/edit/types/update.order.discount.request';
import type { OrderListItemEntity } from '@/pages/sales/order/list/types/order.list.item.entity';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ProFormInstance } from '@ant-design/pro-components';
import {
  PageContainer,
  ProCard,
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
} from '@ant-design/pro-components';
import { ProFormTextArea } from '@ant-design/pro-form';
import { history, useIntl, useSearchParams } from '@umijs/max';
import { useDebounceFn } from 'ahooks';
import { App, Col, ConfigProvider, Row, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import CstForm from './compoments/CstForm';
import type { HandleUpdatePriceOrNumberProps } from './compoments/SalesList';
import SalesList from './compoments/SalesList';
import SumBar from './compoments/SumBar';
import {
  createDraftOrder,
  getOrderByOrderNoForDbReturnSelected,
  updateExtendExpense,
  updateOrderAddress,
  updateOrderDeliveryInfo,
  updateOrderDiscount,
  updateOrderItem,
  updateOrderPayKind,
  updateOrderRemark,
} from './services';
import type { CreateDraftOrderRequest } from './types/create.draft.order.request';
import { OprateType } from './types/update.order.item.request';

const SalesOrderEdit = () => {
  const [searchParams] = useSearchParams();
  const { modal } = App.useApp();
  const intl = useIntl();
  // 客户信息表单
  const cstFormRef = useRef<ProFormInstance>();
  // 客户信息详情
  const [cstDetail, setCstDetail] = useState<CustomerSaveEntity>();
  const [accountList, setAccountList] = useState<any[]>([]);
  // 订单详情
  const [orderDetail, setOrderDetail] = useState<OrderListItemEntity>();
  // 底部的额外表单
  const extraFormRef = useRef<ProFormInstance>();
  // 底部表单值变化监听使用防抖
  const { run } = useDebounceFn(
    (changedValues: any, allValues: any) => handleExtraFormDataChange(changedValues, allValues),
    {
      wait: 1000,
    },
  );
  const [warehouseId, setWarehouseId] = useState<string>();
  const [storeId, setStoreId] = useState<string>();

  const orderNo = searchParams.get('orderNo');

  // 设置默认值
  const setDefaultData = () => {
    extraFormRef.current?.setFieldsValue({
      discountType: DiscountType.NONE,
      deliveryMethod: DeliveryMethod.MERCHANT_DELIVERY,
    });
  };

  useActivate(() => {
    queryDetail();
  });

  // 初始化
  useEffect(() => {
    setDefaultData();
    queryDetail();

    return () => {
      setOrderDetail(undefined);
      cstFormRef.current?.resetFields();
      extraFormRef?.current?.resetFields();
      setCstDetail(undefined);
    };
  }, [orderNo]);

  useEffect(() => {
    if (storeId) {
      queryMemberAccountPage({
        belongToStore: [storeId],
      }).then((result) => {
        if (result?.data?.length) {
          setAccountList(
            result.data.map((item) => ({
              label: item.memberAccountName,
              value: item.id,
            })),
          );
        }
      });
    }
    return () => {
      setAccountList([]);
    };
  }, [storeId]);

  // 如果客户信息变化
  useEffect(() => {
    if (cstDetail && !orderNo) {
      // 获取默认的地址作为商家配送地址
      cstDetail.addresses?.forEach((item, index) => {
        if (item.isDefault) {
          extraFormRef.current?.setFieldsValue({
            addressId: item.id,
          });
        }
      });
      // 结算方式设置默认值
      extraFormRef.current?.setFieldsValue({
        payKind: cstDetail.settle?.credit ? PayKind.Credit : PayKind.Cash,
      });
    }
  }, [cstDetail]);

  /**
   * 查询订单信息
   */
  const queryDetail = () => {
    if (orderNo) {
      getOrderByOrderNoForDbReturnSelected(orderNo).then((result) => {
        if (result) {
          setOrderDetail(result);
        }
      });
    }
  };

  /**
   * 回填订单信息到表单
   */
  useEffect(() => {
    if (orderDetail) {
      cstFormRef.current?.setFieldsValue({
        cstId: orderDetail?.orders?.cstId,
        storeId: orderDetail?.orders?.storeId,
        warehouseId: orderDetail?.orderFixedDistributionList?.[0]?.warehouseId,
      });
      setWarehouseId(orderDetail?.orderFixedDistributionList?.[0]?.warehouseId);
      setStoreId(orderDetail?.orders?.storeId);
      extraFormRef.current?.setFieldsValue({
        remark: orderDetail?.orderNoteList?.[0]?.noteDetail,
        estimatedDeliveryTime: orderDetail?.orderFixedDistributionList?.[0]?.estimatedDeliveryTime,
        discountType: orderDetail?.orderFixedDiscountList?.[0]?.discountType,
        discountMoney: orderDetail?.orderFixedDiscountList?.[0]?.discountMoneyYuan,
        deliveryAmount: orderDetail?.orderPrice?.deliveryAmountYuan,
        deliveryMethod: orderDetail?.orderFixedDistributionList?.[0]?.distributionMode,
        payKind: orderDetail?.orderPayDetailList?.[0]?.payKind,
      });
      if (orderDetail?.orderFixedDiscountList?.[0]?.discountDesc !== 'null') {
        extraFormRef.current?.setFieldsValue({
          discountRate: orderDetail?.orderFixedDiscountList?.[0]?.discountDesc,
        });
      }
      if (orderDetail?.orderPayDetailList?.[0]?.payKind === PayKind.Cash) {
        const payDetailList: any[] = [];
        orderDetail?.orderPayDetailList?.forEach((item) => {
          if (
            typeof item.payAmountYuan !== 'undefined' &&
            typeof item.payeeAccount !== 'undefined'
          ) {
            payDetailList.push({
              payeeAcount: item.payeeAccount,
              payAmount: item.payAmountYuan,
            });
          }
        });
        if (payDetailList.length) {
          extraFormRef.current?.setFieldsValue({
            payDetailList: payDetailList,
          });
        }
      }
      if (orderDetail?.orderFixedDiscountList?.length === 0) {
        extraFormRef.current?.setFieldsValue({
          discountType: DiscountType.NONE,
        });
      }
      setTimeout(() => {
        // 因为初始化时某些表单没有创建，所以需要延迟赋值
        extraFormRef.current?.setFieldsValue({
          addressId: orderDetail?.orderFixedAddressList?.[0]?.addressId,
          logisticsCompanyName: orderDetail?.orderFixedDistributionList?.[0]?.logisticsCompanyName,
          logisticsNo: orderDetail?.orderFixedDistributionList?.[0]?.logisticsNo,
        });
      }, 500);
      handleGetCstDetail(orderDetail.orders?.cstId!);
    }
  }, [orderDetail]);

  /**
   * 添加商品事件
   */
  const handleAdd = async (itemList: any[]) => {
    const item = itemList[0];
    if (item.number > item.avaNum) {
      modal.confirm({
        content: intl.formatMessage({ id: 'sales.order.edit.confirmAddOverStock' }),
        okText: intl.formatMessage({ id: 'sales.order.edit.add' }),
        onOk: () => {
          handleAddFn(item);
        },
      });
    } else {
      handleAddFn(item);
    }
  };

  /**
   * 添加商品函数
   * @param item
   */
  const handleAddFn = async (item: any) => {
    // 转换商品数据（提交接口和列表接口的商品字段不一致）
    const { number, price, brandPartNos, oeNos, suggestPrice, unitName, ...rest } = item;
    const goodItem = {
      ...rest,
      brandPartNo: brandPartNos.join(','),
      oeNo: oeNos.join(','),
      unitPrice: price,
      saleNum: number,
      unit: unitName,
      originalPrice: suggestPrice,
    };
    if (orderNo) {
      // 添加时
      updateOrderItem({
        orderId: orderDetail?.orderId,
        oprateType: OprateType.Add,
        orderItemList: [goodItem],
      }).then((result) => {
        if (result) {
          message.success(intl.formatMessage({ id: 'sales.order.edit.addSuccess' }));
          queryDetail();
        }
      });
    } else {
      // 创建时
      const cstInfo = await cstFormRef.current?.validateFields?.();
      console.log('cstInfo', cstInfo);
      const extraInfo = await extraFormRef.current?.validateFields?.();
      console.log('extraInfo', extraInfo);

      // 配送地址
      let address = {} as Address;
      if (extraInfo?.addressId) {
        address =
          cstDetail?.addresses?.find((item) => item.id === extraInfo?.addressId) ?? ({} as Address);
      } else {
        address = cstDetail?.addresses?.find((item) => item.isDefault) ?? ({} as Address);
      }

      // 默认联系人
      const contact = cstDetail?.contacts?.find((item) => item.isDefault);

      const params: CreateDraftOrderRequest = {
        draftOrderMain: {
          cstId: cstInfo?.cstId,
          cstName: cstInfo?.cstName,
          storeId: cstInfo?.storeId,
          cstPhone: contact?.phone,
          storeName: cstInfo?.storeName,
        },
        draftOrderAddress: address,
        draftOrderPay: {
          payKind: extraInfo.payKind,
          payChannel:
            extraInfo.payKind === PayKind.Cash ? PayChannel.CASH : PayChannel.PAYMENT_DAYS,
        },
        draftOrderDelivery: {
          deliveryMethod: extraInfo?.deliveryMethod,
          warehouseId: cstInfo?.warehouseId,
          warehouseName: cstInfo?.warehouseName,
        },
        draftOrderItemList: [goodItem],
      };
      console.log('params', params);
      // 当现款时默认账户设置
      if (extraInfo.payKind === PayKind.Cash) {
        params.draftOrderPay!.payeeAcount = accountList?.[0]?.value;
      }
      createDraftOrder(params).then((result) => {
        if (result?.orderNo) {
          message.success(intl.formatMessage({ id: 'sales.order.edit.addSuccess' }));
          history.replace(`/sales/order/edit?orderNo=${result.orderNo}`);
        }
      });
    }
  };

  /**
   * 更新价格/数量事件
   * @param id
   * @param value
   */
  const handleUpdatePriceOrNumber = (data: HandleUpdatePriceOrNumberProps) => {
    const { id, price, number } = data;
    const currentItem = orderDetail?.orderGoodsROList?.find((item) => item.id === id);
    if (currentItem) console.log('UPDATE', price, number);
    updateOrderItem({
      orderId: orderDetail?.orderId,
      oprateType: OprateType.Modify,
      orderItemList: [{ ...currentItem, unitPrice: price, saleNum: number }],
    }).then((result) => {
      if (result) {
        queryDetail();
      }
    });
  };

  /**
   * 删除商品事件
   * @param id
   */
  const handleDeleteGoodItem = (id: string) => {
    if (orderDetail?.orderFixedDiscountList?.length) {
      message.warning(intl.formatMessage({ id: 'sales.order.edit.confirmDeleteWithDiscount' }));
      return;
    }
    const currentItem = orderDetail?.orderGoodsROList?.find((item) => item.id === id);
    if (currentItem) {
      updateOrderItem({
        orderId: orderDetail?.orderId,
        oprateType: OprateType.Delete,
        orderItemList: [currentItem],
      }).then((result) => {
        if (result) {
          queryDetail();
        }
      });
    }
  };

  /**
   * 查询客户详情
   */
  const handleGetCstDetail = async (cstId: string) => {
    if (cstId) {
      getCstDetail({ cstId }).then((result) => {
        if (result) {
          setCstDetail(result);
        }
      });
    }
  };

  /**
   * 监听客户信息表单值变化
   * @param changedValues
   */
  const handleCstFormDataChange = (changedValues: any) => {
    const keys = Object.keys(changedValues);
    const keyName = keys[0];
    const value = changedValues[keyName];
    console.log('CstFormChangedValues', changedValues);
    switch (keyName) {
      case 'storeId': // 门店变化时清空仓库已选值
        cstFormRef.current?.resetFields(['warehouseId']);
        setStoreId(value);
        break;
      case 'cstId': // 客户变化时获取客户详情信息
        handleGetCstDetail(value);
        break;
      case 'warehouseId':
        setWarehouseId(value);
        if (orderNo) {
          queryDetail();
        }
        break;
    }
  };

  /**
   * 监听额外信息表单值变化(实时保存)
   */
  const handleExtraFormDataChange = (changedValues: any, allValues: any) => {
    const keys = Object.keys(changedValues);
    const keyName = keys[0];
    const value = changedValues[keyName];
    console.log('ExtraFormChangedValues', changedValues);
    if (orderNo) {
      const orderId = orderDetail?.orderId;
      switch (keyName) {
        case 'remark': // 更新备注
          updateOrderRemark({ remark: value, remarkType: 2, orderId: orderDetail?.orderId }).then(
            (result) => {
              if (result) {
                queryDetail();
              }
            },
          );
          break;
        case 'estimatedDeliveryTime': // 更新交货日期
          updateOrderDeliveryInfo({
            orderId: orderDetail?.orderId,
            estimatedDeliveryTime: value,
          }).then((result) => {
            if (result) {
              queryDetail();
            }
          });
          break;
        case 'discountType': // 更新优惠信息
        case 'discountRate':
        case 'discountMoney':
          const updateOrderDiscountParams = {
            discountType: allValues.discountType,
            orderId,
          } as UpdateOrderDiscountRequest;
          switch (allValues.discountType) {
            case DiscountType.NONE:
              updateOrderDiscount(updateOrderDiscountParams).then((result) => {
                if (result) {
                  queryDetail();
                }
              });
              break;
            case DiscountType.DISCOUNT_ON_ORDER:
              if (changedValues.discountRate) {
                updateOrderDiscountParams.discountRate = allValues.discountRate;
                updateOrderDiscount(updateOrderDiscountParams).then((result) => {
                  if (result) {
                    queryDetail();
                  }
                });
              }
              break;
            case DiscountType.DEDUCTION_ON_ORDER:
              if (changedValues.discountMoney) {
                updateOrderDiscountParams.discountMoney = allValues.discountMoney;
                updateOrderDiscount(updateOrderDiscountParams).then((result) => {
                  if (result) {
                    queryDetail();
                  }
                });
              }
              break;
          }
          break;
        case 'deliveryAmount': // 更新运费
          updateExtendExpense({ orderId, deliveryAmount: value }).then((result) => {
            if (result) {
              queryDetail();
            }
          });
          break;
        case 'deliveryMethod': // 更新配送信息
          updateOrderDeliveryInfo({ orderId, deliveryMethod: value }).then((result) => {
            if (result) {
              queryDetail();
            }
          });
          break;
        case 'addressId':
          const address = cstDetail?.addresses?.find((item) => item.id === allValues.addressId);
          if (address) {
            updateOrderAddress({ orderId, ...address }).then((result) => {
              if (result) {
                queryDetail();
              }
            });
          }
          break;
        case 'logisticsCompanyName':
        case 'logisticsNo':
          updateOrderDeliveryInfo({
            orderId,
            logisticsCompanyName: allValues.logisticsCompanyName,
            logisticsNo: allValues.logisticsNo,
          }).then((result) => {
            if (result) {
              queryDetail();
            }
          });
          break;
        case 'payKind':
        case 'payDetailList':
          const payKindParams = {
            orderId,
            payKind: allValues.payKind,
            payDetailList: [],
          } as ConfirmPayRequest;
          let PayInfoFinished = true;
          switch (allValues.payKind) {
            case PayKind.Credit:
              payKindParams.payDetailList?.push({
                payChannel: PayChannel.PAYMENT_DAYS,
                payAmount: orderDetail?.orderPrice?.shouldTotalOrderAmountYuan,
              });
              break;
            case PayKind.Cash:
              allValues.payDetailList?.forEach((item: any) => {
                if (
                  typeof item?.payeeAcount !== 'undefined' &&
                  typeof item?.payAmount !== 'undefined'
                ) {
                  payKindParams.payDetailList?.push({
                    ...item,
                    payChannel: PayChannel.CASH,
                  });
                }
              });
              console.log('accountList', accountList);
              if (payKindParams.payDetailList?.length === 0) {
                payKindParams.payDetailList.push({
                  payChannel: PayChannel.CASH,
                  payeeAcount: accountList?.[0]?.value,
                  payAmount: orderDetail?.orderPrice?.shouldTotalOrderAmountYuan,
                });
              }
              console.log(
                'PayInfoFinished',
                allValues.payDetailList?.length,
                payKindParams?.payDetailList?.length,
              );
              PayInfoFinished =
                (allValues.payDetailList?.length ?? 0) <=
                (payKindParams?.payDetailList?.length ?? 0);
              break;
          }
          if (PayInfoFinished) {
            updateOrderPayKind(payKindParams).then((result) => {
              if (result) {
                queryDetail();
              }
            });
          }
          break;
      }
    }
  };

  return (
    <PageContainer>
      <ProCard>
        <ProForm
          formRef={cstFormRef}
          submitter={false}
          layout="inline"
          onValuesChange={handleCstFormDataChange}
        >
          <CstForm
            // @ts-ignore
            cstFormRef={cstFormRef}
            orderId={orderDetail?.orderId}
            orderNo={orderNo as string}
            setWarehouseId={setWarehouseId}
            setStoreId={setStoreId}
          />
        </ProForm>
        <CstDetail cstDetail={cstDetail} />
      </ProCard>
      <ProCard className="mt-4" bodyStyle={{ paddingTop: 8 }}>
        <ConfigProvider
          theme={{
            components: {
              InputNumber: {
                controlWidth: 80,
              },
            },
          }}
        >
          <GoodsSearch
            bizType={GoodsSearchBizType.Sales}
            onAdd={handleAdd}
            addedItemSns={orderDetail?.orderGoodsROList?.map((item) => item.itemSn!) ?? []}
            warehouseId={warehouseId}
            cstId={cstDetail?.base?.id}
            storeId={storeId}
          />
        </ConfigProvider>
      </ProCard>
      <ProCard className="mt-4" bodyStyle={{ paddingTop: 0 }}>
        <ConfigProvider
          theme={{
            components: {
              InputNumber: {
                controlWidth: 80,
              },
            },
          }}
        >
          <SalesList
            orderDetail={orderDetail}
            handleUpdatePriceOrNumber={handleUpdatePriceOrNumber}
            handleDeleteGoodItem={handleDeleteGoodItem}
            cstId={cstDetail?.base?.id}
            storeId={storeId}
            warehouseId={warehouseId}
          />
        </ConfigProvider>
        <ConfigProvider theme={{ components: { Form: { itemMarginBottom: 8 } } }}>
          <ProForm
            submitter={false}
            formRef={extraFormRef}
            disabled={!Boolean(orderNo)}
            onValuesChange={run}
            className="mt-[20px]"
          >
            <Row justify="space-between">
              <Col style={{ width: 130 }}>
                <DiscountForm />
              </Col>
              <Col style={{ width: 270 }}>
                <PaymentForm cstDetail={cstDetail} storeId={storeId} />
              </Col>
              <Col span={3} style={{ minWidth: 120 }}>
                <ProFormDigit
                  label={<span className="font-semibold">{intl.formatMessage({ id: 'sales.order.edit.deliveryAmount' })}</span>}
                  name="deliveryAmount"
                  min={0}
                  placeholder={intl.formatMessage({ id: 'sales.order.edit.setDeliveryAmount' })}
                  fieldProps={{ addonAfter: intl.formatMessage({ id: 'sales.order.edit.amountUnit' }), precision: 2, controls: false }}
                />
              </Col>
              <Col span={4}>
                <DeliveryForm cstDetail={cstDetail} />
              </Col>
              <Col span={3}>
                <ProFormDatePicker
                  // @ts-ignore
                  width="100%"
                  label={<span className="font-semibold">{intl.formatMessage({ id: 'sales.order.edit.estimatedDeliveryTime' })}</span>}
                  name="estimatedDeliveryTime"
                  placeholder={intl.formatMessage({ id: 'sales.order.edit.estimatedDeliveryTimePlaceholder' })}
                />
              </Col>
              <Col span={4}>
                <ProFormTextArea
                  label={<span className="font-semibold">{intl.formatMessage({ id: 'sales.order.edit.remark' })}</span>}
                  name="remark"
                  fieldProps={{ count: { max: 100, show: true }, maxLength: 100 }}
                />
              </Col>
            </Row>
          </ProForm>
        </ConfigProvider>
      </ProCard>
      <ProCard className="mt-[1px]">
        <SumBar orderDetail={orderDetail} />
      </ProCard>
    </PageContainer>
  );
};

export default withKeepAlive(SalesOrderEdit);