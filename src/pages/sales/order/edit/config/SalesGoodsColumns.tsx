import ColumnRender from '@/components/ColumnRender';
import type { StoreGoodsEntity } from '@/components/GoodsSearch/components/GoodsList/types/store.goods.entity';
import ImageList from '@/components/ImageList';
import type { PriceInfoDrawerProps } from '@/components/PriceInfoDrawer';
import type { StocksInfoDrawerProps } from '@/components/StocksInfoDrawer';
import MoneyText from '@/components/common/MoneyText';
import type { OrderGoodsROList } from '@/pages/sales/order/list/types/order.list.item.entity';
import { requiredProps } from '@/types/validateRules';
import { MAX_AMOUNT, MAX_COUNT } from '@/utils/Constants';
import { type ProColumns } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';
import { Button, InputNumber } from 'antd';

export interface SalesGoodsColumnsProps {
  handleDeleteGoodItem: (id: string) => void;
  handleUpdate: (id: string) => void;
  /**
   * 查看价格
   */
  handleViewPriceInfo: (data: PriceInfoDrawerProps) => void;
  /**
   * 查看库存
   */
  handleViewStocksInfo: (data: StocksInfoDrawerProps) => void;
  intl: IntlShape;
}

export default (props: SalesGoodsColumnsProps) => {
  const { intl } = props;

  return [
    {
      title: intl.formatMessage({ id: 'sales.order.edit.index' }),
      valueType: 'index',
      fixed: 'left',
      editable: false,
      width: 40,
      dataIndex: 'index',
    },
    // {
    //   title: '商品名称',
    //   dataIndex: 'itemName',
    //   width: 200,
    //   fixed: 'left',
    //   search: false,
    //   editable: false,
    // },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.productCode' }),
      dataIndex: 'itemSn',
      width: 100,
      search: false,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.productName' }),
      dataIndex: 'images',
      width: 140,
      search: false,
      editable: false,
      render: (text, record) => {
        return <ImageList itemName={record.itemName} urls={record.images} />;
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.oe' }),
      editable: false,
      dataIndex: 'oeNo',
      search: false,
      width: 140,
      render: (text, record) => ColumnRender.ArrayColumnRender(record.oeNo?.split(',') as string[]),
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.brandPartNo' }),
      editable: false,
      dataIndex: 'brandPartNo',
      search: false,
      width: 100,
      render: (text, record) =>
        ColumnRender.ArrayColumnRender(record.brandPartNo?.split(',') as string[]),
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.brand' }),
      dataIndex: 'brandName',
      editable: false,
      search: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.category' }),
      editable: false,
      search: false,
      dataIndex: 'categoryName',
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.origin' }),
      dataIndex: 'originRegionName',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.specification' }),
      dataIndex: 'spec',
      search: false,
      editable: false,
      width: 60,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.unit' }),
      dataIndex: 'unitName',
      search: false,
      editable: false,
      width: 50,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.vehicleRemark' }),
      dataIndex: 'adaptModel',
      search: false,
      editable: false,
      width: 120,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.productRemark' }),
      dataIndex: 'remark',
      search: false,
      editable: false,
      width: 120,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.localStock' }),
      dataIndex: 'avaNum',
      search: false,
      editable: false,
      width: 60,
      render: (text, record: StoreGoodsEntity) => (
        <a
          className="cursor-pointer"
          onClick={() => {
            props.handleViewStocksInfo({
              itemIdList: [record.itemId],
            });
          }}
        >
          {record.avaNum}
        </a>
      ),
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.location' }),
      dataIndex: 'locationCode',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.suggestedPrice' }),
      dataIndex: 'origPriceYuan',
      search: false,
      editable: false,
      width: 80,
      render: (text, record: StoreGoodsEntity) => (
        <a
          className="cursor-pointer"
          onClick={() =>
            props.handleViewPriceInfo({
              itemId: record.itemId,
              itemSn: record.itemSn,
              itemName: record.itemName,
              // @ts-ignore
              suggestPrice: record.origPriceYuan,
              lowPrice: record.lowPrice,
              costPrice: record.costPriceYuan,
            })
          }
        >
          <MoneyText text={record.origPriceYuan} />
        </a>
      ),
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.lastSalePrice' }),
      dataIndex: 'lastSalePrice',
      search: false,
      editable: false,
      valueType: 'money',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.lowestPrice' }),
      dataIndex: 'lowPrice',
      search: false,
      editable: false,
      valueType: 'money',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.costPrice' }),
      dataIndex: 'costPriceYuan',
      search: false,
      editable: false,
      valueType: 'money',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.grossMargin' }),
      dataIndex: 'grossMargin',
      search: false,
      editable: false,
      width: 80,
      valueType: 'money',
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.salePrice' }),
      dataIndex: 'unitPriceYuan',
      search: false,
      width: 100,
      fixed: 'right',
      renderFormItem: (item, config) => {
        return (
          <InputNumber
            min={0.01}
            controls={false}
            precision={2}
            max={MAX_AMOUNT}
            placeholder={intl.formatMessage({ id: 'sales.order.edit.inputPlaceholder' })}
            onChange={(value) => {
              props.handleUpdate(config.record?.id!);
            }}
          />
        );
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.saleQuantity' }),
      dataIndex: 'saleNum',
      search: false,
      width: 100,
      fixed: 'right',
      valueType: 'digit',
      renderFormItem: (item, config) => {
        return (
          <InputNumber
            min={1}
            precision={0}
            max={MAX_COUNT}
            placeholder={intl.formatMessage({ id: 'sales.order.edit.inputPlaceholder' })}
            onChange={(value) => {
              props.handleUpdate(config.record?.id!);
            }}
          />
        );
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.operation' }),
      width: 60,
      editable: false,
      dataIndex: 'action',
      fixed: 'right',
      render: (text, row) => (
        <Button type={'link'} onClick={() => props.handleDeleteGoodItem(row.id)}>
          {intl.formatMessage({ id: 'sales.order.edit.delete' })}
        </Button>
      ),
    },
  ] as ProColumns<OrderGoodsROList>[];
};
