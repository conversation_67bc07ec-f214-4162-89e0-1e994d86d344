.shortcut-help-drawer {
  .ant-drawer-body {
    background-color: #F2F2F2 ;
    padding: 16px;
  }

  .ant-drawer-header {
    border-bottom: none;
  }
}

.shortcut-help-table {
  .ant-table {
    background: #fff;
    border-radius: 10px;
    overflow: hidden;

    .ant-table-container {
      border-radius: 10px;
    }

    .ant-table-thead {
      > tr {
        > th {
          background-color: #fff !important;
          border: none;
          border-bottom: 1px solid #f0f0f0;
          padding: 12px 8px;
          color: #000;

          &:first-child {
            padding-left: 24px;
          }

          &::before {
            display: none;
          }
        }
      }
    }

    .ant-table-tbody {
      > tr {
        > td {
          border: none;
          background-color: #fff;
          padding: 8px;
          font-size: 16px;

          &:first-child {
            padding-left: 24px;
          }
        }

        &:hover {
          > td {
            background-color: #fff !important;
          }
        }
      }
    }
  }
}
