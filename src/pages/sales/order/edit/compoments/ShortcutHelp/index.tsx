import { Drawer, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React from 'react';
import './index.scss';

interface ShortcutItem {
    operation: string;
    windowsShortcut: string;
    macShortcut: string;
}

const ShortcutHelp: React.FC = () => {
    const columns: ColumnsType<ShortcutItem> = [
        {
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            width: '25%',
        },
        {
            title: 'Windows快捷键',
            dataIndex: 'windowsShortcut',
            key: 'windowsShortcut',
            width: '37.5%',
            render: (text) => (
                <span>{text}</span>
            ),
        },
        {
            title: 'Mac快捷键',
            dataIndex: 'macShortcut',
            key: 'macShortcut',
            width: '37.5%',
            render: (text) => (
                <span>{text}</span>
            ),
        },
    ];

    const dataSource: ShortcutItem[] = [
        { operation: '上一行', windowsShortcut: '↑', macShortcut: '↑' },
        { operation: '下一行', windowsShortcut: '↓', macShortcut: '↓' },
        { operation: '下一项', windowsShortcut: 'Tab', macShortcut: 'Tab' },
        { operation: '上一项', windowsShortcut: 'Shift+Tab', macShortcut: 'Shift+Tab' },
        { operation: '确认/选中', windowsShortcut: 'Enter', macShortcut: 'Enter' },
        { operation: '关闭', windowsShortcut: 'Esc', macShortcut: 'Esc' },
        { operation: '商品图片', windowsShortcut: 'Alt+T', macShortcut: 'Options+T' },
        { operation: '本地库存', windowsShortcut: 'Alt+B', macShortcut: 'Options+B' },
        { operation: '价格信息', windowsShortcut: 'Alt+J', macShortcut: 'Options+J' },
        { operation: '采购历史', windowsShortcut: 'Alt+C', macShortcut: 'Options+C' },
        { operation: '编辑商品', windowsShortcut: 'Alt+S', macShortcut: 'Options+S' },
        { operation: '客户档案', windowsShortcut: 'Alt+K', macShortcut: 'Options+K' },
    ];

    return (
        <Table

            columns={columns}
            dataSource={dataSource}
            pagination={false}
            size="large"
            bordered={false}
            style={{
                width: '100%',
            }}
            className="shortcut-help-table"
        />
    );
};

const ShortcutHelpDrawer = (props: { open: boolean, onOpenChange: (open: boolean) => void, onClose: () => void }) => {
    return (
        <Drawer
            title="快捷键"
            open={props.open}
            onOpenChange={props.onOpenChange}
            onClose={props.onClose}
            width={500}
            className="shortcut-help-drawer"
        >
            <ShortcutHelp />
        </Drawer>
    );
};

export default ShortcutHelpDrawer;