import MoneyText from '@/components/common/MoneyText';
import type { OrderListItemEntity } from '@/pages/sales/order/list/types/order.list.item.entity';
import { useIntl } from '@umijs/max';

export interface SubTotalProps {
  orderDetail?: OrderListItemEntity;
}

const SubTotal = (props: SubTotalProps) => {
  const { orderDetail } = props;
  const intl = useIntl();

  const getSaleNum = () => {
    let num = 0;
    orderDetail?.orderGoodsROList?.forEach((item) => {
      num += item?.saleNum ?? 0;
    });
    return num;
  };

  return (
    <span className="font-semibold text-base text-[#000000D9]">
      <span className="">{intl.formatMessage({ id: 'sales.order.edit.totalQuantity' })}：{getSaleNum()}</span>
      <span className="ml-8">
        {intl.formatMessage({ id: 'sales.order.edit.totalAmount' })}：
        <span className="text-2xl text-[#F83431FF]">
          <MoneyText text={orderDetail?.orderPrice?.totalGoodsPriceAmountYuan} />
        </span>
      </span>
      <span className="ml-8">
        {intl.formatMessage({ id: 'sales.order.edit.discountAmount' })}：
        <MoneyText text={orderDetail?.orderPrice?.totalDiscountAmountYuan} />
      </span>
      <span className="ml-8">
        {intl.formatMessage({ id: 'sales.order.edit.deliveryFee' })}：
        <MoneyText text={orderDetail?.orderPrice?.deliveryAmountYuan} />
      </span>
      <span className="ml-8">
        {intl.formatMessage({ id: 'sales.order.edit.actualAmount' })}：
        <span className="text-2xl text-[#F83431FF]">
          <MoneyText text={orderDetail?.orderPrice?.shouldTotalOrderAmountYuan} />
        </span>
      </span>
    </span>
  );
};

export default SubTotal;
