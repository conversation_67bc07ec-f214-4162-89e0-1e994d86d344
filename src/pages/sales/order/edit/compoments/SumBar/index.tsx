import AuthButton from '@/components/common/AuthButton';
import { KeepAliveTabContext } from '@/layouts/context';
import SubTotal from '@/pages/sales/order/edit/compoments/SubTotal';
import { allOutbound, confirmPay } from '@/pages/sales/order/edit/services';
import { submitOrder } from '@/pages/sales/order/list/services';
import { OrderListItemEntity } from '@/pages/sales/order/list/types/order.list.item.entity';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { Access, history, useAccess, useIntl } from '@umijs/max';
import { Checkbox, Col, Row, Space } from 'antd';
import { useContext, useState } from 'react';

export interface SumBarProps {
  orderDetail?: OrderListItemEntity;
}

export const SumBar = (props: SumBarProps) => {
  const { orderDetail } = props;
  const { closeTab } = useContext(KeepAliveTabContext);
  const [loading, setLoading] = useState(false);
  const [settle, setSettle] = useState(false);
  const [stockOut, setStockOut] = useState(false);
  const [print, setPrint] = useState(false);
  const access = useAccess();
  const intl = useIntl();

  const handleSubmit = () => {
    setLoading(true);
    submitOrder(orderDetail?.orderId!)
      .then(async (result) => {
        if (result) {
          if (stockOut) {
            try {
              await allOutbound([orderDetail?.orderId!]);
            } catch (error) {
              console.error(error);
            }
          }
          if (settle) {
            try {
              await confirmPay({
                orderId: orderDetail?.orderId,
                payKind: orderDetail?.orderPayDetailList?.[0].payKind,
                payDetailList: orderDetail?.orderPayDetailList?.map((item) => ({
                  payeeAcount: item.payeeAccount,
                  payAmount: item.payAmountYuan,
                  payChannel: item.payChannel,
                })),
              });
            } catch (error) {
              console.error(error);
            }
          }
          if (print) {
            window.open(
              `/print?printType=${PrintType.salesOrder}&orderNo=${orderDetail?.orders?.orderNo}`,
            );
          }
          closeTab();
          history.push(`/sales/order/detail?orderNo=${orderDetail?.orders?.orderNo}`);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <Row justify="space-between">
      <Col>
        <SubTotal orderDetail={orderDetail} />
      </Col>
      <Col>
        <Space>
          <Access accessible={access.hasButtonPerms('salesConfirmSettlement')}>
            <Checkbox checked={settle} onChange={() => setSettle(!settle)}>
              {intl.formatMessage({ id: 'sales.order.edit.confirmSettlement' })}
            </Checkbox>
          </Access>
          <Access accessible={access.hasButtonPerms('salesOutput')}>
            <Checkbox checked={stockOut} onChange={() => setStockOut(!stockOut)}>
              {intl.formatMessage({ id: 'sales.order.edit.oneClickOutbound' })}
            </Checkbox>
          </Access>
          <Access accessible={access.hasButtonPerms('salesPrint')}>
            <Checkbox checked={print} onChange={() => setPrint(!print)}>
              {intl.formatMessage({ id: 'sales.order.edit.submitAndPrint' })}
            </Checkbox>
          </Access>
          <AuthButton
            authority="submitOrder"
            type="primary"
            onClick={handleSubmit}
            loading={loading}
            disabled={!orderDetail?.orderId || orderDetail?.orderGoodsROList?.length === 0}
          >
            {intl.formatMessage({ id: 'sales.order.edit.submit' })}
          </AuthButton>
        </Space>
      </Col>
    </Row>
  );
};

export default SumBar;
