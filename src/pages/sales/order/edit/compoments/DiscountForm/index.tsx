import { DiscountType, useDiscountTypeOptions } from '@/pages/sales/order/edit/types/DiscountType';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { ProFormDependency, ProFormDigit, ProFormSelect } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';

export interface DiscountFormData {
  discountType: DiscountType;
  discountRate?: number;
  discountMoney?: number;
}

const DiscountForm = () => {
  const intl = useIntl();
  const discountTypeOptions = useDiscountTypeOptions();

  return (
    <>
      <ProFormSelect
        name="discountType"
        options={discountTypeOptions}
        label={<span className="font-semibold">{intl.formatMessage({ id: 'sales.order.edit.discountType' })}</span>}
      />
      <ProFormDependency name={['discountType']}>
        {({ discountType }) => {
          if (discountType === DiscountType.DISCOUNT_ON_ORDER) {
            return (
              <ProFormDigit
                rules={[REQUIRED_RULES]}
                name="discountRate"
                placeholder={intl.formatMessage({ id: 'sales.order.edit.inputDiscount' })}
                max={9.9}
                min={0.1}
                fieldProps={{ addonAfter: intl.formatMessage({ id: 'sales.order.edit.discountUnit' }), precision: 1 }}
              />
            );
          }
          if (discountType === DiscountType.DEDUCTION_ON_ORDER) {
            return (
              <ProFormDigit
                rules={[REQUIRED_RULES]}
                name="discountMoney"
                min={0.01}
                placeholder={intl.formatMessage({ id: 'sales.order.edit.inputAmount' })}
                fieldProps={{ addonAfter: intl.formatMessage({ id: 'sales.order.edit.amountUnit' }), precision: 2 }}
              />
            );
          }
        }}
      </ProFormDependency>
    </>
  );
};

export default DiscountForm;
