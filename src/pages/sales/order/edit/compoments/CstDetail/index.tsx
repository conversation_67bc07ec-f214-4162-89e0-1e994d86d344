import CustomerDetailDrawerForm from '@/pages/customer/list/components/CustomerDetailDrawerForm';
import { CustomerDetailDrawerFormType } from '@/pages/customer/list/types/CustomerDetailDrawerFormType';
import type { CustomerSaveEntity } from '@/pages/customer/list/types/CustomerSaveEntity';
import { useCallback, useState } from 'react';

import { simulateEscKeyPress } from '@/components/GoodsSearch/components/GoodsList/useKeyboardEvents';
import { useIntl } from '@umijs/max';
import { useKeyPress } from 'ahooks';
import { Col, Row, Tag } from 'antd';
import { defaultTo } from 'lodash';

export interface CstDetailProps {
  cstDetail?: CustomerSaveEntity;
}

const CstDetail = (props: CstDetailProps) => {
  const { cstDetail } = props;
  const intl = useIntl();

  const [detailModalProps, setDetailModalProps] = useState<CustomerDetailDrawerFormType>({
    visible: false,
    recordId: cstDetail?.base?.id ?? '',
    onCancel: () => {
      setDetailModalProps((preModalProps) => ({
        ...preModalProps,
        visible: false,
        recordId: '',
      }));
    },
    title: intl.formatMessage({ id: 'sales.order.edit.customerDetail' }),
  });


  const handleKeyPress = useCallback(() => {
    if (!detailModalProps.visible) {
      simulateEscKeyPress()
    }
    setDetailModalProps((prev) => ({
      ...prev,
      visible: !prev.visible,
      recordId: prev.visible ? '' : (cstDetail?.base?.id ?? ''),
    }));
  }, [cstDetail?.base?.id, detailModalProps.visible]);

  useKeyPress(
    'alt.k',
    handleKeyPress,
    {
      events: cstDetail?.base?.id ? ['keydown'] : [],
      target: () => document,
    }
  );


  if (!cstDetail) {
    return;
  }

  const contact = cstDetail.contacts?.find((item) => item.isDefault);

  return (
    <div className="mt-4">
      <div className="py-5 px-6 rounded bg-[#f5f5f5]">
        <span className="text-[#000000D9] font-semibold text-xl flex items-center">
          <span className="mr-2">{cstDetail.base.cstName}</span>
          {cstDetail.settle?.credit ? <Tag color="orange">{intl.formatMessage({ id: 'sales.order.edit.creditCustomer' })}</Tag> : ''}
          {cstDetail.tags?.map((item) => (
            <Tag key={item.id} color="blue">{item.tagName}</Tag>
          ))}
        </span>
        <Row className="mt-2 text-[#000000D9]">
          <Col span={6}>{intl.formatMessage({ id: 'sales.order.edit.contact' })}：{contact?.name}</Col>
          <Col span={6}>{intl.formatMessage({ id: 'sales.order.edit.contactMethod' })}：{contact?.phone}</Col>
          {cstDetail?.settle?.credit && (
            <>
              <Col span={6}>
                {intl.formatMessage({ id: 'sales.order.edit.creditTerm' })}：{cstDetail?.settle?.remainTerms ?? '-'}{intl.formatMessage({ id: 'sales.order.edit.days' })}/
                {cstDetail?.settle?.creditTerms ?? '-'}{intl.formatMessage({ id: 'sales.order.edit.days' })}
              </Col>
              <Col span={6}>
                {intl.formatMessage({ id: 'sales.order.edit.creditLimit' })}：{intl.formatMessage({ id: 'sales.order.edit.used' })}{defaultTo(cstDetail.settle?.usedAmount, '-')}/{intl.formatMessage({ id: 'sales.order.edit.available' })}
                {defaultTo(cstDetail.settle?.availableAmount, '-')}
              </Col>
            </>
          )}
        </Row>
      </div>

      <CustomerDetailDrawerForm {...detailModalProps} />
    </div>
  );
};

export default CstDetail;
