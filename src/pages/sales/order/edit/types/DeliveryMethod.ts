import { useIntl } from '@umijs/max';

export enum DeliveryMethod {
  SELF_PICKUP = 1,
  MERCHANT_DELIVERY = 2,
  EXPRESS_LOGISTICS = 3,
}

export enum DeliveryMethodName {
  SELF_PICKUP = '客户自提',
  MERCHANT_DELIVERY = '商家配送',
  EXPRESS_LOGISTICS = '快递物流',
}

export const useDeliveryMethodOptions = () => {
  const intl = useIntl();

  return [
    {
      label: intl.formatMessage({ id: 'sales.order.edit.deliveryMethods.selfPickup' }),
      value: DeliveryMethod.SELF_PICKUP,
    },
    {
      label: intl.formatMessage({ id: 'sales.order.edit.deliveryMethods.merchantDelivery' }),
      value: DeliveryMethod.MERCHANT_DELIVERY,
    },
    {
      label: intl.formatMessage({ id: 'sales.order.edit.deliveryMethods.expressLogistics' }),
      value: DeliveryMethod.EXPRESS_LOGISTICS,
    },
  ];
};

// Keep the original for backward compatibility
export const deliveryMethodOptions = [
  {
    label: DeliveryMethodName.SELF_PICKUP,
    value: DeliveryMethod.SELF_PICKUP,
  },
  {
    label: DeliveryMethodName.MERCHANT_DELIVERY,
    value: DeliveryMethod.MERCHANT_DELIVERY,
  },
  {
    label: DeliveryMethodName.EXPRESS_LOGISTICS,
    value: DeliveryMethod.EXPRESS_LOGISTICS,
  },
];
