import { useIntl } from '@umijs/max';

export enum DiscountType {
  NONE,
  DISCOUNT_ON_ORDER,
  DEDUCTION_ON_ORDER,
}

export enum DiscountTypeName {
  NONE = '无折扣',
  DISCOUNT_ON_ORDER = '整单折',
  DEDUCTION_ON_ORDER = '整单减',
}

export const useDiscountTypeOptions = () => {
  const intl = useIntl();

  return [
    {
      label: intl.formatMessage({ id: 'sales.order.edit.discountTypes.none' }),
      value: DiscountType.NONE,
    },
    {
      label: intl.formatMessage({ id: 'sales.order.edit.discountTypes.orderDiscount' }),
      value: DiscountType.DISCOUNT_ON_ORDER,
    },
    {
      label: intl.formatMessage({ id: 'sales.order.edit.discountTypes.orderDeduction' }),
      value: DiscountType.DEDUCTION_ON_ORDER,
    },
  ];
};

// Keep the original for backward compatibility
export const discountTypeOptions = [
  {
    label: DiscountTypeName.NONE,
    value: DiscountType.NONE,
  },
  {
    label: DiscountTypeName.DISCOUNT_ON_ORDER,
    value: DiscountType.DISCOUNT_ON_ORDER,
  },
  {
    label: DiscountTypeName.DEDUCTION_ON_ORDER,
    value: DiscountType.DEDUCTION_ON_ORDER,
  },
];
