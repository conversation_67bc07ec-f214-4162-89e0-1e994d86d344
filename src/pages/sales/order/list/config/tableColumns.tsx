import { getCstList } from '@/pages/customer/list/services';
import { MemberAccountEntity } from '@/pages/finance/customer/types/MemberAccountEntity';
import { orderStatusMap } from '@/pages/sales/order/list/types/OrderStatus';
import { payStatusMap } from '@/pages/sales/order/list/types/PayStatus';
import { paymentStatusMap } from '@/pages/sales/order/list/types/PaymentStatus';
import { queryWarehouseList } from '@/pages/stocks/warehouse/services';
import { accountListQuerySimple, queryStoreByAccount } from '@/pages/system/user/services';
import type { ProColumns } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';
import { history } from '@umijs/max';
import { receiptStatusOptions } from '../types/ReceiptStatus';
import type { OrderListItemEntity } from '../types/order.list.item.entity';

export interface OrderListColumns {
  operatorColumn: ProColumns<OrderListItemEntity>;
  activeTabKey: string;
  accountList: MemberAccountEntity[];
  intl: IntlShape;
}

export const tableColumns = (props: OrderListColumns) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.orderNo' }),
      dataIndex: ['orders', 'orderNo'],
      formItemProps: {
        name: 'orderNo',
      },
      fixed: 'left',
      width: 160,
      render: (text) => (
        <a
          className="cursor-pointer"
          onClick={() => history.push(`/sales/order/detail?orderNo=${text}`)}
        >
          {text}
        </a>
      ),
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.customerName' }),
      dataIndex: ['orders', 'cstName'],
      formItemProps: {
        name: 'cstId',
      },
      debounceTime: 200,
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'cstName', value: 'cstId' },
      },
      request: () => getCstList({}),
      width: 120,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.orderTime' }),
      dataIndex: ['orders', 'orderCreateTime'],
      search: false,
      width: 140,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.orderTime' }),
      dataIndex: ['orders', 'orderCreateTime'],
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value: any) => {
          return {
            beginOrderTime: value[0],
            endOrderTime: value[1],
          };
        },
      },
      width: 120,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.orderAmount' }),
      dataIndex: ['orderPrice', 'shouldTotalOrderAmountYuan'],
      width: 80,
      search: false,
      valueType: 'money',
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.grossProfit' }),
      width: 80,
      dataIndex: ['orderPrice', 'grossProfitYuan'],
      valueType: 'money',
      search: false,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.orderStatus' }),
      dataIndex: ['orderStatus', 'orderStatusName'],
      width: 60,
      valueType: 'select',
      valueEnum: orderStatusMap,
      formItemProps: {
        name: 'orderStatusList',
      },
      fieldProps: {
        disabled: props.activeTabKey,
        mode: 'multiple',
      },
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.settlementAccount' }),
      dataIndex: 'orderPayDetailList',
      width: 120,
      transform: (value) => {
        return { accountIds: value }
      },
      renderText: (orderPayDetailList) => {
        return orderPayDetailList?.map((item) => item.payKind == 2 ? item.payKindName : item.payeeAccountName).join(',');
      },
      ellipsis: true,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        name: 'accountIds',
        options: (props.accountList ?? [])?.map(item => ({
          label: item.memberAccountName,
          value: item.id
        })),
      }
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.settlementStatus' }),
      dataIndex: ['orderStatus', 'payStatus'],
      width: 80,
      valueType: 'select',
      valueEnum: payStatusMap,
      formItemProps: {
        name: 'payStatusList',
      },
      fieldProps: {
        mode: 'multiple',
      },
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.paymentStatus' }),
      dataIndex: ['orderStatus', 'paymentStatus'],
      width: 80,
      valueType: 'select',
      valueEnum: paymentStatusMap,
      formItemProps: {
        name: 'paymentStatusList',
      },
      fieldProps: {
        mode: 'multiple',
      },
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.payTime' }),
      dataIndex: ['orderPayList', 0, 'payTime'],
      width: 140,
      search: false,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.outboundTime' }),
      dataIndex: ['orderFixedDistributionList', 0, 'outboundFinishTime'],
      width: 140,
      search: false,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.finishTime' }),
      dataIndex: ['orders', 'orderFinishTime'],
      width: 140,
      search: false,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.productInfo' }),
      dataIndex: 'itemInfo',
      hideInTable: true,
      fieldProps: {
        placeholder: props.intl.formatMessage({ id: 'sales.order.list.productSearchPlaceholder' }),
      },
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.salesStore' }),
      dataIndex: ['orders', 'storeName'],
      width: 120,
      debounceTime: 300,
      valueType: 'select',
      ellipsis: true,
      formItemProps: {
        name: 'storeIdList',
      },
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
      },
      request: (query) =>
        queryStoreByAccount({}).then((result) =>
          result.map((item) => ({ label: item.name, value: item.id })),
        ),
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.deliveryWarehouse' }),
      dataIndex: ['orderFixedDistributionList', 0, 'warehouseName'],
      width: 120,
      ellipsis: true,
      debounceTime: 300,
      valueType: 'select',
      formItemProps: {
        name: 'deliWarehouseIdList',
      },
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
      },
      request: (query) =>
        queryWarehouseList({
          warehouseNameKeyword: query.keyWords,
          pageSize: 99,
        }).then((result) =>
          result.data.map((item) => ({ label: item.warehouseName, value: item.id })),
        ),
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.creator' }),
      dataIndex: ['orders', 'salesman'],
      search: {
        transform: (value: any) => {
          return {
            salesmanIdList: [value],
          };
        },
      },
      width: 60,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.remark' }),
      dataIndex: ['orderNoteList', 0, 'noteDetail'],
      width: 80,
      ellipsis: true,
      formItemProps: {
        name: 'remark',
      },
    },
    props.operatorColumn,
  ] as ProColumns<OrderListItemEntity>[];
