import { FormattedMessage } from "@umijs/max";

export enum OrderStatus {
  WAIT_TO_HANDLE = 90,
  WAIT_TO_OUTBOUND = 300,
  OUTBOUND_FINISH = 400,
  TRADE_SUCCESS = 600,
  TRADE_CLOSE = 800,
}

export enum OrderStatusName {
  WAIT_TO_HANDLE = 'sales.order.list.status.draft',
  WAIT_TO_OUTBOUND = 'sales.order.list.status.waitToOutbound',
  OUTBOUND_FINISH = 'sales.order.list.status.outboundFinish',
  TRADE_SUCCESS = 'sales.order.list.status.tradeSuccess',
  TRADE_CLOSE = 'sales.order.list.status.tradeClose',
}

export const orderStatusMap = {
  [OrderStatus.WAIT_TO_HANDLE]: {
    text: <FormattedMessage id={OrderStatusName.WAIT_TO_HANDLE} />,
    color: 'red',
  },
  [OrderStatus.WAIT_TO_OUTBOUND]: {
    text: <FormattedMessage id={OrderStatusName.WAIT_TO_OUTBOUND} />,
    color: 'red',
  },
  [OrderStatus.OUTBOUND_FINISH]: {
    text: <FormattedMessage id={OrderStatusName.OUTBOUND_FINISH} />,
    color: 'green',
  },
  [OrderStatus.TRADE_SUCCESS]: {
    text: <FormattedMessage id={OrderStatusName.TRADE_SUCCESS} />,
    color: 'green',
  },
  [OrderStatus.TRADE_CLOSE]: {
    text: <FormattedMessage id={OrderStatusName.TRADE_CLOSE} />,
    color: '',
  },
};

export const orderStatusTabOption = [
  {
    label: OrderStatusName.WAIT_TO_HANDLE,
    key: OrderStatus.WAIT_TO_HANDLE,
  },
  {
    label: OrderStatusName.WAIT_TO_OUTBOUND,
    key: OrderStatus.WAIT_TO_OUTBOUND,
  },
  {
    label: OrderStatusName.OUTBOUND_FINISH,
    key: OrderStatus.OUTBOUND_FINISH,
  },
  {
    label: OrderStatusName.TRADE_SUCCESS,
    key: OrderStatus.TRADE_SUCCESS,
  },
  {
    label: OrderStatusName.TRADE_CLOSE,
    key: OrderStatus.TRADE_CLOSE,
  },
];
