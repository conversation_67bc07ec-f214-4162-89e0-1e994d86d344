import { FormattedMessage } from "@umijs/max";

export enum PayStatus {
  WAIT_TO_PAY = 0,
  PART_PAY = 1,
  ALL_PAY = 2,
  PAYING = 6,
}

export enum PayStatusName {
  WAIT_TO_PAY = 'sales.order.list.payStatus.waitToPay',
  PART_PAY = 'sales.order.list.payStatus.partPay',
  ALL_PAY = 'sales.order.list.payStatus.allPay',
  PAYING = 'sales.order.list.payStatus.paying',
}

export const payStatusMap = {
  [PayStatus.WAIT_TO_PAY]: {
    text: <FormattedMessage id={PayStatusName.WAIT_TO_PAY} />,
    color: 'red',
    status: 'error',
  },
  [PayStatus.PART_PAY]: {
    text: <FormattedMessage id={PayStatusName.PART_PAY} />,
    color: 'red',
    status: 'error',
  },
  [PayStatus.ALL_PAY]: {
    text: <FormattedMessage id={PayStatusName.ALL_PAY} />,
    color: 'green',
    status: 'success',
  },
  [PayStatus.PAYING]: {
    text: <FormattedMessage id={PayStatusName.PAYING} />,
    color: 'red',
    status: 'error',
  },
};
