
export enum ReceiptStatus {
  NOT_INVOICED = 0,
  INVOICED = 1,
}

export enum ReceiptStatusName {
  NOT_INVOICED = 'sales.order.list.receiptStatus.notInvoiced',
  INVOICED = 'sales.order.list.receiptStatus.invoiced',
}

export const receiptStatusOptions = {
  [ReceiptStatus.INVOICED]: { text: ReceiptStatusName.INVOICED, status: 'success' },
  [ReceiptStatus.NOT_INVOICED]: { text: ReceiptStatusName.NOT_INVOICED, status: 'error' },
};