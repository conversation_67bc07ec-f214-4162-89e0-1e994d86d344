import { FormattedMessage } from "@umijs/max";

export enum PaymentStatus {
  UN_PAY,
  PART_PAY,
  ALL_PAY,
}

export enum PaymentStatusName {
  UN_PAY = 'sales.order.list.receiptPaymentStatus.unPay',
  PART_PAY = 'sales.order.list.receiptPaymentStatus.partPay',
  ALL_PAY = 'sales.order.list.receiptPaymentStatus.allPay',
}

export const paymentStatusMap = {
  [PaymentStatus.UN_PAY]: {
    text: <FormattedMessage id={PaymentStatusName.UN_PAY} />,
    color: 'red',
    status: 'error',
  },
  [PaymentStatus.PART_PAY]: {
    text: <FormattedMessage id={PaymentStatusName.PART_PAY} />,
    color: 'red',
    status: 'error',
  },
  [PaymentStatus.ALL_PAY]: {
    text: <FormattedMessage id={PaymentStatusName.ALL_PAY} />,
    color: 'green',
    status: 'success',
  },
};
