import type { OrderCount } from '@/pages/sales/order/list/types/OrderCount';
import type { DeleteOrderRequest } from '@/pages/sales/order/list/types/delete.order.request';
import type { DeleteOrderResponse } from '@/pages/sales/order/list/types/delete.order.response';
import type { OrderListItemEntity } from '@/pages/sales/order/list/types/order.list.item.entity';
import type { PageQueryOrderByConditionRequest } from '@/pages/sales/order/list/types/page.query.order.by.condition.request';
import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import type { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { EditInvoiceRequest } from '../detail/types/edit.invoice.request';

/**
 * 销售单列表查询
 * @param params
 * @returns
 */
export const pageQueryOrderByCondition = (
  params: PageQueryOrderByConditionRequest & PageRequestParamsType,
) => {
  return request<PageResponseDataType<OrderListItemEntity>>(`/ipmssale/pageQueryOrderByCondition`, {
    data: params,
  });
};

/**
 * 查询各销售单状态对应的单据数量
 */
export const queryOrderStatusCount = (params: PageQueryOrderByConditionRequest) => {
  return request<OrderCount>(`/ipmssale/queryOrderStatusCount`, {
    data: params,
  });
};

/**
 * 撤回订单
 */
export const withdrawOrder = (orderId: string) => {
  return request<boolean>(`/ipmssale/withdrawOrder`, {
    data: { orderId },
  });
};

/**
 * 作废订单
 */
export const deleteOrder = (params: DeleteOrderRequest) => {
  return request<DeleteOrderResponse[]>(`/ipmssale/deleteOrder`, {
    data: params,
  });
};

/**
 * 提交订单
 */
export const submitOrder = (orderId: string) => {
  return request<boolean>(`/ipmssale/submitOrder`, {
    data: { orderId },
  });
};

/**
 * 更新订单开票
 */
export const editOrderInvoice = async (params: Partial<EditInvoiceRequest>) => {
  console.log(params, '开票接口传参');
  return request<boolean>(`/ipmssale/orderInvoice`, {
    data: params,
  });
};
