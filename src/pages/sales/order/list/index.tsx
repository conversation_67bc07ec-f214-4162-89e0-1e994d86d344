import TabsTitle from '@/components/TabsTitle';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import {
  deleteOrder,
  pageQueryOrderByCondition,
  queryOrderStatusCount,
  withdrawOrder,
} from '@/pages/sales/order/list/services';
import type { OrderCount } from '@/pages/sales/order/list/types/OrderCount';
import { OrderStatus, orderStatusTabOption } from '@/pages/sales/order/list/types/OrderStatus';
import type { OrderListItemEntity } from '@/pages/sales/order/list/types/order.list.item.entity';
import { exportData } from '@/utils/exportData';
import { importData } from '@/utils/importData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { history, useIntl, useLocation, useModel } from '@umijs/max';
import { useRequest } from 'ahooks';
import type { TableProps } from 'antd';
import { App, Popconfirm, Space, Tabs, message } from 'antd';
import * as NP from 'number-precision';
import type { Tab } from 'rc-tabs/lib/interface';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import { tableColumns } from './config/tableColumns';
type TableRowSelection<T extends object = object> = TableProps<T>['rowSelection'];

const SalesOrderList = () => {
  const { modal } = App.useApp();
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [count, setCount] = useState<OrderCount>();
  const [params, setParams] = useState<{ orderStatus: string | number; storeIdList?: string[] }>({
    orderStatus: '',
  });

  const location = useLocation();
  const [tableData, setTableData] = useState<OrderListItemEntity[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const selectedRows = useMemo(() => {
    return tableData.filter((item) => selectedRowKeys.includes(item.orderId));
  }, [selectedRowKeys, tableData]);

  const rowSelection: TableRowSelection<OrderListItemEntity> = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };


  const { isSidebarFold } = useModel('layoutModel');

  const { data: accountData = [] } = useRequest(() => queryMemberAccountPage({ pageSize: 100 }))
  const accountList = [{ id: '0', memberAccountName: intl.formatMessage({ id: 'sales.order.list.creditAccount' }) }, ...(accountData?.data ?? [])];


  useEffect(() => {
    if (!location?.state) return;
    // 回填查询表单订单状态
    if (location.state?.orderStatusList) {
      formRef.current?.setFieldValue('orderStatusList', location.state.orderStatusList);
      setParams((t) => ({ ...t, orderStatus: parseInt(location.state.orderStatusList[0]) }));
    }
    // 回填查询表单销售门店
    formRef.current?.setFieldValue('storeIdList', location.state?.storeIdList);
    setParams((t) => ({ ...t, storeIdList: location.state?.storeIdList }));
  }, [location?.state]);

  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 表格列操作
   */
  const operatorColumn: ProColumns<OrderListItemEntity> = {
    title: intl.formatMessage({ id: 'common.column.operation' }),
    valueType: 'option',
    width: 80,
    fixed: 'right',
    render: (_text, record) => {
      const buttons = [];
      if ([OrderStatus.WAIT_TO_HANDLE].includes(record.orderStatus?.orderStatus!)) {
        buttons.push(
          <AuthButton
            isHref
            authority="editOrder"
            key="editOrder"
            onClick={() => history.push(`/sales/order/edit?orderNo=${record.orders?.orderNo!}`)}
          >
            {intl.formatMessage({ id: 'sales.order.list.edit' })}
          </AuthButton>,
        );
      }
      if ([OrderStatus.WAIT_TO_OUTBOUND].includes(record.orderStatus?.orderStatus!)) {
        buttons.push(
          <Popconfirm
            title={intl.formatMessage({ id: 'sales.order.list.confirmWithdraw' })}
            onConfirm={() =>
              withdrawOrder(record.orderId).then((result) => {
                if (result) {
                  actionRef.current?.reload(true);
                  message.success(intl.formatMessage({ id: 'sales.order.list.withdrawSuccess' }));
                }
              })
            }
          >
            <AuthButton isHref authority="withdrawOrder" key="withdrawOrder">
              {intl.formatMessage({ id: 'sales.order.list.withdraw' })}
            </AuthButton>
          </Popconfirm>,
        );
      }
      if (
        [OrderStatus.WAIT_TO_HANDLE, OrderStatus.WAIT_TO_OUTBOUND].includes(
          record.orderStatus?.orderStatus!,
        )
      ) {
        buttons.push(
          <AuthButton
            isHref
            authority="deleteOrder"
            key="crete"
            onClick={() =>
              modal.confirm({
                content: intl.formatMessage({ id: 'sales.order.list.confirmVoid' }),
                onOk: () =>
                  deleteOrder({ deleteOrderItemList: [{ orderId: record.orderId }] }).then(
                    (result) => {
                      if (result?.[0]?.success) {
                        message.success(intl.formatMessage({ id: 'sales.order.list.voidSuccess' }));
                        actionRef.current?.reload(true);
                      } else {
                        message.error(result?.[0]?.returnMsg);
                      }
                    },
                  ),
              })
            }
          >
            {intl.formatMessage({ id: 'sales.order.list.void' })}
          </AuthButton>,
        );
      }
      return buttons;
    },
  };

  return (
    <PageContainer style={{ paddingBottom: '60px' }}>
      <FunProTable<OrderListItemEntity, any>
        className="salesOrderListTable"
        formRef={formRef}
        rowSelection={rowSelection}
        onLoad={(dataSource) => {
          setTableData(dataSource);
        }}
        tableExtraRender={() => (
          <ProCard bodyStyle={{ paddingLeft: 24, paddingTop: 0, paddingBottom: 0 }}>
            <Tabs
              tabBarStyle={{ marginBottom: 0 }}
              activeKey={params.orderStatus}
              onChange={(value) => {
                setParams((t) => ({ ...t, orderStatus: value }));
                formRef.current?.resetFields(['orderStatusList']);
              }}
              items={
                [
                  { key: '', label: intl.formatMessage({ id: 'sales.order.list.all' }) },
                  ...orderStatusTabOption.map((item) => ({
                    key: item.key,
                    label: (
                      <TabsTitle
                        title={intl.formatMessage({ id: item.label })}
                        // @ts-ignore
                        count={count?.[item.key]}
                      />
                    ),
                  })),
                ] as Tab[]
              }
            />
          </ProCard>
        )}
        rowKey="orderId"
        params={params}
        requestPage={(query) => {
          // 如果tab不是全部，则禁用搜索表单的订单状态功能（清空/置灰), 且Tab状态不影响count数量
          console.log('query', query);
          let { orderStatus, orderStatusList, ...rest } = query;
          if (orderStatus) {
            orderStatusList = [orderStatus];
          }
          const params = { ...rest, orderStatusList };
          queryOrderStatusCount(orderStatus ? { ...rest } : params).then((result) => {
            if (result) {
              setCount(result);
            }
          });
          setSelectedRowKeys([]);
          return pageQueryOrderByCondition(params);
        }}
        actionRef={actionRef}
        columns={tableColumns({ operatorColumn, activeTabKey: params.orderStatus, accountList, intl })}
        headerTitle={
          <Space>
            <AuthButton
              key="create"
              type="primary"
              authority="addOrder"
              onClick={() => {
                history.push('/sales/order/edit');
              }}
            >
              {intl.formatMessage({ id: 'sales.order.list.addSales' })}
            </AuthButton>
            <AuthButton
              key="export"
              danger
              authority="exportOrder"
              onClick={() => {
                const exportParams = formRef.current?.getFieldsValue();
                const { orders, ...rest } = exportParams;
                exportData({
                  systemId: 'ETC_SAAS_SYS',
                  taskDesc: '销售订单导出',
                  moduleId: 'SALE_ORDER_EXPORT',
                  params: rest,
                });
              }}
            >
              {intl.formatMessage({ id: 'common.button.export' })}
            </AuthButton>
            <AuthButton
              key="import"
              danger
              authority="importOrder"
              onClick={() => {
                importData({
                  moduleId: 'SALE_ORDER_IMPORT',
                  systemId: 'ETC_SAAS_SYS',
                  taskDesc: '批量导入销售订单',
                  downloadFileName:
                    'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E5%8E%86%E5%8F%B2%E9%94%80%E5%94%AE%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx',
                });
              }}
            >
              {intl.formatMessage({ id: 'sales.order.list.import' })}
            </AuthButton>
          </Space>
        }
      />

      <div className={`bg-white px-6 py-3 fixed bottom-[0] right-[0] z-50 shadow-[0_-2px_4px_rgba(0,0,0,0.05)] ${isSidebarFold ? 'left-[48px]' : 'left-[180px]'}`}>
        <div className="font-bold text-[14px] mb-[4px]">
          <span>{intl.formatMessage({ id: 'sales.order.list.totalOrderCount' })}：{selectedRowKeys.length}</span>
          <span className='ml-[40px]'>{intl.formatMessage({ id: 'sales.order.list.totalSalesAmount' })}：<b className='text-red-500 text-[20px]'> {NP.plus(selectedRows.reduce((total, item) => NP.plus(total, item.orderPrice?.shouldTotalOrderAmountYuan!), 0), 0).toFixed(2)}{intl.formatMessage({ id: 'common.unit.yuan' })} </b></span>
          <span className='ml-[40px]'>{intl.formatMessage({ id: 'sales.order.list.totalGrossProfit' })}：<b className='text-red-500 text-[20px]'> {NP.plus(selectedRows.reduce((total, item) => NP.plus(total, item.orderPrice?.grossProfitYuan!), 0), 0).toFixed(2)}{intl.formatMessage({ id: 'common.unit.yuan' })} </b></span>
        </div>
        <div>
          {
            accountList?.map(item => {
              const total = selectedRows.reduce((total, row) => {
                return NP.plus(total, row?.orderPayDetailList?.find(pay => pay.payeeAccount === item.id)?.payAmountYuan ?? 0)
              }, 0)
              if (total > 0) {
                return <span key={item.id} className='mr-[16px]'>{item.memberAccountName}：¥
                  {total.toFixed(2)}{intl.formatMessage({ id: 'common.unit.yuan' })}
                </span>
              }
              return null
            })
          }
        </div>
      </div>
    </PageContainer >
  );
};

export default withKeepAlive(SalesOrderList);