import FunProTable from '@/components/common/FunProTable';
import LeftTitle from '@/components/LeftTitle';
import type {
  OrderListItemEntity,
  OrderTimeNodeROList,
} from '@/pages/sales/order/list/types/order.list.item.entity';
import { useIntl } from '@umijs/max';
import { getDetailOperatorColumns } from '../../config/DetailColumns';

export interface OperationInfoProps {
  orderDetail?: OrderListItemEntity;
}

const OperationInfo = (props: OperationInfoProps) => {
  const { orderDetail } = props;
  const intl = useIntl();

  return (
    <FunProTable<OrderTimeNodeROList, any>
      scroll={{ x: 800 }}
      headerTitle={<LeftTitle title={intl.formatMessage({ id: 'sales.order.detail.operationRecord' })} />}
      search={false}
      pagination={false}
      className="mt-4"
      dataSource={orderDetail?.orderTimeNodeROList ?? []}
      options={false}
      columns={getDetailOperatorColumns(intl)}
    />
  );
};

export default OperationInfo;
