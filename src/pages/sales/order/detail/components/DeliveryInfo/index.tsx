import { OrderListItemEntity } from '@/pages/sales/order/list/types/order.list.item.entity';
import LeftTitle from '@/components/LeftTitle';
import { ProCard, ProDescriptions } from '@ant-design/pro-components';
import { DeliveryMethod } from '@/pages/sales/order/edit/types/DeliveryMethod';
import { useIntl } from '@umijs/max';

export interface DeliveryInfoProps {
  orderDetail?: OrderListItemEntity;
}

const DeliveryInfo = (props: DeliveryInfoProps) => {
  const { orderDetail } = props;
  const intl = useIntl();

  if (
    [DeliveryMethod.SELF_PICKUP].includes(
      orderDetail?.orderFixedDistributionList?.[0]?.distributionMode!,
    )
  ) {
    return null;
  }

  return (
    <ProCard className="mt-4" title={<LeftTitle title={intl.formatMessage({ id: 'sales.order.detail.deliveryInfo' })} />}>
      <ProDescriptions
        column={4}
        dataSource={{
          distributionModeName: orderDetail?.orderFixedDistributionList?.[0].distributionModeName,
          logisticsCompanyName: orderDetail?.orderFixedDistributionList?.[0].logisticsCompanyName,
          logisticsNo: orderDetail?.orderFixedDistributionList?.[0]?.logisticsNo,
          address: `${orderDetail?.orderFixedAddressList?.[0]?.consigneeProvinceName ?? ''}${orderDetail?.orderFixedAddressList?.[0]?.consigneeCityName ?? ''
            }${orderDetail?.orderFixedAddressList?.[0]?.consigneePrefectureName ?? ''}${orderDetail?.orderFixedAddressList?.[0]?.consigneeDetail ?? ''
            }`,
        }}
        columns={[
          {
            title: intl.formatMessage({ id: 'sales.order.detail.deliveryMethod' }),
            key: 'distributionModeName',
            dataIndex: 'distributionModeName',
          },
          {
            title: intl.formatMessage({ id: 'sales.order.detail.logisticsCompany' }),
            key: 'logisticsCompanyName',
            dataIndex: 'logisticsCompanyName',
          },
          {
            title: intl.formatMessage({ id: 'sales.order.detail.logisticsNumber' }),
            key: 'logisticsNo',
            dataIndex: 'logisticsNo',
          },
          {
            title: intl.formatMessage({ id: 'sales.order.detail.deliveryAddress' }),
            key: 'address',
            dataIndex: 'address',
          },
        ]}
      />
    </ProCard>
  );
};

export default DeliveryInfo;
