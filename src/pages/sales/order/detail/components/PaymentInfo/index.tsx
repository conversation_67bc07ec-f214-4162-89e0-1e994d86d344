import FunProTable from '@/components/common/FunProTable';
import LeftTitle from '@/components/LeftTitle';
import type {
  OrderListItemEntity,
  OrderPayDetailList,
} from '@/pages/sales/order/list/types/order.list.item.entity';
import { useIntl } from '@umijs/max';
import { getDetailPaymentColumns } from '../../config/DetailColumns';

export interface PaymentInfoProps {
  orderDetail?: OrderListItemEntity;
}

const PaymentInfo = (props: PaymentInfoProps) => {
  const { orderDetail } = props;
  const intl = useIntl();

  return (
    <FunProTable<OrderPayDetailList, any>
      headerTitle={<LeftTitle title={intl.formatMessage({ id: 'sales.order.detail.settlementRecord' })} />}
      search={false}
      scroll={{ x: 800 }}
      pagination={false}
      className="mt-4"
      dataSource={
        orderDetail?.orderPayDetailList?.map((item) => ({
          ...item,
          payeeAccountName: item?.payeeAccountName ?? item.payKindName,
        })) ?? []
      }
      options={false}
      columns={getDetailPaymentColumns(intl)}
    />
  );
};

export default PaymentInfo;
