import ColumnRender from '@/components/ColumnRender';
import type { ProColumns } from '@ant-design/pro-components';
import type {
  OrderGoodsROList,
  OrderPayDetailList,
  OrderTimeNodeROList,
} from '../../list/types/order.list.item.entity';

export const getDetailColumns = (intl: IntlShape): ProColumns<OrderGoodsROList>[] => [
  {
    title: intl.formatMessage({ id: 'common.column.index' }),
    valueType: 'index',
    width: 40,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.productCode' }),
    dataIndex: 'itemSn',
    width: 100,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.productName' }),
    dataIndex: 'itemName',
    width: 140,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.oeNumber' }),
    dataIndex: 'oeNo',
    width: 140,
    renderText: (text) => ColumnRender.ArrayColumnRender(text?.split(',') as string[]),
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.brandPartNumber' }),
    dataIndex: 'brandPartNo',
    width: 100,
    renderText: (text) => ColumnRender.ArrayColumnRender(text?.split(',') as string[]),
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.brand' }),
    dataIndex: 'brandName',
    width: 100,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.category' }),
    dataIndex: 'categoryName',
    width: 100,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.unit' }),
    width: 50,
    dataIndex: 'unitName',
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.salePrice' }),
    dataIndex: 'unitPriceYuan',
    valueType: 'money',
    width: 80,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.discountPrice' }),
    dataIndex: 'actualSellingUnitPriceYuan',
    valueType: 'money',
    width: 80,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.saleQuantity' }),
    dataIndex: 'saleNum',
    width: 60,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.subtotal' }),
    dataIndex: 'actualSellingTotalAmountYuan',
    valueType: 'money',
    width: 80,
  },
];
/**
 * 支付流水
 */
export const getDetailPaymentColumns = (intl: IntlShape): ProColumns<OrderPayDetailList>[] => [
  {
    title: intl.formatMessage({ id: 'sales.order.detail.settlementTime' }),
    dataIndex: 'payTime',
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.settlementAccount' }),
    dataIndex: 'payeeAccountName',
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.settlementAmount' }),
    dataIndex: 'payAmountYuan',
    valueType: 'money',
  },
];

/**
 * 操作记录
 */
export const getDetailOperatorColumns = (intl: IntlShape): ProColumns<OrderTimeNodeROList>[] => [
  {
    title: intl.formatMessage({ id: 'sales.order.detail.operationTime' }),
    dataIndex: 'nodeTime',
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.orderNode' }),
    dataIndex: 'operateTypeName',
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'sales.order.detail.operator' }),
    dataIndex: 'operator',
    search: false,
  },
];
