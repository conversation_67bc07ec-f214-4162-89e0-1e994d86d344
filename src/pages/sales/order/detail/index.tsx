import BaseInfo from '@/pages/sales/order/detail/components/BaseInfo';
import DeliveryInfo from '@/pages/sales/order/detail/components/DeliveryInfo';
import GoodsInfo from '@/pages/sales/order/detail/components/GoodsInfo';
import OperationInfo from '@/pages/sales/order/detail/components/OperationInfo';
import PaymentInfo from '@/pages/sales/order/detail/components/PaymentInfo';
import { getOrderByOrderNoForDbReturnSelected } from '@/pages/sales/order/edit/services';
import { useSearchParams } from '@@/exports';
import { PageContainer } from '@ant-design/pro-components';
import { useEffect, useState } from 'react';
import { useActivate } from 'react-activation';
import type { OrderListItemEntity } from '../list/types/order.list.item.entity';

export default () => {
  const [searchParams] = useSearchParams();
  const orderNo = searchParams.get('orderNo');
  const [orderDetail, setOrderDetail] = useState<OrderListItemEntity>();
  const [loading, setLoading] = useState(false);

  /**
   * 查询订单信息
   */
  const queryDetail = () => {
    if (orderNo) {
      setLoading(true);
      getOrderByOrderNoForDbReturnSelected(orderNo)
        .then((result) => {
          setOrderDetail(result);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  useActivate(() => {
    queryDetail();
  });

  useEffect(() => {
    queryDetail();
    return () => {
      setOrderDetail(undefined);
    };
  }, [orderNo]);

  return (
    <PageContainer loading={loading}>
      <BaseInfo orderDetail={orderDetail} queryDetail={queryDetail} />
      <GoodsInfo orderDetail={orderDetail} />
      <PaymentInfo orderDetail={orderDetail} />
      <DeliveryInfo orderDetail={orderDetail} />
      <OperationInfo orderDetail={orderDetail} />
    </PageContainer>
  );
};
