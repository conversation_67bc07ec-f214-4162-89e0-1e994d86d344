import ConfirmModal from '@/components/ConfirmModal';
import GoodsSearch from '@/components/GoodsSearch';
import { GoodsSearchBizType } from '@/components/GoodsSearch/types/BizType';
import LeftTitle from '@/components/LeftTitle';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { getCstDetail, getCstList } from '@/pages/customer/list/services';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { queryStoreByAccount } from '@/pages/system/user/services';
import { CashRefundTypeValueEnum, RefundTypeEnum, RefundTypeValueEnum } from '@/types/CommonStatus';

import { KeepAliveTabContext } from '@/layouts/context';
import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import type { BaseOptionsType } from '@/types/BaseOptionsType';
import withKeepAlive from '@/wrappers/withKeepAlive';
import {
  DeleteOutlined,
  FileAddOutlined,
  FileSearchOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import type { FormListActionType } from '@ant-design/pro-components';
import {
  CheckCard,
  PageContainer,
  ProCard,
  ProForm,
  ProFormDependency,
  ProFormList,
  ProFormMoney,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { history, useIntl, useLocation } from '@umijs/max';
import { useAsyncEffect, useDebounceFn, useRequest } from 'ahooks';
import type { FormInstance, GetProps } from 'antd';
import { Button, Checkbox, Col, ConfigProvider, Flex, Row, Space, Spin, message } from 'antd';
import type { DefaultOptionType } from 'antd/es/select';
import { defaultTo, isEmpty, isUndefined, set, sum, uniqueId, values } from 'lodash';
import { useContext, useEffect, useRef, useState } from 'react';
import ReturnsDetailColumns from './config/ReturnsDetailColumns';
import ReturnsOrdersColumns from './config/ReturnsOrdersColumns';
import {
  addItem,
  confirmRefund,
  createOrder,
  deleteItem,
  directIn,
  getAfterSaleDetail,
  getRefundablePaged,
  modifyItem,
  modifyOrder,
  submitOrder,
} from './services';
import type { AfterSaleOrderGoodsRo } from './types/ReturnsAfterSaleDetailEntity';
import type {
  ReturnsGoodsCreateMain,
  ReturnsOrderModifyRefund,
  ReturnsOrderModifyRefundDetail,
  ReturnsOrderModifyType,
} from './types/ReturnsGoodsCreateItemType';
import type { ReturnsOrderEntity } from './types/ReturnsOrderEntity';
import type { ReturnsOrderQuery } from './types/ReturnsOrderQuery';
import type { ReturnsOrderType } from './types/ReturnsOrderType';

type TabKeyType = 'goods' | 'orders';
const ReturnsOperation = () => {
  const intl = useIntl();

  const plainOptions = [
    { label: intl.formatMessage({ id: 'sales.returns.operation.confirmSettlement' }), value: '0' },
    { label: intl.formatMessage({ id: 'sales.returns.operation.directInbound' }), value: '1' },
    { label: intl.formatMessage({ id: 'sales.returns.operation.printAfterSubmit' }), value: '2' },
  ];
  const [tabelId, setTableId] = useState<string>('returns_table_1');
  // 加载中
  const [loading, setLoading] = useState<boolean>(false);
  // 退货单明细
  // 可编辑行
  const [editorRows, setEditorRows] = useState<string[]>([]);
  // 头部表单值
  const [formValues, setFormValues] = useState<ReturnsGoodsCreateMain>();
  const [orderEditorRows, setOrderEditorRows] = useState<string[]>([]);
  // 退货明细
  const [dataSourceCache, setDataSourceCache] = useState<AfterSaleOrderGoodsRo[]>([]);
  // 退货明细编辑行
  const [editorDataSourceCache, setEditorDataSourceCache] = useState<AfterSaleOrderGoodsRo[]>([]);
  // 商品明细[编辑行]
  const [orderDataCache, setOrderDataCache] = useState<ReturnsOrderEntity[]>([]);
  const formRef = useRef<FormInstance<ReturnsGoodsCreateMain>>();
  const bottomFormRef = useRef<FormInstance<ReturnsOrderModifyRefund>>();
  const [orderId, setOrderId] = useState<string>('');
  const [orderNo, setOrderNo] = useState<string>('');
  const { state } = useLocation();
  console.log('state', state);

  // 是否为新增退货单
  const [isAdd, setIsAdd] = useState<boolean>(false);
  useEffect(() => {
    setIsAdd(isEmpty(orderId) && isEmpty(orderNo));
  }, [orderId, orderNo]);
  useEffect(() => {
    if (state) {
      const { orderId: oid, orderNo: oldOrderNo } = state as unknown as {
        orderId: string;
        orderNo: string;
        name: string;
      };
      setOrderId(oid);
      setOrderNo(oldOrderNo);
      getDetail({ orderId: oid, orderNo: oldOrderNo });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state]);
  // 退款状态
  const [orderStatusName, setOrderStatusName] = useState<string>();
  // 商品总数
  const [totalCount, setTotalCount] = useState<number>(0);
  // 退款总金额
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [tabActiveKey, setTabActiveKey] = useState<TabKeyType>('orders');

  const actionRef = useRef<ActionType>();
  const formListActionRef = useRef<FormListActionType>();
  const onChange = (key: TabKeyType) => {
    setTabActiveKey(key);
  };
  const { closeTab } = useContext(KeepAliveTabContext);

  const [checkedList, setCheckedList] = useState<string[]>([]);
  const onCheckboxChange = (list: string[]) => {
    setCheckedList(list);
  };

  const [confirmModalProps, setConfirmModalProps] = useState<GetProps<typeof ConfirmModal>>({
    open: false,
  });
  /** 查询退货详情*/
  const getDetail = async (params: ReturnsOrderType) => {
    if (params.orderId && params.orderNo) {
      const result = await getAfterSaleDetail(params);
      if (result?.main) {
        const {
          orderAmount,
          tagIds,
          cstId,
          cstName,
          backWarehouseId,
          backWarehouseName,
          storeId,
          storeName,
        } = result.main;
        // 给你加了这个，如果存在1=销售单退回 存在2=商品退货
        if (tagIds.includes(1)) {
          setTabActiveKey('orders');
        } else if (tagIds.includes(2)) {
          setTabActiveKey('goods');
        }
        setCstOptions([{ label: cstName, value: cstId }]);
        setWarehouseOptions([{ label: backWarehouseName, value: backWarehouseId }]);
        setStoreOptions([{ label: storeName, value: storeId }]);
        // 设置客户/退货门店/退回仓库默认值
        formRef.current?.setFieldsValue({
          cstId,
          backWarehouseId,
          storeId,
        });
        setFormValues({ cstId, backWarehouseId, storeId });
        // 设置退款金额
        setTotalAmount(orderAmount);
        // 设置备注
        bottomFormRef.current?.setFieldValue('remark', result.main.remark);
      }
      // 设置退货明细
      if (result?.goods) {
        setTotalCount(sum(result?.goods.map((t) => t.refundNum)));
        setDataSourceCache(
          result?.goods.map((t) => ({
            ...t,
            storeName: result.main.storeName,
            returnsId: t.orgOrderNo + t.itemId,
          })) ?? [],
        );
        setEditorRows(result.goods?.map((item) => item.id));
        setTableId(() => uniqueId('returns_'));
      }
      // 设置结算信息
      if (result?.refunds) {
        const refundType = result.refunds[0].refundType;
        bottomFormRef.current?.setFieldValue('refundType', refundType);

        if (refundType == RefundTypeEnum.Cash) {
          if (result?.refundDetails) {
            bottomFormRef.current?.setFieldValue(
              'refundDetails',
              result.refundDetails.map((t) => ({
                accountId: t.accountId,
                refundAmount: t.refundAmount,
              })),
            );
          }
        }
      }
      // 设置退货单状态
      if (result?.status) {
        const { orderStatusName: neworderStatusName } = result.status;
        setOrderStatusName(neworderStatusName);
      }
    } else {
      setIsAdd(true);
      setOrderStatusName('');
      setEditorRows([]);
      setDataSourceCache([]);
      setTotalCount(0);
      setTotalAmount(0);
      bottomFormRef.current?.resetFields();
      formRef.current?.resetFields(['cstId']);
    }
  };

  const [refundOptions, setRefundOptions] = useState<DefaultOptionType[]>([]);
  const [usedAmount, setUsedAmount] = useState<number>();
  const [availableAmount, setAvailableAmount] = useState<number>();
  // 选择客户时查询客户详情
  useAsyncEffect(async () => {
    if (formValues?.cstId) {
      const cstDetail = await getCstDetail({ cstId: formValues.cstId });
      if (cstDetail?.settle?.credit) {
        setRefundOptions(RefundTypeValueEnum);
        if (cstDetail?.settle?.usedAmount) {
          setUsedAmount(cstDetail?.settle?.usedAmount);
        }
        if (cstDetail?.settle?.availableAmount) {
          setAvailableAmount(cstDetail?.settle?.availableAmount);
        }
        // 新增时才需要默认值
        if (isAdd) {
          bottomFormRef.current?.setFieldValue('refundType', RefundTypeEnum.Account);
        }
      } else {
        setRefundOptions(CashRefundTypeValueEnum);
        // 新增时才需要默认值
        if (isAdd) {
          bottomFormRef.current?.setFieldValue('refundType', RefundTypeEnum.Cash);
          bottomFormRef.current?.setFieldValue('refundDetails', []);
        }
      }
    }
  }, [formValues?.cstId, isAdd]);

  /**
   * 删除
   */
  const onRemoveClick = async (row: AfterSaleOrderGoodsRo) => {
    const data = await deleteItem({ ids: [row.id], orderId, orderNo });
    if (data) {
      getDetail({ orderId, orderNo });
    }
  };

  const checkAmountAndCount = (item: ReturnsOrderEntity) => {
    // 退款数量
    let refundNum = 0;
    // 退款金额
    let refundAmount = item.refundAmount;
    if (tabActiveKey == 'orders') {
      if ((item?.refundAmount ?? 0) < 0) {
        message.error(intl.formatMessage({ id: 'sales.returns.operation.inputReturnAmount' }));
        return;
      }
      if (!item?.refundNum) {
        message.error(intl.formatMessage({ id: 'sales.returns.operation.inputReturnQuantity' }));
        return;
      } else {
        refundNum = item.refundNum;
      }
    } else if (tabActiveKey == 'goods') {
      if ((item?.price ?? 0) < 0) {
        message.error(intl.formatMessage({ id: 'sales.returns.operation.inputReturnAmount' }));
        return;
      } else {
        refundAmount = item.price;
      }
      if (!item?.number) {
        message.error(intl.formatMessage({ id: 'sales.returns.operation.inputReturnQuantity' }));
        return;
      } else {
        refundNum = item.number;
      }
    }
    return { refundAmount, refundNum };
  };
  /**
   * 新建退货记录（退货单退货/商品退货）
   * @param item
   */
  const handleCreate = async (itemList: any[]) => {
    const item = itemList[0];
    const v = await formRef.current?.validateFields(['cstId', 'storeId', 'backWarehouseId']);
    const bottomValues = bottomFormRef.current?.getFieldsValue?.();
    const checkRes = checkAmountAndCount(item);
    if (isEmpty(checkRes)) return;
    setLoading(true);
    const result = await createOrder({
      main: { ...v, remark: bottomValues?.remark },
      refund: {
        refundType: bottomValues?.refundType,
      },
      items: [
        {
          itemId: item?.itemId, // TODO itemId不唯一
          refundNum: checkRes.refundNum,
          unitAmount: checkRes.refundAmount,
          costAmount: item?.costAmount,
          saleNum: item?.saleNum,
          orgOrderNo: item?.orderNo,
        },
      ],
    });
    if (result) {
      setLoading(false);
      const { orderId: newOrderId, orderNo: newOrderNo } = result;
      message.success(intl.formatMessage({ id: 'sales.returns.operation.addSuccess' }));
      setOrderId(newOrderId);
      setOrderNo(newOrderNo);
      getDetail({ orderId: newOrderId, orderNo: newOrderNo });
    }
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };

  // 提交订单
  const onSubmit = async () => {
    const params = { orderId, orderNo };
    const result = await submitOrder(params);
    if (result) {
      // 确认退款
      if (checkedList.includes('0')) {
        await confirmRefund(params);
      }
      // 直接入库
      if (checkedList.includes('1')) {
        await directIn(params);
      }
      if (checkedList.includes('2')) {
        window.open(
          `/print?orderNo=${orderNo}&orderId=${orderId}&printType=${PrintType.salesReturnOrder}`,
        );
      }
      message.success(intl.formatMessage({ id: 'sales.returns.operation.submitSuccess' }));
      closeTab();
      history.push('/sales/returns/list');
    }
  };

  // 关闭弹窗
  const onCancel = () => {
    setConfirmModalProps((preProps) => ({
      ...preProps,
      open: false,
    }));
  };

  /**
   * 覆盖或者叠加记录
   * @param item 操作的记录
   * @param flag true 是否覆盖
   */
  const itemUpdate = async (item: {
    id: string;
    refundNum?: number;
    refundAmount?: number;
    cause?: string;
  }) => {
    setLoading(true);
    const result = await modifyItem({
      id: item.id,
      orderId,
      orderNo,
      refundNum: item?.refundNum ?? 0,
      unitAmount: item?.refundAmount ?? 0,
      cause: item?.cause,
    });
    if (result) {
      setLoading(false);
      message.success(intl.formatMessage({ id: 'sales.returns.operation.operationSuccess' }));
      onCancel();
      getDetail({ orderId, orderNo });
    }
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };
  /**
   * order 可退货订单新增行
   * @param item
   * @returns
   */
  const handleAdd = async (itemList: any[]) => {
    const item = itemList[0];
    const checkRes = checkAmountAndCount(item);
    if (isEmpty(checkRes)) return;
    if (dataSourceCache.length >= 200) {
      message.warning(intl.formatMessage({ id: 'sales.returns.operation.maxGoodsWarning' }));
      return;
    }
    // 新增商品
    const result = await addItem({
      orderId,
      orderNo,
      items: [
        {
          itemId: item.itemId, // TODO itemId不唯一
          refundNum: checkRes.refundNum,
          unitAmount: checkRes.refundAmount,
          costAmount: item?.costAmount,
          saleNum: item?.saleNum,
          orgOrderNo: item?.orderNo,
        },
      ],
    });
    if (result) {
      message.success(intl.formatMessage({ id: 'sales.returns.operation.addSuccess' }));
      getDetail({ orderId, orderNo });
    }
  };
  /**
   * 更新退货订单
   */
  const updateOrder = async (params: ReturnsOrderModifyType, reload?: boolean) => {
    setLoading(true);
    const result = await modifyOrder(params);
    if (result) {
      setLoading(false);
      if (reload) {
        getDetail({ orderId, orderNo });
      }
    }
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };

  const { run: runUpdateOrder } = useDebounceFn(
    (params: ReturnsOrderModifyType, reload?: boolean) => updateOrder(params, reload),
    {
      wait: 1000,
    },
  );
  // 加载客户下拉列表
  const { runAsync: loadCstData } = useRequest(getCstList, {
    manual: true,
    debounceWait: 200,
  });
  const [cstOptions, setCstOptions] = useState<BaseOptionsType<string, string>[]>([]);
  useAsyncEffect(async () => {
    const cstSlectData = await loadCstData({ cstStatus: 0 });
    const options = cstSlectData?.map((t) => ({ value: t.cstId, label: t.cstName }));
    // 设置默认客户
    if (options) {
      setCstOptions(options);
    }
  }, []);
  // 加载门店下拉列表
  const { runAsync: loadStoreData } = useRequest(queryStoreByAccount, {
    manual: true,
    debounceWait: 200,
  });
  const [storeOptions, setStoreOptions] = useState<BaseOptionsType<string, string>[]>([]);
  useAsyncEffect(async () => {
    if (isAdd) {
      const storeSlectData = await loadStoreData({ status: 1 });
      const options = storeSlectData?.map((t) => ({ value: t.id, label: t.name }));
      if (options) {
        setStoreOptions(options);
        // 设置默认门店
        const storeId = options[0].value;
        setFormValues((pre) => ({ ...pre, storeId }));
        formRef.current?.setFieldValue('storeId', storeId);
      }
    }
  }, [isAdd]);
  // 加载退回仓库下拉列表
  const { runAsync: loadWarehouseData } = useRequest(warehouseList, {
    manual: true,
    debounceWait: 200,
  });
  const [warehouseOptions, setWarehouseOptions] = useState<BaseOptionsType<string, string>[]>([]);
  useAsyncEffect(async () => {
    if (formValues?.storeId && isAdd) {
      const { warehouseStoreRelationRoList: warehouseSlectData } = await loadWarehouseData({
        state: YesNoStatus.YES,
        storeIdList: [formValues.storeId],
      });
      const options = warehouseSlectData?.map((t) => ({
        value: t.warehouseId,
        label: t.warehouseName,
      }));
      // 设置默认仓库
      if (options && !isEmpty(options)) {
        const backWarehouseId = options[0].value;
        setWarehouseOptions(options);
        formRef.current?.setFieldValue('backWarehouseId', backWarehouseId);
        setFormValues((pre) => ({ ...pre, backWarehouseId }));
      }
    }
  }, [formValues?.storeId, isAdd]);
  // 选择门店时加载现款账户列表
  const [accountList, setAccountList] = useState<DefaultOptionType[]>([]);
  useAsyncEffect(async () => {
    if (formValues?.storeId && !isAdd) {
      const result = await queryMemberAccountPage({
        belongToStore: [formValues?.storeId],
      });
      if (result?.data) {
        setAccountList(
          result.data.map((item) => ({
            label: item.memberAccountName,
            value: item.id,
          })),
        );
      }
    }
  }, [formValues?.storeId, isAdd]);

  return (
    <PageContainer>
      <ConfigProvider
        theme={{
          token: {
            colorPrimary: '#F49C1F',
          },
          components: {
            InputNumber: {
              controlWidth: 80,
            },
          },
        }}
      >
        <ProCard bodyStyle={{ paddingTop: 24, paddingBottom: 8 }}>
          <ProForm<ReturnsGoodsCreateMain>
            formRef={formRef}
            onValuesChange={(_, _formValues) => {
              setFormValues(_formValues);
            }}
            submitter={false}
            disabled={!isAdd}
            layout="horizontal"
          >
            <Row gutter={40}>
              <Col span={8}>
                <ProFormSelect<string>
                  fieldProps={{
                    filterOption: false,
                  }}
                  showSearch={true}
                  debounceTime={300}
                  label={intl.formatMessage({ id: 'sales.returns.operation.customer' })}
                  name="cstId"
                  rules={[{ required: true, message: intl.formatMessage({ id: 'sales.returns.operation.selectCustomer' }) }]}
                  request={(query) =>
                    getCstList({ keyword: query.keyWords, cstStatus: 0 }).then((result) => {
                      const list = result.map((item) => ({
                        label: item.cstName,
                        value: item.cstId,
                      }));
                      return list;
                    })
                  }
                />
              </Col>
              <Col span={8}>
                <ProFormSelect<string>
                  showSearch={false}
                  rules={[{ required: true, message: intl.formatMessage({ id: 'sales.returns.operation.selectReturnStore' }) }]}
                  label={intl.formatMessage({ id: 'sales.returns.operation.returnStore' })}
                  name="storeId"
                  options={storeOptions}
                />
              </Col>
              <Col span={8}>
                <ProFormSelect
                  showSearch={false}
                  rules={[{ required: true, message: intl.formatMessage({ id: 'sales.returns.operation.selectReturnWarehouse' }) }]}
                  label={intl.formatMessage({ id: 'sales.returns.operation.returnWarehouse' })}
                  name="backWarehouseId"
                  options={warehouseOptions}
                />
              </Col>
            </Row>
          </ProForm>
        </ProCard>
        <ProCard className="mt-4" bodyStyle={{ paddingTop: 24, paddingBottom: 0 }}>
          <CheckCard.Group
            disabled={!isAdd}
            value={tabActiveKey}
            onChange={(checked) => {
              if (checked) {
                onChange(checked as TabKeyType);
              }
            }}
          >
            <Space size={24}>
              <CheckCard
                style={{ marginBottom: 0 }}
                avatar={
                  <div className="h-[52px] flex flex-row justify-center items-center">
                    <FileSearchOutlined style={{ fontSize: 32 }} />
                  </div>
                }
                description={
                  <Flex vertical gap={0}>
                    <span className="font-semibold text-[16px] text-[#000000D9]">{intl.formatMessage({ id: 'sales.returns.operation.salesOrderReturn' })}</span>
                    <span className="text-[#00000073]">{intl.formatMessage({ id: 'sales.returns.operation.salesOrderReturnDesc' })}</span>
                  </Flex>
                }
                value="orders"
              // style={{ width: 240 }}
              />
              <CheckCard
                style={{ marginBottom: 0 }}
                avatar={
                  <div className="h-[52px] flex flex-row justify-center items-center">
                    <FileAddOutlined style={{ fontSize: 32 }} />
                  </div>
                }
                description={
                  <Flex vertical>
                    <span className="font-semibold text-[16px] text-[#000000D9]">{intl.formatMessage({ id: 'sales.returns.operation.goodsReturn' })}</span>
                    <span className="text-[#00000073]">{intl.formatMessage({ id: 'sales.returns.operation.goodsReturnDesc' })}</span>
                  </Flex>
                }
                value="goods"
              // style={{ width: 240 }}
              />
            </Space>
          </CheckCard.Group>
        </ProCard>
        <Spin spinning={loading}>
          {/* 退货单明细 */}
          {tabActiveKey == 'orders' && (
            <FunProTable<ReturnsOrderEntity, ReturnsOrderQuery>
              key="reset-ant-pro-table-search"
              className="reset-ant-pro-table-search"
              manualRequest={true}
              requestPage={async (params) => {
                if (tabActiveKey == 'orders') {
                  try {
                    const v = await formRef.current?.validateFields([
                      'cstId',
                      'storeId',
                      'backWarehouseId',
                    ]);
                    if (values(v).length == 3) {
                      const result = await getRefundablePaged({ ...v, ...params });
                      if (result) {
                        const { data, total } = result;
                        if (data) {
                          setOrderEditorRows(
                            result.data.map((item) => `${item.orderNo}${item.itemId}`),
                          );
                          return {
                            data: data.map((t) => ({
                              ...t,
                              refundNum: defaultTo(t?.refundableNum, 0) >= 1 ? 1 : 0,
                              refundAmount: t.unitAmount,
                              returnsId: t.orderNo + t.itemId,
                            })),
                            success: true,
                            total,
                          };
                        }
                      }
                    }
                  } catch (error) {
                    console.error(error);
                  }
                }
                return { data: [], success: true };
              }}
              editable={{
                type: 'single',
                editableKeys: orderEditorRows,
                actionRender: () => [],
                onValuesChange: (record, recordList) => {
                  setOrderDataCache(recordList);
                },
              }}
              onDataSourceChange={(dataSource) => {
                setOrderDataCache(dataSource);
              }}
              rowKey="returnsId"
              options={false}
              columns={ReturnsOrdersColumns({
                addedItemPurchaseOrderNo: dataSourceCache?.map((item) => item.returnsId),
                handleAdd: (item) => (isEmpty(orderId) ? handleCreate([item]) : handleAdd([item])),
                orderDataCache,
              })}
            />
          )}
          {/* 商品明细 */}
          {tabActiveKey == 'goods' && (
            <ProCard bordered={false} bodyStyle={{ marginTop: 8 }}>
              <GoodsSearch
                warehouseId={formValues?.backWarehouseId}
                bizType={GoodsSearchBizType.SalesReturn}
                addedItemSns={dataSourceCache?.map((item) => item.itemSn ?? '')}
                onAdd={(itemList) =>
                  isEmpty(orderId) ? handleCreate(itemList) : handleAdd(itemList)
                }
              />
            </ProCard>
          )}

          {/* 退货明细 */}
          <FunProTable<AfterSaleOrderGoodsRo, any>
            key={tabelId}
            className="mt-4"
            rowKey="id"
            pagination={false}
            headerTitle={
              <Space size={16}>
                <LeftTitle title={intl.formatMessage({ id: 'sales.returns.operation.returnDetails' })} />
                {orderNo && <span className="font-normal text-base ml-8">{intl.formatMessage({ id: 'sales.returns.operation.returnOrderNo' })}: {orderNo}</span>}
                {orderStatusName && (
                  <span className="font-normal text-base ml-8">{intl.formatMessage({ id: 'sales.returns.operation.returnStatus' })}: {orderStatusName}</span>
                )}
              </Space>
            }
            onRow={(record) => {
              return {
                onBlur: () => {
                  const result = editorDataSourceCache.find((t) => t.returnsId == record.returnsId);
                  if (result) {
                    const { unitAmount, refundNum, cause } = result;
                    itemUpdate({
                      id: result.id,
                      refundAmount: unitAmount,
                      refundNum: refundNum,
                      cause: cause,
                    });
                  }
                },
              };
            }}
            editable={{
              editableKeys: editorRows,
              onValuesChange: (_, vv) => {
                setEditorDataSourceCache(vv);
              },
              actionRender: (row) => {
                return [
                  <Button
                    className="px-0"
                    type="link"
                    key="delete"
                    onClick={() => onRemoveClick(row)}
                  >
                    {intl.formatMessage({ id: 'sales.returns.operation.delete' })}
                  </Button>,
                ];
              },
            }}
            search={false}
            actionRef={actionRef}
            columns={ReturnsDetailColumns()}
            dataSource={dataSourceCache}
          />
          <ProCard>
            <ProForm<ReturnsOrderModifyRefund>
              key="bottomForm"
              formRef={bottomFormRef}
              validateTrigger="onBlur"
              submitter={false}
              disabled={isAdd}
              onValuesChange={async (
                changeValues: ReturnsOrderModifyRefund,
                allValues: ReturnsOrderModifyRefund,
              ) => {
                try {
                  // 此处只关注 refundDetails
                  if (changeValues?.remark || changeValues.refundType) return;
                  const bottomValues = await bottomFormRef.current?.validateFields?.();
                  console.log(changeValues, allValues, bottomValues);
                  if (!bottomValues?.refundDetails) return;
                  // 如果有非法结算信息则不保存
                  const validArray: ReturnsOrderModifyRefundDetail[] = [];
                  const inValidArray: ReturnsOrderModifyRefundDetail[] = [];

                  const array = bottomValues.refundDetails;
                  for (let i = 0; i < array.length; i++) {
                    const item = array[i];
                    if (
                      !isUndefined(item) &&
                      isUndefined(item?.accountId) &&
                      isUndefined(item?.refundAmount)
                    ) {
                      continue;
                    }
                    if (
                      isEmpty(item) ||
                      isEmpty(item?.accountId) ||
                      (item.refundAmount ?? 0) <= 0
                    ) {
                      inValidArray.push(item);
                    } else {
                      validArray.push(item);
                    }
                  }
                  if (!isEmpty(inValidArray) || isEmpty(validArray)) return;
                  const reqParams = {
                    orderId,
                    orderNo,
                    refund: { refundType: allValues.refundType, refundDetails: validArray },
                  };
                  runUpdateOrder(reqParams, true);
                } catch (e) {
                  console.log(e);
                }
              }}
            >
              <Row gutter={80} className="customer-ant-form-item-label">
                <Col span={8}>
                  <ProFormSelect<number>
                    // width={500}
                    label={intl.formatMessage({ id: 'sales.returns.operation.settlementMethod' })}
                    options={refundOptions}
                    name="refundType"
                    allowClear={false}
                    onChange={async (value) => {
                      const reqParams: ReturnsOrderModifyType = {
                        orderId,
                        orderNo,
                        refund: { refundType: value },
                      };
                      // 结算方式修改为挂账时直接更新
                      if (value == 0) {
                        const defaultAccountId = accountList[0].value;
                        const refundDetails = [
                          { accountId: defaultAccountId, refundAmount: totalAmount },
                        ];
                        set(reqParams, 'refund.refundDetails', refundDetails);
                        bottomFormRef?.current?.setFieldValue('refundDetails', refundDetails);
                      }
                      runUpdateOrder(reqParams, true);
                    }}
                  />
                  <ProFormDependency name={['refundType']}>
                    {(props) => {
                      const { refundType } = props;
                      if (refundType === 10) {
                        return (
                          <div className="text-gray-500 availableAmount">
                            {intl.formatMessage({
                              id: 'sales.returns.operation.usedAvailable'
                            }, {
                              used: defaultTo(usedAmount, '-'),
                              available: defaultTo(availableAmount, '-')
                            })}
                          </div>
                        );
                      } else if (refundType === 0) {
                        return (
                          <ProFormList
                            className="reset-ant-form-item"
                            name="refundDetails"
                            max={2}
                            min={1}
                            initialValue={[{}]}
                            actionRef={formListActionRef}
                            creatorButtonProps={false}
                            copyIconProps={false}
                            deleteIconProps={false}
                            actionRender={(field, action, _, count) => [
                              <div key="action" className="ml-4">
                                {count == 1 && (
                                  <PlusOutlined
                                    className="text-lg"
                                    key="add"
                                    onClick={() => action.add()}
                                  />
                                )}
                                {count == 2 && (
                                  <DeleteOutlined
                                    className="text-lg text-primary"
                                    key="remove"
                                    onClick={() => action.remove(field.name)}
                                  />
                                )}
                              </div>,
                            ]}
                          >
                            <ProFormMoney
                              // width={500}
                              name="refundAmount"
                              fieldProps={{
                                addonBefore: (
                                  <ProFormSelect
                                    noStyle
                                    name="accountId"
                                    allowClear={false}
                                    options={accountList}
                                    placeholder={intl.formatMessage({ id: 'sales.returns.operation.selectAccount' })}
                                    normalize={(value, prevValue, prevValues) => {
                                      const res = prevValues?.refundDetails?.find(
                                        (t: ReturnsOrderModifyRefundDetail) =>
                                          t?.accountId == value,
                                      );
                                      if (!isEmpty(res)) {
                                        message.error(intl.formatMessage({ id: 'sales.returns.operation.selectDifferentAccount' }));
                                        return prevValue ?? '';
                                      }
                                      return value;
                                    }}
                                  />
                                ),
                                controls: false,
                                precision: 2,
                                // max: totalAmount,
                                placeholder: intl.formatMessage({ id: 'sales.returns.operation.inputAmount' }),
                              }}
                            />
                          </ProFormList>
                        );
                      }
                    }}
                  </ProFormDependency>
                </Col>
                <Col span={16}>
                  <ProFormTextArea
                    label={intl.formatMessage({ id: 'sales.returns.operation.remark' })}
                    name="remark"
                    fieldProps={{
                      maxLength: 200,
                      onBlur: async (e) => {
                        setLoading(true);
                        const result = await modifyOrder({
                          orderId,
                          orderNo,
                          remark: e.target.value,
                        });
                        if (result) {
                          setLoading(false);
                        }
                        setTimeout(() => {
                          setLoading(false);
                        }, 2000);
                      },
                    }}
                    rules={[{ message: intl.formatMessage({ id: 'sales.returns.operation.maxCharacters' }), max: 200 }]}
                  />
                </Col>
              </Row>
            </ProForm>
          </ProCard>
          <ProCard className="mt-[1px]">
            <Flex justify="space-between">
              <Flex key="summary" gap={80} justify="flex-start" align="center">
                <span className="text-[16px] font-semibold text-[#000000D9]">
                  {intl.formatMessage({ id: 'sales.returns.operation.totalQuantity' })}：{defaultTo(totalCount, '0')}
                </span>
                <span className="flex flex-row items-center">
                  <span className="text-[16px] font-semibold text-[#000000D9]">{intl.formatMessage({ id: 'sales.returns.operation.totalRefundAmount' })}：</span>
                  <span className="text-[24px] font-medium text-[#F83431]">
                    ￥{defaultTo(totalAmount, '0')}
                  </span>
                </span>
              </Flex>
              <Space>
                <Checkbox.Group
                  disabled={dataSourceCache?.length <= 0}
                  options={plainOptions}
                  value={checkedList}
                  onChange={onCheckboxChange}
                />
                <AuthButton
                  disabled={dataSourceCache?.length <= 0}
                  authority="salesSubmit"
                  type="primary"
                  onClick={onSubmit}
                >
                  {intl.formatMessage({ id: 'sales.returns.operation.submit' })}
                </AuthButton>
              </Space>
            </Flex>
          </ProCard>
        </Spin>
        <ConfirmModal {...confirmModalProps} />
      </ConfigProvider>
    </PageContainer>
  );
};

export default withKeepAlive(ReturnsOperation);
