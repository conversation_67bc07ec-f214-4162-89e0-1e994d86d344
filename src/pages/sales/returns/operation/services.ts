import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { OrderPageParamsType } from './types/PageParamsType';
import type { AfterSaleOrderRo } from './types/ReturnsAfterSaleDetailEntity';
import type {
  ReturnsGoodsAddItem,
  ReturnsGoodsCreateItemType,
  ReturnsGoodsModifyItem,
  ReturnsOrderModifyType,
} from './types/ReturnsGoodsCreateItemType';
import type { ReturnsOrderEntity } from './types/ReturnsOrderEntity';
import type { ReturnsOrderType } from './types/ReturnsOrderType';

/**
 * 售后列表
 * （售后单维度:orders,商品维度:goods）
 * @param params
 * @returns
 */
export const getRefundablePaged = async (params: PageRequestParamsType & OrderPageParamsType) => {
  return request<PageResponseDataType<ReturnsOrderEntity>>(
    '/ipmsaftersale/AfterSaleQryFacade/getRefundablePaged',
    {
      data: params,
    },
  );
};
/**
 * 新建销售单退货记录
 * @param params
 * @returns
 */
export const createOrder = async (params: ReturnsGoodsCreateItemType) => {
  return request<ReturnsOrderType>('/ipmsaftersale/AfterSaleCmdFacade/createOrder', {
    data: params,
  });
};
/**
 * 新建销售单退货记录
 * @param params
 * @returns
 */
export const modifyOrder = async (params: ReturnsOrderModifyType) => {
  return request<ReturnsOrderType>('/ipmsaftersale/AfterSaleCmdFacade/modifyOrder', {
    data: params,
  });
};
/**
 * 撤回
 * @param params
 * @returns
 */
export const drawOrder = async (params: ReturnsOrderType) => {
  return request<boolean>('/ipmsaftersale/AfterSaleCmdFacade/drawOrder', {
    data: params,
  });
};
/**
 * 作废
 * @param params
 * @returns
 */
export const cancelOrder = async (params: ReturnsOrderType) => {
  return request<boolean>('/ipmsaftersale/AfterSaleCmdFacade/cancelOrder', {
    data: params,
  });
};
/**
 * 提交
 * @param params
 * @returns
 */
export const submitOrder = async (params: ReturnsOrderType) => {
  return request<boolean>('/ipmsaftersale/AfterSaleCmdFacade/submitOrder', {
    data: params,
  });
};
/**
 * 一键入库
 * @param params
 * @returns
 */
export const directIn = async (params: ReturnsOrderType) => {
  return request<boolean>('/ipmsaftersale/AfterSaleCmdFacade/directIn', {
    data: params,
  });
};
/**
 * 确认退款/确认结算
 * @param params
 * @returns
 */
export const confirmRefund = async (params: ReturnsOrderType) => {
  return request<boolean>('/ipmsaftersale/AfterSaleCmdFacade/confirmRefund', {
    data: params,
  });
};
/**
 * 售后确认
 * @param params
 * @returns
 */
export const confirmOrder = async (params: ReturnsOrderType) => {
  return request<boolean>('/ipmsaftersale/AfterSaleCmdFacade/confirmOrder', {
    data: params,
  });
};
/**
 * 向销售单退货记录添加商品
 * @param params
 * @returns
 */
export const addItem = async (params: ReturnsGoodsAddItem) => {
  return request<boolean>('/ipmsaftersale/AfterSaleCmdFacade/addItem', {
    data: params,
  });
};
/**
 * 修改售后单商品数量或价格
 * @param params ReturnsGoodsModifyItem
 * @returns
 */
export const modifyItem = async (params: ReturnsGoodsModifyItem) => {
  return request<boolean>('/ipmsaftersale/AfterSaleCmdFacade/modifyItem', {
    data: params,
  });
};
/**
 * 删除退货商品
 * @param params ReturnsGoodsAddItem
 * @returns
 */
export const deleteItem = async (params: { ids: string[]; orderId: string; orderNo: string }) => {
  return request<boolean>('/ipmsaftersale/AfterSaleCmdFacade/deleteItem', {
    data: params,
  });
};
/**
 * 售后详情
 * @param params
 * @returns
 */
export const getAfterSaleDetail = async (params: ReturnsOrderType) => {
  return request<AfterSaleOrderRo>('/ipmsaftersale/AfterSaleQryFacade/getAfterSaleDetail', {
    data: params,
  });
};
