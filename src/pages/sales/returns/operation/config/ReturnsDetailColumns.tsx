import ColumnRender from '@/components/ColumnRender';
import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { AfterSaleOrderGoodsRo } from '../types/ReturnsAfterSaleDetailEntity';

export default () => {
  const intl = useIntl();

  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      fixed: 'left',
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.productCode' }),
      dataIndex: 'itemSn',
      width: 100,
      search: false,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.productName' }),
      dataIndex: 'itemName',
      width: 100,
      search: false,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.oe' }),
      dataIndex: 'oeNos',
      width: 140,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.brandPartNo' }),
      dataIndex: 'brandPartNos',
      width: 100,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.brand' }),
      dataIndex: 'brandName',
      search: false,
      width: 80,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.salesOrderNo' }),
      dataIndex: 'orgOrderNo',
      search: false,
      width: 160,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.salesStore' }),
      dataIndex: 'storeName',
      search: false,
      width: 120,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.refundAmount' }),
      dataIndex: 'unitAmount',
      key: 'unitAmount',
      search: false,
      width: 100,
      fixed: 'right',
      valueType: 'digit',
      editable: true,
      fieldProps() {
        return { precision: 2 };
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.operation.returnQuantity' }),
      dataIndex: 'refundNum',
      key: 'refundNum',
      search: false,
      width: 100,
      valueType: 'digit',
      fixed: 'right',
      editable: true,
      fieldProps() {
        return { precision: 0 };
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.detail.returnReason' }),
      dataIndex: 'cause',
      key: 'cause',
      search: false,
      fixed: 'right',
      width: 140,
    },
    {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      valueType: 'option',
      fixed: 'right',
      align: 'center',
      width: 60,
    },
  ] as ProColumns<AfterSaleOrderGoodsRo>[];
};
