import ConfirmModal from '@/components/ConfirmModal';
import LeftTitle from '@/components/LeftTitle';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { KeepAliveTabContext } from '@/layouts/context';
import CustomerDetailDrawerForm from '@/pages/customer/list/components/CustomerDetailDrawerForm';
import type { CustomerDetailDrawerFormType } from '@/pages/customer/list/types/CustomerDetailDrawerFormType';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { RightOutlined } from '@ant-design/icons';
import { PageContainer, ProCard, ProDescriptions } from '@ant-design/pro-components';
import { history, useIntl, useLocation } from '@umijs/max';
import type { GetProps } from 'antd';
import { Flex, Space } from 'antd';
import classNames from 'classnames';
import { defaultTo, includes, isEmpty, sum } from 'lodash';
import qs from 'qs';
import { useContext, useEffect, useState } from 'react';
import SettleWayConfirmModal from '../list/components/SettleWayConfirmModal';
import GoodsDetailColumns from '../list/config/GoodsDetailColumns';
import OperatorColumns from '../list/config/OperatorColumns';
import SettleColumns from '../list/config/SettleColumns';
import type { ReturnsDetailBaseInfoType } from '../list/types/ReturnsDetailBaseInfoType';
import type { SettleConfirmModalType } from '../list/types/SettleConfirmModalType';
import {
  cancelOrder,
  confirmRefund,
  directIn,
  drawOrder,
  getAfterSaleDetail,
} from '../operation/services';
import type {
  AfterSaleOrderGoodsRo,
  AfterSaleOrderTimeRo,
  AfterSaleRefundDetailRo,
} from '../operation/types/ReturnsAfterSaleDetailEntity';
import type { ReturnsOrderType } from '../operation/types/ReturnsOrderType';

export default () => {
  const intl = useIntl();
  const location = useLocation();
  const state = qs.parse(location.search.substring(1)) as ReturnsOrderType;

  // 商品总数
  const [totalCount, setTotalCount] = useState<number>();
  // 退款总金额
  const [totalAmount, setTotalAmount] = useState<number>();

  const [cstInfo, setCstInfo] = useState<ReturnsDetailBaseInfoType>();

  // 退货明细
  const [dataSourceCache, setDataSourceCache] = useState<AfterSaleOrderGoodsRo[]>([]);
  // 操作记录
  const [operatorDataSource, setOperatorDataSource] = useState<AfterSaleOrderTimeRo[]>([]);
  // 结算记录
  const [settleDataSource, setSettleDataSource] = useState<AfterSaleRefundDetailRo[]>([]);

  // 客户详情
  const [detailModalProps, setDetailModalProps] = useState<CustomerDetailDrawerFormType>({
    visible: false,
    recordId: '',
    onCancel: () => {
      setDetailModalProps((preModalProps) => ({
        ...preModalProps,
        visible: false,
        recordId: '',
      }));
    },
    title: intl.formatMessage({ id: 'sales.returns.detail.customerDetail' }),
  });

  const { closeTab } = useContext(KeepAliveTabContext);

  /** 查询退货详情*/
  const getDetail = async () => {
    console.log('state', state);
    if (state?.orderId && state?.orderNo) {
      const baseInfo: ReturnsDetailBaseInfoType = {};
      const result = await getAfterSaleDetail({ orderId: state.orderId, orderNo: state.orderNo });

      if (result?.main) {
        const {
          orderAmount,
          cstName,
          orderCreateTime,
          storeName,
          salesmanName,
          backWarehouseName,
          remark,
          cstId,
          storeId,
        } = result.main;
        setTotalAmount(orderAmount);
        baseInfo.cstName = cstName;
        baseInfo.orderAmount = orderAmount;
        baseInfo.backWarehouseName = backWarehouseName;
        baseInfo.storeName = storeName;
        baseInfo.salesmanName = salesmanName;
        baseInfo.orderCreateTime = orderCreateTime;
        baseInfo.remark = remark;
        baseInfo.cstId = cstId;
        baseInfo.storeId = storeId;
      }
      if (result?.refunds) {
        baseInfo.refundTypeName = result.refunds[0].refundTypeName;
        baseInfo.refundType = result.refunds[0].refundType;
      }
      if (result?.goods) {
        setTotalCount(sum(result?.goods.map((t) => t.refundNum)) ?? 0);
        setDataSourceCache(
          result?.goods.map((t) => ({ ...t, storeName: result.main.storeName })) ?? [],
        );
      }
      if (result?.status) {
        baseInfo.orderStatusName = result.status.orderStatusName;
        baseInfo.refundStatusName = result.status.refundStatusName;
        baseInfo.orderStatus = result.status.orderStatus;
        baseInfo.refundStatus = result.status.refundStatus;
      }
      if (result?.refundDetails) {
        if (baseInfo?.refundStatus == 100) {
          setSettleDataSource(result.refundDetails);
        }
      }
      if (result?.times) {
        setOperatorDataSource(result.times);
      }
      console.log(baseInfo);

      setCstInfo(baseInfo);
    }
  };
  //   查询详情
  useEffect(() => {
    getDetail();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.search]);

  /**
   * 作废
   */
  const [confirmModalProps, setConfirmModalProps] = useState<GetProps<typeof ConfirmModal>>({
    open: false,
  });
  /**
   * 确认结算
   */
  const [confirmSettleModalProps, setConfirmSettleModalProps] = useState<SettleConfirmModalType>({
    visible: false,
    orderAmount: 0,
  });
  /**
   * 关闭确认框
   */
  const onConfirmCancel = async () => {
    setConfirmModalProps((preProps) => ({
      ...preProps,
      open: false,
    }));
  };
  /**
   * 确认【作废】
   */
  const onCancelOrder = async () => {
    if (isEmpty(state)) return;
    const result = await cancelOrder(state);
    if (result) {
      getDetail();
      onConfirmCancel();
    }
  };
  /**
   * 确认【撤回】
   */
  const onDrawOrder = async () => {
    if (isEmpty(state)) return;
    const result = await drawOrder(state);
    if (result) {
      onConfirmCancel();
      getDetail();
    }
  };
  /**
   * 确认【一键入库】
   */
  const onDirectIn = async () => {
    if (isEmpty(state)) return;
    const result = await directIn(state);
    if (result) {
      getDetail();
      onConfirmCancel();
    }
  };
  /**
   * 关闭【确认结算】
   */
  const onSettleCancel = async () => {
    setConfirmSettleModalProps((preProps) => ({
      ...preProps,
      orderId: '',
      orderNo: '',
      cstId: '',
      storeId: '',
      orderAmount: 0,
      visible: false,
    }));
  };
  /**
   * 确认【确认结算】
   */
  const onConfirmPay = async () => {
    if (isEmpty(state)) return;
    const result = await confirmRefund(state);
    if (result) {
      getDetail();
      onSettleCancel();
    }
  };

  /**
   * 查看详情
   * @param id
   */
  const openDetailModal = (id: string) => {
    setDetailModalProps((preModalProps) => ({
      ...preModalProps,
      visible: true,
      recordId: id,
    }));
  };

  // 单据状态
  const orderStatusClassname = classNames('rounded-sm px-[10px] py-1 text-[13px]', {
    // 绿色
    'bg-[#EAF9ECFF] text-[#33CC47FF]': [100, 20].includes(cstInfo?.orderStatus!),
    // 灰色
    'bg-[#00000008] text-[#000000D9]': cstInfo?.orderStatus == 90,
    'bg-[#FFEDED] text-[#F83331]': ![20, 90, 100].includes(cstInfo?.orderStatus!),
  });
  // 结算状态
  const refundStatusClassname = classNames('rounded-sm px-[10px] py-1 text-[13px]', {
    // 绿色
    'bg-[#EAF9ECFF] text-[#33CC47FF]': cstInfo?.refundStatus == 100,
    'bg-[#FFEDED] text-[#F83331]': cstInfo?.refundStatus !== 100,
  });
  // 草稿或者已作废只展示单据状态不展示结算状态
  const hiddenRefundStatus = !includes([0, 90], cstInfo?.orderStatus);
  return (
    <PageContainer>
      <ProCard className="mb-4">
        <Flex justify="space-between">
          <Space>
            <span className="text-[20px] font-semibold text-[#000000D9]">{state?.orderNo}</span>
            {/* 单据状态：已完成绿色 已作废灰色 其余状态红色 */}
            <span className={orderStatusClassname}>{cstInfo?.orderStatusName}</span>
            {/* 结算状态：已结算绿色未结算红色 */}
            {hiddenRefundStatus && (
              <span className={refundStatusClassname}>{cstInfo?.refundStatusName}</span>
            )}
          </Space>
          <Space>
            <AuthButton
              authority="editSaleReturn"
              danger
              visible={cstInfo?.orderStatus == 0}
              onClick={() => {
                closeTab();
                history.push(`/sales/returns/operation`, { ...state, name: intl.formatMessage({ id: 'sales.returns.detail.editPageName' }) });
              }}
            >
              {intl.formatMessage({ id: 'sales.returns.detail.edit' })}
            </AuthButton>
            <AuthButton
              authority="withdrawSaleReturn"
              danger
              visible={cstInfo?.orderStatus == 10}
              onClick={() =>
                setConfirmModalProps((preProps) => ({
                  ...preProps,
                  open: true,
                  okText: intl.formatMessage({ id: 'sales.returns.detail.withdraw' }),
                  tips: intl.formatMessage({ id: 'sales.returns.detail.confirmWithdraw' }),
                  onOk: onDrawOrder,
                }))
              }
            >
              {intl.formatMessage({ id: 'sales.returns.detail.withdraw' })}
            </AuthButton>
            <AuthButton
              authority="deleteSaleReturn"
              danger
              visible={(cstInfo?.orderStatus ?? 0) <= 10}
              onClick={() =>
                setConfirmModalProps((preProps) => ({
                  ...preProps,
                  open: true,
                  okText: intl.formatMessage({ id: 'sales.returns.detail.void' }),
                  tips: intl.formatMessage({ id: 'sales.returns.detail.confirmVoid' }),
                  onOk: onCancelOrder,
                }))
              }
            >
              {intl.formatMessage({ id: 'sales.returns.detail.void' })}
            </AuthButton>
            <AuthButton
              authority="saleReturnPrint"
              danger
              onClick={() =>
                window.open(
                  `/print?orderNo=${state.orderNo}&orderId=${state.orderId}&printType=${PrintType.salesReturnOrder}`,
                )
              }
            >
              {intl.formatMessage({ id: 'sales.returns.detail.print' })}
            </AuthButton>
            <AuthButton
              authority="saleReturnInWareHouse"
              type="primary"
              visible={cstInfo?.orderStatus == 10}
              onClick={() =>
                setConfirmModalProps((preProps) => ({
                  ...preProps,
                  open: true,
                  okText: intl.formatMessage({ id: 'sales.returns.detail.oneClickInbound' }),
                  tips: intl.formatMessage({ id: 'sales.returns.detail.confirmOneClickInbound' }),
                  onOk: onDirectIn,
                }))
              }
            >
              {intl.formatMessage({ id: 'sales.returns.detail.oneClickInbound' })}
            </AuthButton>
            <AuthButton
              authority="orderReturnSettlement"
              type="primary"
              visible={
                cstInfo?.refundStatus == 0 &&
                (cstInfo.orderStatus == 10 || cstInfo.orderStatus == 20)
              }
              onClick={() => {
                setConfirmSettleModalProps((preProps) => ({
                  ...preProps,
                  ...state,
                  refundType: cstInfo?.refundType,
                  cstId: cstInfo?.cstId,
                  storeId: cstInfo?.storeId,
                  orderAmount: cstInfo?.orderAmount ?? 0,
                  visible: true,
                }));
              }}
            >
              {intl.formatMessage({ id: 'sales.returns.detail.confirmSettlement' })}
            </AuthButton>
          </Space>
        </Flex>

        <ProDescriptions<ReturnsDetailBaseInfoType>
          className="mt-5"
          column={4}
          dataSource={cstInfo}
          columns={[
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.customerName' }),
              dataIndex: 'cstName',
              renderText: (text) => (
                <span className="flex">
                  {text}
                  <RightOutlined
                    className="cursor-pointer"
                    title={intl.formatMessage({ id: 'sales.returns.detail.viewCustomerDetail' })}
                    onClick={() => {
                      const cstId = cstInfo?.cstId;
                      if (cstId) {
                        openDetailModal(cstId);
                      }
                    }}
                  />
                </span>
              ),
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.orderStatus' }),
              dataIndex: 'orderStatusName',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.refundAmount' }),
              dataIndex: 'orderAmount',
              valueType: 'money',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.settlementMethod' }),
              dataIndex: 'refundTypeName',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.returnStore' }),
              dataIndex: 'storeName',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.receiveWarehouse' }),
              dataIndex: 'backWarehouseName',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.orderTime' }),
              dataIndex: 'orderCreateTime',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.creator' }),
              dataIndex: 'salesmanName',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.remark' }),
              dataIndex: 'remark',
            },
          ]}
        />
      </ProCard>
      <FunProTable<AfterSaleOrderGoodsRo>
        headerTitle={<LeftTitle title={intl.formatMessage({ id: 'sales.returns.detail.goodsDetail' })} />}
        search={false}
        pagination={false}
        scroll={{ x: 800 }}
        dataSource={dataSourceCache}
        options={false}
        columns={GoodsDetailColumns()}
      />
      <ProCard bordered>
        <Flex gap={40} key="summary" className="flex flex-row justify-end items-center">
          <span className="text-[16px] font-semibold text-[#000000D9]">
            {intl.formatMessage({ id: 'sales.returns.detail.totalQuantity' })}：{defaultTo(totalCount, '0')}
          </span>
          <span className="flex flex-row items-center">
            <span className="text-[16px] font-semibold text-[#000000D9]">{intl.formatMessage({ id: 'sales.returns.detail.totalReturnAmount' })}：</span>
            <span className="text-[24px] font-medium text-[#F83431]">
              ￥{defaultTo(totalAmount, 0).toFixed(2)}
            </span>
          </span>
        </Flex>
      </ProCard>
      <FunProTable<AfterSaleRefundDetailRo, any>
        headerTitle={<LeftTitle title={intl.formatMessage({ id: 'sales.returns.detail.settlementRecord' })} />}
        search={false}
        pagination={false}
        className="mt-4"
        scroll={{ x: 'max-content' }}
        dataSource={settleDataSource}
        options={false}
        columns={SettleColumns()}
      />
      <FunProTable<AfterSaleOrderTimeRo, any>
        headerTitle={<LeftTitle title={intl.formatMessage({ id: 'sales.returns.detail.operationRecord' })} />}
        search={false}
        pagination={false}
        className="mt-4"
        scroll={{ x: 'max-content' }}
        dataSource={operatorDataSource}
        options={false}
        columns={OperatorColumns()}
      />
      <SettleWayConfirmModal
        {...confirmSettleModalProps}
        onCancel={onSettleCancel}
        onOk={onConfirmPay}
      />
      <ConfirmModal {...confirmModalProps} onCancel={onConfirmCancel} />
      <CustomerDetailDrawerForm {...detailModalProps} />
    </PageContainer>
  );
};
