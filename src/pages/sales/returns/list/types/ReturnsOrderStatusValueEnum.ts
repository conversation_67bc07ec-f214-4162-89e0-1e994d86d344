export enum ReturnsOrderStatus {
  DRAFT = 0,
  TO_IN = 10,
  HAS_IN = 20,
  CANCEL = 90,
  FINISH = 100,
}

export enum ReturnsOrderStatusName {
  DRAFT = '草稿',
  TO_IN = '待入库',
  HAS_IN = '已入库',
  CANCEL = '已作废',
  FINISH = '已完成',
}
export const ReturnsOrderStatusValueEnum = {
  [ReturnsOrderStatus.DRAFT]: { text: ReturnsOrderStatusName.DRAFT, status: 'Default' },
  [ReturnsOrderStatus.TO_IN]: { text: ReturnsOrderStatusName.TO_IN, status: 'Default' },
  [ReturnsOrderStatus.HAS_IN]: { text: ReturnsOrderStatusName.HAS_IN, status: 'Default' },
  [ReturnsOrderStatus.CANCEL]: { text: ReturnsOrderStatusName.CANCEL, status: 'Error' },
  [ReturnsOrderStatus.FINISH]: { text: ReturnsOrderStatusName.FINISH, status: 'Success' },
};
