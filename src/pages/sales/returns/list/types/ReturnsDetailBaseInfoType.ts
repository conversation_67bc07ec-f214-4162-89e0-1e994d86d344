export interface ReturnsDetailBaseInfoType {
  // 结算状态
  refundStatus?: number;
  // 单据状态
  orderStatus?: number;
  // 客户名称
  cstName?: string;
  // 单据状态
  orderStatusName?: string;
  // 退款状态
  refundStatusName?: string;
  //  结算方式名称
  refundTypeName?: string;
  //  结算方式
  refundType?: number;
  //  退货门店
  storeName?: string;
  //  收货仓库
  backWarehouseName?: string;
  //  下单时间
  orderCreateTime?: string;
  //  制单人
  salesmanName?: string;
  //  备注
  remark?: string;
  //  退款金额
  orderAmount?: number;

  cstId?: string;
  orderId?: string;
  storeId?: string;
}
