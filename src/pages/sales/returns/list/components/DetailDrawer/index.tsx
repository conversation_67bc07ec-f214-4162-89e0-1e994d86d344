import ConfirmModal from '@/components/ConfirmModal';
import LeftTitle from '@/components/LeftTitle';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import type { CommonModelForm } from '@/types/CommonModelForm';
import { DrawerForm, ProCard, ProDescriptions } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import type { GetProps } from 'antd';
import { Flex, Space } from 'antd';
import { defaultTo, sum } from 'lodash';
import { useState } from 'react';
import {
  cancelOrder,
  confirmRefund,
  directIn,
  drawOrder,
  getAfterSaleDetail,
} from '../../../operation/services';
import type {
  AfterSaleOrderGoodsRo,
  AfterSaleOrderTimeRo,
  AfterSaleRefundDetailRo,
} from '../../../operation/types/ReturnsAfterSaleDetailEntity';
import type { ReturnsOrderType } from '../../../operation/types/ReturnsOrderType';
import GoodsDetailColumns from '../../config/GoodsDetailColumns';
import OperatorColumns from '../../config/OperatorColumns';
import SettleColumns from '../../config/SettleColumns';
import type { ReturnsDetailBaseInfoType } from '../../types/ReturnsDetailBaseInfoType';
import type { SettleConfirmModalType } from '../../types/SettleConfirmModalType';
import SettleWayConfirmModal from '../SettleWayConfirmModal';
export default (props: CommonModelForm<ReturnsOrderType, any>) => {
  const intl = useIntl();
  // 商品总数
  const [totalCount, setTotalCount] = useState<number>();
  // 退款总金额
  const [totalAmount, setTotalAmount] = useState<number>();

  const [cstInfo, setCstInfo] = useState<ReturnsDetailBaseInfoType>();

  // 退货明细
  const [dataSourceCache, setDataSourceCache] = useState<AfterSaleOrderGoodsRo[]>([]);
  // 操作记录
  const [operatorDataSource, setOperatorDataSource] = useState<AfterSaleOrderTimeRo[]>([]);
  // 结算记录
  const [settleDataSource, setSettleDataSource] = useState<AfterSaleRefundDetailRo[]>([]);
  /** 查询退货详情*/
  const getDetail = async (params: ReturnsOrderType) => {
    if (params) {
      const baseInfo: ReturnsDetailBaseInfoType = {};
      const result = await getAfterSaleDetail(params);

      if (result?.main) {
        const {
          orderAmount,
          cstName,
          orderCreateTime,
          storeName,
          salesmanName,
          backWarehouseName,
          remark,
          cstId,
          storeId,
        } = result.main;
        setTotalAmount(orderAmount);
        baseInfo.cstName = cstName;
        baseInfo.orderAmount = orderAmount;
        baseInfo.backWarehouseName = backWarehouseName;
        baseInfo.storeName = storeName;
        baseInfo.salesmanName = salesmanName;
        baseInfo.orderCreateTime = orderCreateTime;
        baseInfo.remark = remark;
        baseInfo.cstId = cstId;
        baseInfo.storeId = storeId;
      }
      if (result?.refunds) {
        baseInfo.refundTypeName = result.refunds[0].refundTypeName;
      }
      if (result?.goods) {
        setTotalCount(sum(result?.goods.map((t) => t.refundNum)) ?? 0);
        setDataSourceCache(
          result?.goods.map((t) => ({ ...t, storeName: result.main.storeName })) ?? [],
        );
      }
      if (result?.status) {
        baseInfo.orderStatusName = result.status.orderStatusName;
        baseInfo.refundStatusName = result.status.refundStatusName;
        baseInfo.orderStatus = result.status.orderStatus;
        baseInfo.refundStatus = result.status.refundStatus;
      }
      if (result?.refundDetails) {
        if (baseInfo?.refundStatus == 100) {
          setSettleDataSource(result.refundDetails);
        }
      }
      if (result?.times) {
        setOperatorDataSource(result.times);
      }

      setCstInfo(baseInfo);
    }
  };
  useAsyncEffect(async () => {
    if (props.visible && props?.recordId) {
      getDetail(props.recordId);
    }
  }, [props.visible]);

  /**
   * 作废
   */
  const [confirmModalProps, setConfirmModalProps] = useState<GetProps<typeof ConfirmModal>>({
    open: false,
  });
  /**
   * 确认结算
   */
  const [confirmSettleModalProps, setConfirmSettleModalProps] = useState<SettleConfirmModalType>({
    visible: false,
    orderAmount: 0,
  });
  /**
   * 关闭确认框
   */
  const onConfirmCancel = async () => {
    setConfirmModalProps((preProps) => ({
      ...preProps,
      open: false,
    }));
  };
  /**
   * 确认【作废】
   */
  const onCancelOrder = async () => {
    if (!props?.recordId) return;
    const result = await cancelOrder(props.recordId);
    if (result) {
      onConfirmCancel();
      props?.onCancel?.();
    }
  };
  /**
   * 确认【撤回】
   */
  const onDrawOrder = async () => {
    if (!props?.recordId) return;
    const result = await drawOrder(props.recordId);
    if (result) {
      onConfirmCancel();
      props?.onCancel?.();
    }
  };
  /**
   * 确认【一键入库】
   */
  const onDirectIn = async () => {
    if (!props?.recordId) return;
    const result = await directIn(props.recordId);
    if (result) {
      onConfirmCancel();
      props?.onCancel?.();
    }
  };
  /**
   * 关闭【确认结算】
   */
  const onSettleCancel = async () => {
    setConfirmSettleModalProps((preProps) => ({
      ...preProps,
      orderId: '',
      orderNo: '',
      cstId: '',
      storeId: '',
      orderAmount: 0,
      visible: false,
    }));
  };
  /**
   * 确认【确认结算】
   */
  const onConfirmPay = async () => {
    if (!props?.recordId) return;
    const result = await confirmRefund(props.recordId);
    if (result) {
      onSettleCancel();
      props?.onCancel?.();
    }
  };

  return (
    <DrawerForm
      title={props.title}
      width={1080}
      drawerProps={{
        classNames: {
          body: 'bg-[#f2f2f2]',
        },
        maskClosable: false,
        onClose: props.onCancel,
      }}
      open={props.visible}
      submitter={{
        submitButtonProps: { style: { display: 'none' } },
      }}
    >
      <ProCard className="mb-4">
        <Flex justify="space-between">
          <Space>
            <span className="text-[20px] font-semibold text-[#000000D9]">
              {props.recordId?.orderNo}
            </span>
            <span className="bg-[#FFEDED] text-[#F83331] rounded-sm px-[10px] py-1 text-[13px]">
              {cstInfo?.orderStatusName}
            </span>
            <span className="bg-[#EAF9ECFF] text-[#33CC47FF] rounded-sm px-[10px] py-1 text-[13px]">
              {cstInfo?.refundStatusName}
            </span>
          </Space>
          <Space>
            <AuthButton
              authority="salesReturnsUpdate"
              danger
              visible={cstInfo?.orderStatus == 0}
              onClick={() => {
                props?.onCancel?.();
                history.push(`/sales/returns/operation`, {
                  orderId: props?.recordId?.orderId,
                  orderNo: props?.recordId?.orderNo,
                  name: intl.formatMessage({ id: 'sales.returns.detail.editPageName' }),
                });
              }}
            >
              {intl.formatMessage({ id: 'sales.returns.detail.edit' })}
            </AuthButton>
            <AuthButton
              authority="salesReturnsDrawOrder"
              danger
              visible={cstInfo?.orderStatus == 10}
              onClick={() =>
                setConfirmModalProps((preProps) => ({
                  ...preProps,
                  open: true,
                  okText: intl.formatMessage({ id: 'sales.returns.detail.withdraw' }),
                  tips: intl.formatMessage({ id: 'sales.returns.detail.confirmWithdraw' }),
                  onOk: onDrawOrder,
                }))
              }
            >
              {intl.formatMessage({ id: 'sales.returns.detail.withdraw' })}
            </AuthButton>
            <AuthButton
              authority="salesReturnsCancel"
              danger
              visible={(cstInfo?.orderStatus ?? 0) <= 10}
              onClick={() =>
                setConfirmModalProps((preProps) => ({
                  ...preProps,
                  open: true,
                  okText: intl.formatMessage({ id: 'sales.returns.detail.void' }),
                  tips: intl.formatMessage({ id: 'sales.returns.detail.confirmVoid' }),
                  onOk: onCancelOrder,
                }))
              }
            >
              {intl.formatMessage({ id: 'sales.returns.detail.void' })}
            </AuthButton>
            <AuthButton authority="salesReturnsPrint" danger>
              {intl.formatMessage({ id: 'sales.returns.detail.print' })}
            </AuthButton>
            <AuthButton
              authority="salesReturnsInAll"
              type="primary"
              visible={cstInfo?.orderStatus == 10}
              onClick={() =>
                setConfirmModalProps((preProps) => ({
                  ...preProps,
                  open: true,
                  okText: intl.formatMessage({ id: 'sales.returns.detail.oneClickInbound' }),
                  tips: intl.formatMessage({ id: 'sales.returns.detail.confirmOneClickInbound' }),
                  onOk: onDirectIn,
                }))
              }
            >
              {intl.formatMessage({ id: 'sales.returns.detail.oneClickInbound' })}
            </AuthButton>
            <AuthButton
              authority="salesReturnsSettle"
              type="primary"
              visible={
                cstInfo?.refundStatus == 0 &&
                (cstInfo.orderStatus == 10 || cstInfo.orderStatus == 20)
              }
              onClick={() => {
                setConfirmSettleModalProps((preProps) => ({
                  ...preProps,
                  orderId: props?.recordId?.orderId,
                  orderNo: props?.recordId?.orderNo,
                  cstId: cstInfo?.cstId,
                  storeId: cstInfo?.storeId,
                  orderAmount: cstInfo?.orderAmount ?? 0,
                  visible: true,
                }));
              }}
            >
              {intl.formatMessage({ id: 'sales.returns.detail.confirmSettlement' })}
            </AuthButton>
          </Space>
        </Flex>

        <ProDescriptions<ReturnsDetailBaseInfoType>
          className="mt-5"
          column={4}
          dataSource={cstInfo}
          columns={[
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.customerName' }),
              dataIndex: 'cstName',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.orderStatus' }),
              dataIndex: 'orderStatusName',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.refundAmount' }),
              dataIndex: 'orderAmount',
              valueType: 'money',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.settlementMethod' }),
              dataIndex: 'refundTypeName',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.returnStore' }),
              dataIndex: 'storeName',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.receiveWarehouse' }),
              dataIndex: 'backWarehouseName',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.orderTime' }),
              dataIndex: 'orderCreateTime',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.creator' }),
              dataIndex: 'salesmanName',
            },
            {
              title: intl.formatMessage({ id: 'sales.returns.detail.remark' }),
              dataIndex: 'remark',
            },
          ]}
        />
      </ProCard>
      <FunProTable<AfterSaleOrderGoodsRo>
        headerTitle={<LeftTitle title={intl.formatMessage({ id: 'sales.returns.detail.goodsDetail' })} />}
        search={false}
        pagination={false}
        scroll={{ x: 'max-content' }}
        dataSource={dataSourceCache}
        options={false}
        columns={GoodsDetailColumns()}
      />
      <ProCard bordered>
        <Flex gap={40} key="summary" className="flex flex-row justify-end items-center">
          <span className="text-[16px] font-semibold text-[#000000D9]">
            {intl.formatMessage({ id: 'sales.returns.detail.totalQuantity' })}：{defaultTo(totalCount, '0')}
          </span>
          <span className="flex flex-row items-center">
            <span className="text-[16px] font-semibold text-[#000000D9]">{intl.formatMessage({ id: 'sales.returns.detail.totalReturnAmount' })}：</span>
            <span className="text-[24px] font-medium text-[#F83431]">
              ￥{defaultTo(totalAmount, 0).toFixed(2)}
            </span>
          </span>
        </Flex>
      </ProCard>
      <FunProTable<AfterSaleRefundDetailRo, any>
        // visible={cstInfo?.refundStatus == 1}
        headerTitle={<LeftTitle title={intl.formatMessage({ id: 'sales.returns.detail.settlementRecord' })} />}
        search={false}
        pagination={false}
        className="mt-4"
        scroll={{ x: 'max-content' }}
        dataSource={settleDataSource}
        options={false}
        columns={SettleColumns()}
      />
      <FunProTable<AfterSaleOrderTimeRo, any>
        headerTitle={<LeftTitle title={intl.formatMessage({ id: 'sales.returns.detail.operationRecord' })} />}
        search={false}
        pagination={false}
        className="mt-4"
        scroll={{ x: 'max-content' }}
        dataSource={operatorDataSource}
        options={false}
        columns={OperatorColumns()}
      />
      <SettleWayConfirmModal
        {...confirmSettleModalProps}
        onCancel={onSettleCancel}
        onOk={onConfirmPay}
      />
      <ConfirmModal {...confirmModalProps} onCancel={onConfirmCancel} />
    </DrawerForm>
  );
};
