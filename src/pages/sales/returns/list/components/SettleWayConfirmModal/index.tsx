import { getCstDetail } from '@/pages/customer/list/services';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { CashRefundTypeValueEnum, RefundTypeValueEnum } from '@/types/CommonStatus';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProForm,
  ProFormDependency,
  ProFormList,
  ProFormMoney,
  ProFormSelect,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Spin, message, type FormInstance } from 'antd';
import type { DefaultOptionType } from 'antd/es/select';
import { defaultTo, isEmpty, isUndefined, set } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { getAfterSaleDetail, modifyOrder } from '../../../operation/services';
import type {
  ReturnsOrderModifyRefund,
  ReturnsOrderModifyRefundDetail,
  ReturnsOrderModifyType,
} from '../../../operation/types/ReturnsGoodsCreateItemType';
import type { ReturnsOrderType } from '../../../operation/types/ReturnsOrderType';
import type { SettleConfirmModalType } from '../../types/SettleConfirmModalType';

export default (props: SettleConfirmModalType) => {
  const intl = useIntl();
  const { refundType, cstId, storeId, orderNo, orderId, orderAmount, visible, onOk, onCancel } =
    props;
  // 可用额度
  const [creditAvailableAmount, setCreditAvailableAmount] = useState<number>();
  // 已用额度
  const [creditUsedAmount, setUsedAmount] = useState<number>();
  // 加载中
  const [loading, setLoading] = useState<boolean>(false);
  const [refundOptions, setRefundOptions] = useState<DefaultOptionType[]>([]);

  const formRef = useRef<FormInstance<ReturnsOrderModifyRefund>>();
  useEffect(() => {
    if (!props.visible) {
      formRef.current?.resetFields();
    }
  }, [props.visible]);
  const [accountList, setAccountList] = useState<DefaultOptionType[]>([]);
  useAsyncEffect(async () => {
    setRefundOptions(refundType == 0 ? CashRefundTypeValueEnum : RefundTypeValueEnum);
    formRef.current?.setFieldValue('refundType', refundType);
    if (cstId) {
      const cstDetail = await getCstDetail({ cstId: cstId });
      setCreditAvailableAmount(cstDetail?.settle?.availableAmount);
      setUsedAmount(cstDetail?.settle?.usedAmount);
    }
    if (refundType == 0 && accountList?.length > 0) {
      const defaultAccountId = accountList[0].value;
      const refundDetails = [{ accountId: defaultAccountId, refundAmount: orderAmount }];
      formRef?.current?.setFieldValue('refundDetails', refundDetails);
    }
  }, [refundType, cstId, accountList]);

  useAsyncEffect(async () => {
    if (storeId) {
      const result = await queryMemberAccountPage({
        belongToStore: [storeId],
      });
      if (result?.data) {
        setAccountList(
          result.data.map((item) => ({
            label: item.memberAccountName,
            value: item.id,
          })),
        );
      }
    }
  }, [storeId]);
  /** 查询退货详情*/
  const getDetail = async (params: ReturnsOrderType) => {
    if (params) {
      const result = await getAfterSaleDetail(params);
      if (result?.refunds) {
        formRef.current?.setFieldValue('refundType', result.refunds[0].refundType);
      }
    } else {
      formRef.current?.resetFields();
    }
  };
  /**
   * 更新退货订单
   */
  const updateOrder = async (params: ReturnsOrderModifyType, reload?: boolean) => {
    setLoading(true);
    const result = await modifyOrder(params);
    if (result) {
      setLoading(false);
      if (reload && orderId && orderNo) {
        getDetail({ orderId, orderNo });
      }
    }
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };

  return (
    <ModalForm<ReturnsOrderModifyRefund>
      title={intl.formatMessage({ id: 'sales.returns.settlement.title' })}
      open={visible}
      width={600}
      layout="horizontal"
      validateTrigger="onBlur"
      modalProps={{
        okText: intl.formatMessage({ id: 'sales.returns.settlement.confirm' }),
        maskClosable: false,
        centered: true,
        onCancel: onCancel,
        classNames: {
          header: 'flex flex-row justify-center',
          footer: 'flex flex-row-reverse justify-center gap-4',
        },
      }}
      formRef={formRef}
      onValuesChange={async (
        changeValues: ReturnsOrderModifyRefund,
        allValues: ReturnsOrderModifyRefund,
      ) => {
        try {
          // 此处只关注 refundDetails
          if (changeValues?.remark || changeValues.refundType) return;
          const bottomValues = await formRef.current?.validateFields?.();
          console.log(changeValues, allValues, bottomValues);
          if (!bottomValues?.refundDetails) return;
          // 如果有非法结算信息则不保存
          const validArray: ReturnsOrderModifyRefundDetail[] = [];
          const inValidArray: ReturnsOrderModifyRefundDetail[] = [];

          const array = bottomValues.refundDetails;
          for (let i = 0; i < array.length; i++) {
            const item = array[i];
            if (
              !isUndefined(item) &&
              isUndefined(item?.accountId) &&
              isUndefined(item?.refundAmount)
            ) {
              continue;
            }
            if (isEmpty(item) || isEmpty(item?.accountId) || (item.refundAmount ?? 0) <= 0) {
              inValidArray.push(item);
            } else {
              validArray.push(item);
            }
          }
          if (!isEmpty(inValidArray) || isEmpty(validArray)) return;
          const reqParams = {
            orderId,
            orderNo,
            refund: { refundType: allValues.refundType, refundDetails: validArray },
          };
          updateOrder(reqParams, true);
        } catch (e) {
          console.log(e);
        }
      }}
      onFinish={async () => {
        onOk?.();
        return true;
      }}
    >
      <ProForm.Item className="text-2xl" label={<span>{intl.formatMessage({ id: 'sales.returns.settlement.settlementAmount' })}</span>}>
        <div className="text-[24px] font-medium text-[#F83431]"> ￥{orderAmount}</div>
      </ProForm.Item>
      <Spin spinning={loading}>
        <ProFormSelect<number>
          label={intl.formatMessage({ id: 'sales.returns.settlement.settlementMethod' })}
          options={refundOptions}
          name="refundType"
          allowClear={false}
          onChange={async (value) => {
            const reqParams: ReturnsOrderModifyType = {
              orderId,
              orderNo,
              refund: { refundType: value },
            };
            // 结算方式修改为挂账时直接更新
            if (value == 0) {
              const defaultAccountId = accountList[0].value;
              const refundDetails = [{ accountId: defaultAccountId, refundAmount: orderAmount }];
              set(reqParams, 'refund.refundDetails', refundDetails);
              formRef?.current?.setFieldValue('refundDetails', refundDetails);
            }
            updateOrder(reqParams, true);
          }}
        />
        <ProFormDependency name={['refundType']}>
          {({ refundType }) => {
            if (refundType === 10) {
              return (
                <div className="text-gray-500 availableAmount">
                  {intl.formatMessage({ id: 'sales.returns.settlement.remainingCredit' })}：
                  <div className="text-gray-500 availableAmount">
                    {intl.formatMessage({ id: 'sales.returns.settlement.used' })}{defaultTo(creditUsedAmount, '-')}/{intl.formatMessage({ id: 'sales.returns.settlement.available' })}
                    {defaultTo(creditAvailableAmount, '-')}
                  </div>
                </div>
              );
            } else if (refundType === 0) {
              return (
                <ProFormList
                  className="ml-20 reset-ant-form-item"
                  name="refundDetails"
                  max={2}
                  min={1}
                  initialValue={[{}]}
                  creatorButtonProps={false}
                  copyIconProps={false}
                  deleteIconProps={false}
                  actionRender={(field, action, _, count) => [
                    <div key="action" className="ml-4">
                      {count == 1 && (
                        <PlusOutlined
                          className="text-lg"
                          key="add"
                          onClick={() => action.add({})}
                        />
                      )}
                      {count == 2 && (
                        <DeleteOutlined
                          className="text-lg text-primary"
                          key="remove"
                          onClick={() => action.remove(field.name)}
                        />
                      )}
                    </div>,
                  ]}
                >
                  <ProFormMoney
                    name="refundAmount"
                    fieldProps={{
                      addonBefore: (
                        <ProFormSelect
                          noStyle
                          name="accountId"
                          allowClear={false}
                          options={accountList}
                          placeholder={intl.formatMessage({ id: 'sales.returns.settlement.selectAccount' })}
                          normalize={(value, prevValue, prevValues) => {
                            const res = prevValues?.refundDetails?.find(
                              (t: ReturnsOrderModifyRefundDetail) => t?.accountId == value,
                            );
                            if (!isEmpty(res)) {
                              message.error(intl.formatMessage({ id: 'sales.returns.settlement.duplicateAccountError' }));
                              return prevValue ?? '';
                            }
                            return value;
                          }}
                        />
                      ),
                      controls: false,
                      precision: 2,
                      // max: totalAmount,
                      placeholder: intl.formatMessage({ id: 'sales.returns.settlement.inputAmount' }),
                    }}
                  />
                </ProFormList>
              );
            }
          }}
        </ProFormDependency>
      </Spin>
    </ModalForm>
  );
};
