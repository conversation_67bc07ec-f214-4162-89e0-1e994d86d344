import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import type { ReturnsTableEntity } from '../operation/types/ReturnsTableEntity';
import { type CustomerPriceLevelEntity } from './types/CustomerPriceLevel.entity';
import { type CustomerTagEntity } from './types/CustomerTag.entity';
export type CustomerPropertyTableType = CustomerTagEntity & CustomerPriceLevelEntity;

/**
 * 客户属性分页查询
 *
 * @param params PageRequestParamsType<Partial<GoodsCategoryEntity>>
 * @returns
 */
export const getAfterSaleOrderPaged = async (
  params: Partial<CustomerPropertyTableType> & PageRequestParamsType,
) => {
  return request<PageResponseDataType<ReturnsTableEntity>>(
    `/ipmsaftersale/AfterSaleQryFacade/getAfterSaleOrderPaged`,
    { data: params },
  );
};
