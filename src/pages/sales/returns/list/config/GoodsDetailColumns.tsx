import ColumnRender from '@/components/ColumnRender';
import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { multiply } from 'lodash';
import type { AfterSaleOrderGoodsRo } from '../../operation/types/ReturnsAfterSaleDetailEntity';

export default () => {
  const intl = useIntl();

  return [
    {
      title: intl.formatMessage({ id: 'sales.returns.list.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.productCode' }),
      dataIndex: 'itemSn',
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.productName' }),
      dataIndex: 'itemName',
      width: 140,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.oe' }),
      dataIndex: 'oeNos',
      width: 140,
      render: (_, entity) => ColumnRender.ArrayColumnRender(entity?.oeNos),
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.brandPartNo' }),
      dataIndex: 'brandPartNos',
      width: 100,
      render: (_, entity) => ColumnRender.ArrayColumnRender(entity?.brandPartNos),
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.brand' }),
      dataIndex: 'brandName',
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.category' }),
      dataIndex: 'categoryName',
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.unit' }),
      dataIndex: 'unitName',
      width: 50,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.list.orderNo' }),
      dataIndex: 'orgOrderNo',
      width: 160,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.refundAmount' }),
      dataIndex: 'unitAmount',
      valueType: 'money',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.saleQuantity' }),
      dataIndex: 'refundNum',
      width: 80,
      valueType: 'digit',
    },
    {
      title: intl.formatMessage({ id: 'sales.order.detail.subtotal' }),
      dataIndex: 'sum',
      width: 80,
      valueType: 'money',
      renderText(_, record) {
        return multiply(record.refundNum, record.unitAmount);
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.detail.returnReason' }),
      width: 140,
      ellipsis: true,
      dataIndex: 'cause',
    },
  ] as ProColumns<AfterSaleOrderGoodsRo>[];
};
