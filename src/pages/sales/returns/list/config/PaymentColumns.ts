import type { GoodsEntity } from '@/pages/goods/list/types/GoodsEntity.entity';
import type { ProColumns } from '@ant-design/pro-components';

/**
 * 支付流水
 */
export default [
  {
    title: '支付时间',
    dataIndex: 'oeNoList',
    valueType: 'dateTime',
  },
  {
    title: '支付账户',
    dataIndex: '12oeNoList',
    search: false,
  },
  {
    title: '支付金额',
    dataIndex: '12oeNoList',
    search: false,
    valueType: 'money',
  },
] as ProColumns<GoodsEntity>[];
