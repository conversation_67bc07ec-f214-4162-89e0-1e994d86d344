import AuthButton from '@/components/common/AuthButton';
import { getCstList } from '@/pages/customer/list/services';
import { MemberAccountEntity } from '@/pages/finance/customer/types/MemberAccountEntity';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { accountListQuerySimple, queryStoreByAccount } from '@/pages/system/user/services';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { Space } from 'antd';
import type { MutableRefObject } from 'react';
import type { ReturnsOrderType } from '../../operation/types/ReturnsOrderType';
import type { ReturnsTableEntity } from '../../operation/types/ReturnsTableEntity';
import { ReturnsOrderStatusValueEnum } from '../types/ReturnsOrderStatusValueEnum';
import { ReturnsRefundStatusValueEnum } from '../types/ReturnsRefundStatusValueEnum';
export default (
  openUndoModal: (params: ReturnsOrderType) => void,
  openCancelModal: (params: ReturnsOrderType) => void,
  formRef: MutableRefObject<ProFormInstance | undefined>,
  accountList: MemberAccountEntity[] | undefined
) => {
  const intl = useIntl();

  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      fixed: 'left',
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.returnOrderNo' }),
      dataIndex: 'orderNo',
      width: 150,
      order: 100,
      fixed: 'left',
      render: (orderNo, record) => {
        return (
          <a
            onClick={() => {
              history.push(
                `/sales/returns/detail?orderId=${record.orderId}&orderNo=${record.orderNo}`,
              );
            }}
          >
            {orderNo}
          </a>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.customerName' }),
      dataIndex: 'cstIds',
      width: 120,
      valueType: 'select',
      order: 75,
      hideInTable: true,
      search: {
        transform: (value) => {
          return { cstIds: [value] };
        },
      },
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'cstName', value: 'cstId' },
      },
      request: () => getCstList({}),
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.customer' }),
      dataIndex: 'cstName',
      width: 200,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.returnTime' }),
      dataIndex: 'orderCreateTime',
      width: 140,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.returnTime' }),
      dataIndex: 'orderCreateTime',
      valueType: 'dateRange',
      order: 20,
      hideInTable: true,
      search: {
        transform: (value: any) => {
          return {
            beginTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.orderStatus' }),
      dataIndex: 'orderStatus',
      width: 60,
      valueType: 'select',
      order: 75,
      hideInTable: true,
      search: {
        transform: (value) => {
          return { orderStatusList: [value] };
        },
      },
      valueEnum: ReturnsOrderStatusValueEnum,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.orderStatus' }),
      dataIndex: 'orderStatusName',
      width: 60,
      order: 90,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.returnStore' }),
      dataIndex: 'storeName',
      width: 120,
      hideInSearch: true,
      order: 80,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.returnStore' }),
      dataIndex: 'storeIds',
      width: 100,
      hideInTable: true,
      valueType: 'select',
      order: 80,
      search: {
        transform: (value) => {
          return { storeIds: [value] };
        },
      },
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
        onChange: () => {
          formRef.current?.setFieldValue('backWarehouseIds', undefined);
        },
      },
      request: async () => await queryStoreByAccount({ status: 1 }),
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.receiveWarehouse' }),
      dataIndex: 'backWarehouseName',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.receiveWarehouse' }),
      dataIndex: 'backWarehouseIds',
      hideInTable: true,
      order: 76,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'warehouseName', value: 'id' },
      },
      search: {
        transform: (value) => {
          return { backWarehouseIds: [value] };
        },
      },
      dependencies: ['storeIds'],
      request: ({ storeIds }) =>
        warehouseList({ storeIdList: storeIds }).then((t) => t.warehouseSimpleRoList),
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.refundAmount' }),
      dataIndex: 'orderAmount',
      search: false,
      valueType: 'money',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.settlementMethod' }),
      dataIndex: 'refundTypeName',
      search: false,
      width: 60,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.settlementStatus' }),
      dataIndex: 'refundStatus',
      width: 80,
      valueEnum: ReturnsRefundStatusValueEnum,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.settlementAccount' }),
      dataIndex: 'refundDetailList',
      width: 120,
      ellipsis: true,
      renderText: (refundDetailList) => {
        return refundDetailList?.map((item) => item.accountName).join(',');
      },
      transform: (value) => {
        return { accountIds: value }
      },
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        name: 'accountIds',
        options: (accountList ?? [])?.map(item => ({
          label: item.memberAccountName,
          value: item.id
        })),
      }
    },
    {
      dataIndex: 'keyword',
      search: true,
      hideInTable: true,
      fieldProps: {
        placeholder: intl.formatMessage({ id: 'sales.returns.list.productSearchPlaceholder' }),
      },
      formItemProps: {
        label: intl.formatMessage({ id: 'sales.returns.list.productInfo' }),
      },
      order: 35,
    },

    {
      title: intl.formatMessage({ id: 'sales.returns.list.creator' }),
      dataIndex: 'salesmanId',
      width: 60,
      order: 30,
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.creator' }),
      dataIndex: 'salesmanName',
      width: 60,
      order: 30,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.operation' }),
      valueType: 'option',
      fixed: 'right',
      width: 80,
      render: (_, record) => (
        <Space>
          <AuthButton
            isHref
            visible={record.orderStatus == 0}
            authority="editSaleReturn"
            key="editor"
            onClick={() => {
              history.push(`/sales/returns/operation`, {
                orderId: record.orderId,
                orderNo: record.orderNo,
                name: intl.formatMessage({ id: 'sales.returns.detail.editPageName' }),
              });
            }}
          >
            {intl.formatMessage({ id: 'sales.returns.list.edit' })}
          </AuthButton>
          <AuthButton
            key="undo"
            isHref
            authority="withdrawSaleReturn"
            visible={record.orderStatus === 10}
            onClick={() => {
              openUndoModal({ orderId: record.orderId, orderNo: record.orderNo });
            }}
          >
            {intl.formatMessage({ id: 'sales.returns.list.withdraw' })}
          </AuthButton>
          <AuthButton
            isHref
            authority="deleteSaleReturn"
            key="cancel"
            visible={record.orderStatus <= 10}
            onClick={() => {
              openCancelModal({ orderId: record.orderId, orderNo: record.orderNo });
            }}
          >
            {intl.formatMessage({ id: 'sales.returns.list.void' })}
          </AuthButton>
        </Space>
      ),
    },
  ] as ProColumns<ReturnsTableEntity>[];
};
