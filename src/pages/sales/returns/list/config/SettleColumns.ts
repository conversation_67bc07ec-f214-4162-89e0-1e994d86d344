import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { AfterSaleRefundDetailRo } from '../../operation/types/ReturnsAfterSaleDetailEntity';

/**
 * 结算记录columns
 */
export default () => {
  const intl = useIntl();

  return [
    {
      title: intl.formatMessage({ id: 'sales.returns.detail.settlementTime' }),
      dataIndex: 'refundTime',
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.list.settlementAccount' }),
      dataIndex: 'accountName',
    },
    {
      title: intl.formatMessage({ id: 'sales.returns.settlement.settlementAmount' }),
      dataIndex: 'refundAmount',
      valueType: 'money',
    },
  ] as ProColumns<AfterSaleRefundDetailRo>[];
};
