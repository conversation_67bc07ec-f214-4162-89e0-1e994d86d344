import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { AfterSaleOrderTimeRo } from '../../operation/types/ReturnsAfterSaleDetailEntity';

/**
 * 操作记录Columns
 */
export default () => {
  const intl = useIntl();

  return [
    {
      title: intl.formatMessage({ id: 'sales.order.detail.operationNode' }),
      dataIndex: 'timeTypeName',
    },
    {
      title: intl.formatMessage({ id: 'sales.order.detail.operationTime' }),
      dataIndex: 'time',
    },
    {
      title: intl.formatMessage({ id: 'sales.order.detail.operator' }),
      dataIndex: 'operatorName',
    },
  ] as ProColumns<AfterSaleOrderTimeRo>[];
};
