import Cookies from 'js-cookie';
import { checkRole, matchPerm, matchPermission } from './utils/PermissionUtils';
import { md5 } from './utils/md5';
/**
 * @see https://umijs.org/zh-CN/plugins/plugin-access
 * */
// TODO 未确定登录用户模型
export default function access(initialState: { currentUser?: any; buttonItem?: any } | undefined) {
  const { currentUser, buttonItem } = initialState ?? {};

  const hasPerms = (perm: string) => {
    return matchPermission(initialState?.currentUser?.permissions, perm);
  };
  const roleFiler = (route: { authority: string[] }) => {
    return checkRole(initialState?.currentUser?.roles, route.authority);
  };
  const hasButtonPerms = (perm: string) => {
    if (USE_REMOTE_MENU) {
      return matchPerm(buttonItem, perm);
    } else {
      return true;
    }
  };
  return {
    canAccess: (route?: { authority?: string }) => {
      console.log(route);
      if (!route?.authority) return true;
      if (initialState?.buttonItem && route?.authority) {
        return initialState?.buttonItem.includes(route?.authority);
      }
      return false;
    },
    hasPerms,
    roleFiler,
    hasButtonPerms,
  };
}

export function setSessionCookie(s_sessionId: string) {
  Cookies.set('s-session-id', s_sessionId, { expires: 365 * 24 * 60 * 60 });
}

export function getSeesionCookie() {
  return Cookies.get('s-session-id');
}

export function getTokenExpireTime() {
  return localStorage.getItem('expireTime');
}

export function clearCookies() {
  Cookies.remove('s-session-id');
  Cookies.remove('m-session-id');
  Cookies.remove('session-id');
  Cookies.remove('%24RM_signature');
}

export function getSign({
  token,
  appkey,
  channelName,
  params,
  timestamp,
}: {
  token?: string;
  appkey?: string;
  channelName?: string;
  params?: any;
  timestamp: number;
}) {
  const secret = Cookies.get('%24RM_signature');
  const sessionId = Cookies.get('session-id');
  const sSessionId = Cookies.get('s-session-id');
  const mSessionId = Cookies.get('m-session-id');

  let sign = secret;

  //按字母排序
  sign += `appkey${appkey}`;

  if (params) {
    sign += `data${JSON.stringify(params)}`;
  }

  if (mSessionId) {
    sign += `m-session-id${mSessionId}`;
  }
  if (sSessionId) {
    sign += `s-session-id${sSessionId}`;
  }
  if (sessionId) {
    sign += `session-id${sessionId}`;
  }

  let timeDifference = localStorage._RmTimeDifference || 0;

  sign += `timestamp${timestamp + parseInt(timeDifference)}`;

  // console.log('sign', sign);

  return md5(sign).toUpperCase();
}
